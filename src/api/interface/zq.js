import {formGet, formPost, jsonPost} from "@api/config/index.js";
// （客户经理）获取供应商列表
export const getCustomerSupplierList = () => new Promise(resolve => {
  jsonPost('/ps-ccms-core-front/v2/order/customerManager/supplierList', {}).then(res => {
    if (res.code === '0000') {
      resolve([null, res.data])
    } else {
      resolve([res, null])
    }
  }).catch(() => {
    resolve([{ code: -1, msg: '获取服务商失败' }, null])
  })
})

// （客户经理）确认收货
export const managerConfirmReceive = (supplierOrderId) => new Promise(resolve => {
  formPost('/ps-ccms-core-front/v2/order/developerConfirmRecv', { supplierOrderId }).then(res => {
    if (res.code === '0000') {
      resolve([null, res.data])
    } else {
      resolve([res, null])
    }
  }).catch(() => {
    resolve([{ code: -1, msg: '确认收货失败' }, null])
  })
})

/**
 * 政企查询分类、分区商品
 * @param {Object} params - 请求参数
 * @param {String} params.type - 查询类型：1-分区，2-分类
 * @param {String} params.bizCode - 业务代码，固定为'zq'
 * @param {String} params.id - 分区id或分类id
 * @param {String} params.supplierCode - 服务商编码
 * @param {String} [params.sort] - 排序方式：UP-升序，DOWN-降序
 * @param {Number} params.pageNo - 页码，从1开始
 * @param {Number} params.pageSize - 每页记录数
 * @returns {Promise<Array>} - 返回[error, data]格式的Promise
 */
export const zqQuerySimplified = (params) => new Promise(resolve => {
  formPost('/ps-ccms-core-front/v1/BGoods/pagelist/simplified', params).then(res => {
    if (res.code === '0000') {
      resolve([null, res.data])
    } else {
      resolve([res, null])
    }
  })
})

// 提交订单下单
export const submitOrder = data => new Promise(resolve => {
  formPost('/ps-ccms-core-front/v2/order/create/common', data).then(res => {
    if (res.code === '0000') {
      resolve([null, res.data])
    } else {
      resolve([res, null])
    }
  }).catch(() => {
    resolve([{ code: -1, msg: '下单失败' }, null])
  })
})


export const zqAllSupplierList = () => new Promise(resolve => {
  formGet('/ps-ccms-core-front/v2/order/zq/allSupplierList').then(res => {
    if (res.code === '0000') {
      resolve([null, res.data])
    } else {
      resolve([res, null])
    }
  })
})
