/* ======== CSS变量主题系统 ======== */
/* 使用CSS变量替代LESS变量，支持动态主题切换 */
/* 所有变量都使用 --wo-biz- 前缀 */

/* ======== 京东红主题 ======== */
html.theme-jd {
  /* 主题色定义 */
  --wo-biz-theme-color: #FF2F2F; /* 京东红主色 */
  --wo-biz-theme-color-light: #ff5252; /* 浅色主题色 */
  --wo-biz-theme-color-dark: #e02626; /* 深色主题色 */
  --wo-biz-theme-color-lighter: #ff6b6b; /* 更浅主题色 */
  --wo-biz-theme-color-darker: #c21e1e; /* 更深主题色 */

  /* 主题渐变色 */
  --wo-biz-theme-gradient-1: linear-gradient(106deg, #FF5555 0%, #FF2F2F 100%); /* 按钮渐变色 106度 */
  --wo-biz-theme-gradient-2: linear-gradient(115deg, #FF5555 0%, #FF2F2F 100%); /* 搜索按钮渐变色 115度 */
  --wo-biz-theme-gradient-3: linear-gradient(106deg, #e04848 0%, #c21e1e 100%); /* 按钮激活状态渐变色 */
  --wo-biz-theme-gradient-4: linear-gradient(135deg, #FF2F2F 0%, #e02626 100%); /* 卡片渐变背景 */
  --wo-biz-theme-gradient-5: linear-gradient(90deg, rgba(255, 47, 47, 0.1) 0%, rgba(255, 47, 47, 0.3) 100%); /* 轻微渐变背景 */
  --wo-biz-theme-gradient-6: linear-gradient(90deg, #F53E4A 0%, #FA2D19 100%); /* 轻微渐变背景 */
  --wo-biz-theme-gradient-7: linear-gradient(90deg, #F53E4A 0%, #FA2D19 100%); /* 轻微渐变背景 */

  /* 主题相关颜色 */
  --wo-biz-theme-bg-1: #fff5f5; /* 主题背景色-1 (浅红背景) */
  --wo-biz-theme-bg-2: #fecaca; /* 主题背景色-2 (中红背景) */
  --wo-biz-theme-bg-3: rgba(255, 47, 47, 0.1); /* 主题背景色-3 (10%透明度) */
  --wo-biz-theme-bg-4: rgba(255, 47, 47, 0.2); /* 主题背景色-4 (20%透明度) */
  --wo-biz-theme-bg-5: rgba(255, 47, 47, 0.05); /* 主题背景色-5 (5%透明度) */

  --wo-biz-theme-border-1: #fecaca; /* 主题边框色-1 */
  --wo-biz-theme-border-2: rgba(255, 47, 47, 0.3); /* 主题边框色-2 */
  --wo-biz-theme-border-3: rgba(255, 47, 47, 0.5); /* 主题边框色-3 */

  --wo-biz-theme-text-1: #FF2F2F; /* 主题文字色-1 */
  --wo-biz-theme-text-2: #e02626; /* 主题文字色-2 */
  --wo-biz-theme-text-3: #ff5252; /* 主题文字色-3 */

  /* 状态颜色 */
  --wo-biz-theme-hover: rgba(255, 47, 47, 0.8); /* 悬停状态 */
  --wo-biz-theme-active: #e02626; /* 激活状态 */
  --wo-biz-theme-focus: rgba(255, 47, 47, 0.2); /* 聚焦状态 */
  --wo-biz-theme-disabled: rgba(255, 47, 47, 0.3); /* 禁用状态 */
}

/* ======== 沃橙色主题 ======== */
.theme-wo {
  /* 主题色定义 */
  --wo-biz-theme-color: #FF7A0A; /* 沃橙色/品牌橙主色 */
  --wo-biz-theme-color-light: #ff9433; /* 浅色主题色 */
  --wo-biz-theme-color-dark: #e06600; /* 深色主题色 */
  --wo-biz-theme-color-lighter: #ffad5c; /* 更浅主题色 */
  --wo-biz-theme-color-darker: #c25200; /* 更深主题色 */

  /* 主题渐变色 */
  --wo-biz-theme-gradient-1: linear-gradient(106deg, #FFA033 0%, #FF6D33 100%); /* 按钮渐变色 106度 */
  --wo-biz-theme-gradient-2: linear-gradient(115deg, #FFA033 0%, #FF6D33 100%); /* 搜索按钮渐变色 115度 */
  --wo-biz-theme-gradient-3: linear-gradient(106deg, #e0902e 0%, #e05e2e 100%); /* 按钮激活状态渐变色 */
  --wo-biz-theme-gradient-4: linear-gradient(135deg, #FF7A0A 0%, #e06600 100%); /* 卡片渐变背景 */
  --wo-biz-theme-gradient-5: linear-gradient(90deg, rgba(255, 122, 10, 0.1) 0%, rgba(255, 122, 10, 0.3) 100%); /* 轻微渐变背景 */
  --wo-biz-theme-gradient-6: linear-gradient(90deg, #FFC72D 0%, #FFAD1B 100%); /* 轻微渐变背景 */
  --wo-biz-theme-gradient-7: linear-gradient(101deg, #FFA033 0%, #FF6D33 100%); /* 轻微渐变背景 */

  /* 主题相关颜色 */
  --wo-biz-theme-bg-1: #fff7f0; /* 主题背景色-1 (浅橙背景) */
  --wo-biz-theme-bg-2: #ffe4d1; /* 主题背景色-2 (中橙背景) */
  --wo-biz-theme-bg-3: rgba(255, 122, 10, 0.1); /* 主题背景色-3 (10%透明度) */
  --wo-biz-theme-bg-4: rgba(255, 122, 10, 0.2); /* 主题背景色-4 (20%透明度) */
  --wo-biz-theme-bg-5: rgba(255, 122, 10, 0.05); /* 主题背景色-5 (5%透明度) */

  --wo-biz-theme-border-1: #ffe4d1; /* 主题边框色-1 */
  --wo-biz-theme-border-2: rgba(255, 122, 10, 0.3); /* 主题边框色-2 */
  --wo-biz-theme-border-3: rgba(255, 122, 10, 0.5); /* 主题边框色-3 */

  --wo-biz-theme-text-1: #FF7A0A; /* 主题文字色-1 */
  --wo-biz-theme-text-2: #e06600; /* 主题文字色-2 */
  --wo-biz-theme-text-3: #ff9433; /* 主题文字色-3 */

  /* 状态颜色 */
  --wo-biz-theme-hover: rgba(255, 122, 10, 0.8); /* 悬停状态 */
  --wo-biz-theme-active: #e06600; /* 激活状态 */
  --wo-biz-theme-focus: rgba(255, 122, 10, 0.2); /* 聚焦状态 */
  --wo-biz-theme-disabled: rgba(255, 122, 10, 0.3); /* 禁用状态 */
}

/* ======== 默认主题设置 ======== */
/* 如果没有指定主题类，默认使用沃橙色主题 */
:root {
  /* 主题色定义 */
  --wo-biz-theme-color: #FF7A0A; /* 沃橙色/品牌橙主色 */
  --wo-biz-theme-color-light: #ff9433; /* 浅色主题色 */
  --wo-biz-theme-color-dark: #e06600; /* 深色主题色 */
  --wo-biz-theme-color-lighter: #ffad5c; /* 更浅主题色 */
  --wo-biz-theme-color-darker: #c25200; /* 更深主题色 */

  /* 主题渐变色 */
  --wo-biz-theme-gradient-1: linear-gradient(106deg, #FFA033 0%, #FF6D33 100%); /* 按钮渐变色 106度 */
  --wo-biz-theme-gradient-2: linear-gradient(115deg, #FFA033 0%, #FF6D33 100%); /* 搜索按钮渐变色 115度 */
  --wo-biz-theme-gradient-3: linear-gradient(106deg, #e0902e 0%, #e05e2e 100%); /* 按钮激活状态渐变色 */
  --wo-biz-theme-gradient-4: linear-gradient(135deg, #FF7A0A 0%, #e06600 100%); /* 卡片渐变背景 */
  --wo-biz-theme-gradient-5: linear-gradient(90deg, rgba(255, 122, 10, 0.1) 0%, rgba(255, 122, 10, 0.3) 100%); /* 轻微渐变背景 */
  --wo-biz-theme-gradient-6: linear-gradient(90deg, #FFC72D 0%, #FFAD1B 100%); /* 轻微渐变背景 */
  --wo-biz-theme-gradient-7: linear-gradient(101deg, #FFA033 0%, #FF6D33 100%); /* 轻微渐变背景 */

  /* 主题相关颜色 */
  --wo-biz-theme-bg-1: #fff7f0; /* 主题背景色-1 (浅橙背景) */
  --wo-biz-theme-bg-2: #ffe4d1; /* 主题背景色-2 (中橙背景) */
  --wo-biz-theme-bg-3: rgba(255, 122, 10, 0.1); /* 主题背景色-3 (10%透明度) */
  --wo-biz-theme-bg-4: rgba(255, 122, 10, 0.2); /* 主题背景色-4 (20%透明度) */
  --wo-biz-theme-bg-5: rgba(255, 122, 10, 0.05); /* 主题背景色-5 (5%透明度) */

  --wo-biz-theme-border-1: #ffe4d1; /* 主题边框色-1 */
  --wo-biz-theme-border-2: rgba(255, 122, 10, 0.3); /* 主题边框色-2 */
  --wo-biz-theme-border-3: rgba(255, 122, 10, 0.5); /* 主题边框色-3 */

  --wo-biz-theme-text-1: #FF7A0A; /* 主题文字色-1 */
  --wo-biz-theme-text-2: #e06600; /* 主题文字色-2 */
  --wo-biz-theme-text-3: #ff9433; /* 主题文字色-3 */

  /* 状态颜色 */
  --wo-biz-theme-hover: rgba(255, 122, 10, 0.8); /* 悬停状态 */
  --wo-biz-theme-active: #e06600; /* 激活状态 */
  --wo-biz-theme-focus: rgba(255, 122, 10, 0.2); /* 聚焦状态 */
  --wo-biz-theme-disabled: rgba(255, 122, 10, 0.3); /* 禁用状态 */
}
