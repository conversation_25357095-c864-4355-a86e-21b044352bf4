export const baseRoutes = [
  {
    path: '/zq/entry',
    name: 'zq-entry',
    meta: { login: true },
    component: () => import('@views/ZQMall/EntryView.vue')
  },
  {
    path: '/zq/orderresult',
    name: 'zq-order-result',
    meta: { login: true },
    component: () => import('@/views/ZQMall/EnterpriseManager/Order/OrderResult/OrderResult.vue')
  },
  {
    path: '/zq/goodsdetail/:goodsId/:skuId?',
    name: 'zq-goods-detail',
    component: () => import('@views/ZQMall/EnterpriseManager/Goods/GoodsDetail.vue')
  },
]
