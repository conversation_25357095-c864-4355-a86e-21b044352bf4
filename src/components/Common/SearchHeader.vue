<!--
搜索头部组件 - SearchHeader

功能描述：
- 提供搜索输入框和搜索按钮的组合界面
- 支持双向数据绑定和实时搜索
- 支持防抖搜索优化性能
- 支持跳转到搜索页面或直接搜索两种模式
- 提供搜索图标和右侧操作区的插槽扩展
- 支持键盘回车搜索和点击搜索
- 暴露输入框引用和聚焦/失焦方法
-->

<template>
  <!-- 搜索头部容器 - 包含搜索输入框和相关操作 -->
  <header class="search-header">
    <!-- 搜索输入框包装器 - 包含图标、输入框和搜索按钮 -->
    <div class="search-header__input-wrapper" @click="handleInputClick">
      <!-- 搜索图标插槽 - 允许自定义搜索图标 -->
      <slot name="search-icon">
        <!-- 默认搜索图标 - 使用懒加载优化性能 -->
        <img
          src="@/static/images/search.png"
          alt="搜索"
          class="search-header__icon"
          loading="lazy"
        />
      </slot>
      <!-- 搜索输入框 - 支持双向绑定和键盘事件 -->
      <input
        ref="inputRef"
        v-model="keyword"
        type="text"
        class="search-header__input"
        :placeholder="placeholder"
        :readonly="redirectToSearch"
        @keyup.enter="debouncedSearch"
      />
      <!-- 搜索按钮 - 触发搜索操作 -->
      <button
        type="button"
        class="search-header__button"
        @click.stop="debouncedSearch"
      >
        搜索
      </button>
    </div>
    <!-- 右侧操作区插槽 - 允许添加额外的操作按钮或内容 -->
    <slot name="right-action" />
  </header>
</template>

<script setup>
import { ref, watch, toRefs } from 'vue'
import { useRouter } from 'vue-router'
import { debounce } from 'lodash-es'

// ===================== 路由实例 ======================
// Vue Router实例，用于页面跳转
const router = useRouter()

// ===================== 组件属性定义 ======================
// 定义组件接收的props参数
const props = defineProps({
  // 双向绑定的搜索关键词值
  modelValue: {
    type: String,
    default: ''
  },
  // 输入框占位符文本
  placeholder: {
    type: String,
    default: '搜索'
  },
  // 是否点击输入框时跳转到搜索页面
  redirectToSearch: {
    type: Boolean,
    default: false
  },
  // 跳转的搜索页面URL
  redirectUrl: {
    type: String,
    default: '/search'
  }
})

// 使用toRefs解构props，保持响应性
const { modelValue, placeholder, redirectToSearch, redirectUrl } = toRefs(props)

// ===================== 事件定义 ======================
// 定义组件向外发射的事件
const emit = defineEmits(['update:modelValue', 'search', 'clickable'])

// ===================== 响应式数据 ======================
// 内部搜索关键词状态
const keyword = ref(modelValue.value)
// 输入框DOM引用
const inputRef = ref(null)

// ===================== 数据监听器 ======================
// 监听外部modelValue变化，同步到内部keyword
watch(modelValue, (newVal) => {
  keyword.value = newVal
}, { immediate: true })

// 监听内部keyword变化，向外发射更新事件实现双向绑定
watch(keyword, (newVal) => {
  emit('update:modelValue', newVal)
})

// ===================== 搜索功能 ======================
// 防抖搜索处理函数 - 避免频繁触发搜索，优化性能
const debouncedSearch = debounce(() => {
  // 去除关键词首尾空格
  const trimmedKeyword = keyword.value?.trim()
  // 只有在关键词不为空时才触发搜索
  if (trimmedKeyword) {
    emit('search', trimmedKeyword)
  }
}, 300)

// ===================== 事件处理函数 ======================
// 处理输入框点击事件 - 根据配置决定是跳转还是聚焦
const handleInputClick = () => {
  if (redirectToSearch.value) {
    // 跳转模式：跳转到指定的搜索页面
    router.push(redirectUrl.value)
  } else {
    // 直接搜索模式：聚焦输入框
    inputRef.value?.focus()
  }
  // 触发点击事件，供外部组件监听
  emit('clickable')
}

// ===================== 组件暴露接口 ======================
// 暴露给父组件的方法和属性，供外部直接调用
defineExpose({
  // 输入框DOM引用
  inputRef,
  // 聚焦输入框方法
  focus: () => inputRef.value?.focus(),
  // 失焦输入框方法
  blur: () => inputRef.value?.blur()
})
</script>

<style scoped lang="less">
.search-header {
  display: flex;
  align-items: center;
  padding: 10px;
  background-color: #FFFFFF;
  box-sizing: border-box;

  &__input-wrapper {
    flex: 1;
    display: flex;
    align-items: center;
    height: 32px;
    background-color: #FFFFFF;
    border-radius: 30px;
    padding: 3px 3px 3px 11px;
    box-sizing: border-box;
    border: 1px solid #E2E8EE;
    transition: border-color 0.2s ease;

    //&:focus-within {
    //  border-color: var(--wo-biz-theme-color);
    //}
  }

  &__icon {
    width: 13px;
    height: 13px;
    margin-right: 6px;
    flex-shrink: 0;
    image-rendering: -webkit-optimize-contrast;
  }

  &__input {
    flex: 1;
    border: none;
    background: transparent;
    font-size: 13px;
    color: #171E24;
    outline: none;
    min-width: 0;

    &::placeholder {
      color: #718096;
    }

    &::-webkit-input-placeholder {
      color: #718096;
    }

    &::-moz-placeholder {
      color: #718096;
    }
  }

  &__button {
    width: 50px;
    height: 26px;
    background-image: var(--wo-biz-theme-gradient-2);
    border-radius: 15px;
    font-size: 13px;
    font-weight: 500;
    text-align: center;
    color: #FFFFFF;
    border: none;
    cursor: pointer;
    flex-shrink: 0;
    transition: opacity 0.2s ease;

    &:active {
      opacity: 0.8;
      transform: scale(0.98);
    }

    &:disabled {
      opacity: 0.5;
      cursor: not-allowed;
    }
  }
}
</style>
