<!--
/**
 * 地址快速编辑弹窗组件 - AddressQuickEditPopup
 * 
 * 功能描述：
 * 用于快速编辑现有地址信息的弹窗组件，提供便捷的地址修改功能。
 * 组件封装了地址表单，支持地址信息的编辑、验证和保存操作。
 * 
 * 主要功能：
 * 1. 弹窗展示 - 以底部弹窗形式展示地址编辑界面
 * 2. 地址编辑 - 集成地址表单组件，支持完整的地址信息编辑
 * 3. 表单验证 - 实时验证表单数据的有效性
 * 4. 数据传递 - 支持初始数据传入和编辑结果回传
 * 5. 事件代理 - 代理地址表单的各种事件到父组件
 * 
 * 技术特点：
 * - 使用 Vue 3 Composition API 和 <script setup> 语法
 * - 基于 Vant UI 的弹窗组件实现
 * - 响应式数据处理，支持双向绑定
 * - 组件引用暴露，支持外部调用表单方法
 * - 事件代理机制，保持组件间的松耦合
 */
-->

<template>
  <!-- 地址编辑弹窗容器 - 底部弹出，占据屏幕80%高度，支持圆角和关闭事件 -->
  <van-popup v-model:show="visible" round position="bottom" :style="{ height: '80%' }" @close="handleClose">
    <!-- 弹窗内容主容器 - 包含头部、内容区和操作栏 -->
    <div class="address-edit-popup">
      <!-- 弹窗头部 - 包含标题和关闭按钮 -->
      <div class="popup-header">
        <!-- 弹窗标题 - 明确告知用户当前操作 -->
        <h3 class="title">编辑原地址</h3>
        <!-- 关闭按钮 - 点击关闭弹窗 -->
        <div class="close-btn" @click="handleClose">×</div>
      </div>
      
      <!-- 弹窗内容区 - 包含地址表单组件 -->
      <div class="popup-content">
        <!-- 地址表单组件 - 传入初始数据，监听验证和地区变化事件 -->
        <AddressForm
          ref="addressFormRef"
          :initial-data="initialData"
          @validate="onFormValidate"
          @region-change="onRegionChange"
        />
      </div>
      
      <!-- 底部操作栏 - 包含保存按钮 -->
      <WoActionBar class="action-bar">
        <!-- 保存按钮 - 触发保存操作 -->
        <WoButton type="primary" block size="xlarge" @click="handleSave">保存</WoButton>
      </WoActionBar>
    </div>
  </van-popup>
</template>

<script setup>
// ===================== 核心依赖导入 =======================
import { ref, toRefs, defineProps, defineEmits } from 'vue'
import WoButton from '@components/WoElementCom/WoButton/WoButton.vue'
import WoActionBar from '@components/WoElementCom/WoActionBar.vue'
import AddressForm from '@components/Common/Address/AddressForm.vue'

// ===================== 组件属性定义 =======================
// 定义组件接收的属性
const props = defineProps({
  // 弹窗显示状态 - 控制弹窗的显示和隐藏
  visible: {
    type: Boolean,
    default: false
  },
  // 初始地址数据 - 用于填充表单的默认值
  initialData: {
    type: Object,
    default: () => ({})
  }
})

// 定义组件向外发射的事件
const emit = defineEmits([
  'close',           // 弹窗关闭事件
  'save',            // 保存地址事件
  'validate',        // 表单验证事件
  'region-change',   // 地区变化事件
  'update:visible'   // 更新显示状态事件（用于 v-model）
])

// ===================== 响应式数据处理 =======================
// 使用 toRefs 解构 props，保持响应性
const { visible, initialData } = toRefs(props)

// ===================== 组件引用管理 =======================
// 地址表单组件的引用 - 用于调用表单的方法和获取表单数据
const addressFormRef = ref(null)

// ===================== 表单事件代理处理 =======================
// 处理表单验证事件 - 将表单验证结果代理到父组件
const onFormValidate = ({ isValid }) => {
  // 向父组件发射验证事件，传递验证状态
  emit('validate', { isValid })
}

// 处理地区变化事件 - 将地区选择结果代理到父组件
const onRegionChange = (regionData) => {
  // 向父组件发射地区变化事件，传递地区数据
  emit('region-change', regionData)
}

// ===================== 弹窗操作处理 =======================
// 处理弹窗关闭操作 - 更新显示状态并发射关闭事件
const handleClose = () => {
  // 更新 visible 状态为 false（用于 v-model 双向绑定）
  emit('update:visible', false)
  // 发射关闭事件，通知父组件弹窗已关闭
  emit('close')
}

// 处理保存操作 - 将表单引用传递给父组件进行保存处理
const handleSave = () => {
  // 向父组件发射保存事件，传递表单组件引用
  emit('save', addressFormRef.value)
}

// ===================== 组件暴露接口 =======================
// 向父组件暴露表单引用，允许外部直接调用表单方法
defineExpose({
  addressFormRef
})
</script>

<style scoped lang="less">
.address-edit-popup {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #FFFFFF;

  .popup-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 20px;
    box-sizing: border-box;

    .title {
      font-size: 17px;
      font-weight: 600;
      color: #171E24;
      margin: 0;
    }

    .close-btn {
      font-size: 24px;
      color: #718096;
      cursor: pointer;
      width: 24px;
      height: 24px;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }

  .popup-content {
    flex: 1;
    padding: 10px;
    overflow-y: auto;
    box-sizing: border-box;
  }


}
</style>
