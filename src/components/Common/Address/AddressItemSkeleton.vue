<!--
/**
 * 地址项骨架屏组件 - AddressItemSkeleton
 * 
 * 功能描述：
 * 用于在地址列表加载过程中显示的占位符组件，提供视觉上的加载反馈。
 * 模拟真实地址卡片的布局结构，通过动画效果提升用户体验。
 * 
 * 主要功能：
 * 1. 加载占位 - 在真实数据加载期间显示结构化的占位内容
 * 2. 视觉反馈 - 通过动画效果告知用户内容正在加载中
 * 3. 布局保持 - 保持与真实地址卡片相同的布局结构，避免页面跳动
 * 4. 性能优化 - 使用 CSS 动画和 contain 属性优化渲染性能
 * 
 * 技术特点：
 * - 纯 CSS 实现的骨架屏效果，无需 JavaScript 逻辑
 * - 使用线性渐变和关键帧动画创建流动效果
 * - 采用 contain 属性优化渲染性能
 * - 响应式设计，适配不同屏幕尺寸
 * - 语义化 HTML 结构，保持良好的可访问性
 */
-->

<template>
  <!-- 地址骨架屏容器 - 模拟真实地址卡片的整体结构 -->
  <article class="address-skeleton">
    <!-- 骨架屏头部 - 模拟联系人信息区域 -->
    <header class="address-skeleton__header">
      <!-- 联系人信息骨架屏容器 - 包含姓名和电话占位符 -->
      <div class="contact-info-skeleton">
        <!-- 收件人姓名占位符 - 模拟姓名文本的宽度和高度 -->
        <div class="skeleton-line skeleton-name"></div>
        <!-- 联系电话占位符 - 模拟电话号码文本的宽度和高度 -->
        <div class="skeleton-line skeleton-phone"></div>
      </div>
    </header>

    <!-- 骨架屏内容区 - 模拟地址详情区域 -->
    <div class="address-skeleton__content">
      <!-- 地址第一行占位符 - 模拟省市区信息的显示 -->
      <div class="skeleton-line skeleton-address"></div>
      <!-- 地址第二行占位符 - 模拟详细地址信息的显示 -->
      <div class="skeleton-line skeleton-address-detail"></div>
    </div>

    <!-- 骨架屏底部操作区 - 模拟操作按钮区域 -->
    <footer class="address-skeleton__actions">
      <!-- 第一个操作按钮占位符 - 模拟编辑按钮 -->
      <div class="skeleton-line skeleton-btn"></div>
      <!-- 第二个操作按钮占位符 - 模拟删除按钮 -->
      <div class="skeleton-line skeleton-btn"></div>
    </footer>
  </article>
</template>

<script setup>
// ===================== 组件逻辑说明 =======================
// 骨架屏组件为纯展示组件，不包含任何业务逻辑
// 所有的视觉效果通过 CSS 动画实现，无需 JavaScript 处理
// 组件专注于提供加载状态的视觉反馈，提升用户体验
</script>

<style scoped lang="less">
.address-skeleton {
  padding: 14px;
  background-color: #FFFFFF;
  border-radius: 4px;
  margin-bottom: 8px;
  contain: layout style paint;

  &__header {
    margin-bottom: 12px;
  }

  &__content {
    margin-bottom: 16px;
  }

  &__actions {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
  }
}

.contact-info-skeleton {
  display: flex;
  align-items: center;
  gap: 10px;
}

.skeleton-line {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.2s ease-in-out infinite;
  border-radius: 4px;
  will-change: background-position;
}

.skeleton-name {
  width: 60px;
  height: 16px;
}

.skeleton-phone {
  width: 100px;
  height: 16px;
}

.skeleton-address {
  width: 100%;
  height: 14px;
  margin-bottom: 6px;
}

.skeleton-address-detail {
  width: 80%;
  height: 14px;
}

.skeleton-btn {
  width: 40px;
  height: 24px;
  border-radius: 4px;
}

@keyframes skeleton-loading {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}
</style>
