<!--
/**
 * 浮动气泡组件
 *
 * 主要功能：
 * 1. 提供回到顶部功能，当页面滚动超过300px时自动显示回顶按钮
 * 2. 集成购物车快捷入口，显示购物车商品数量徽章
 * 3. 支持磁性吸附效果，自动吸附到屏幕边缘
 * 4. 根据业务类型动态切换购物车图标（沃一购/京东一购）
 * 5. 仅在用户登录状态下显示购物车数量
 *
 * 技术特点：
 * - 使用van-floating-bubble组件实现浮动效果
 * - 集成lodash防抖优化滚动性能
 * - 响应式监听滚动事件，自动显示/隐藏回顶按钮
 * - 集成Pinia状态管理，实时同步购物车数量
 *
 * 使用场景：
 * - 商品列表页面提供快速回顶和购物车访问
 * - 商品详情页面的便捷操作入口
 */
-->

<template>
  <!-- 浮动气泡容器，支持磁性吸附到x轴边缘 -->
  <van-floating-bubble class="custom-back-top" magnetic="x" v-model:offset="offset">
    <template #default>
      <!-- 气泡内容容器，垂直排列多个功能按钮 -->
      <div class="floating-bubble-content">
        <!-- 回到顶部按钮，仅在滚动超过300px时显示 -->
        <div class="bubble-item" v-if="showBackTop">
          <img v-show="showBackTop" src="../../static/images/go-top.png" alt="回到顶部" @click="scrollToTop" />
        </div>
        <!-- 购物车按钮，显示商品数量徽章 -->
        <div class="bubble-item" v-if="isShowCart">
          <!-- 购物车徽章，显示商品数量，最大显示99+ -->
          <van-badge :content="cartCount" :show-zero="false" :max="99" class="cart-badge">
            <!-- 购物车图标，根据业务类型动态切换 -->
            <img :src="cartIcon" alt="购物车" @click="goToCart" />
          </van-badge>
        </div>
      </div>
    </template>
  </van-floating-bubble>
</template>

<script setup>
import { ref, onMounted, onUnmounted, toRefs, computed } from 'vue'
import { throttle } from 'lodash-es'
import { useNewCartStore } from '@/store/modules/newCart'
import { useUserStore } from '@/store/modules/user'
import { getBizCode } from '@utils/curEnv.js'
import woOneCart from '@/static/images/wo-one-cart.png'
import jdOneCart from '@/static/images/jd-one-cart.png'

// ===================== 组件属性定义 ======================
// 定义组件接收的props参数
const props = defineProps({
  // 浮动气泡的位置偏移配置，默认距离底部150px
  offset: {
    type: Object,
    default: () => ({ bottom: 150 })
  },
  // 是否显示购物车按钮，默认显示
  isShowCart: {
    type: Boolean,
    default: true
  },
  // 徽章内容（已废弃，保留兼容性）
  badgeContent: {
    type: [String, Number],
    default: ''
  }
})

// 使用toRefs解构props，保持响应性
const { offset, isShowCart } = toRefs(props)

// 定义组件向外发射的事件
const emit = defineEmits(['go-to-cart'])

// ==================== 状态管理集成 ====================
// 购物车状态管理store，用于获取购物车商品数量
const cartStore = useNewCartStore()
// 用户状态管理store，用于判断登录状态
const userStore = useUserStore()

// ==================== 购物车功能相关 ====================
// 计算属性：根据业务类型动态切换购物车图标
// 京东一购使用京东图标，沃一购使用沃图标
const cartIcon = computed(() => (getBizCode() === 'ygjd' ? jdOneCart : woOneCart))

// 计算属性：获取购物车商品数量
// 仅在用户登录状态下显示真实数量，未登录时返回0
const cartCount = computed(() => {
  if (!userStore.isLogin) return 0
  return cartStore.countByGoods
})

// 购物车点击处理函数
// 触发父组件的go-to-cart事件，由父组件处理跳转逻辑
const goToCart = () => {
  emit('go-to-cart')
}

// ==================== 回到顶部功能相关 ====================
// 控制回到顶部按钮显示状态的响应式变量
const showBackTop = ref(false)

// 滚动事件处理函数，使用throttle优化性能
// 当页面滚动超过300px时显示回顶按钮
const handleScroll = throttle(() => {
  const scrollTop = document.documentElement.scrollTop || document.body.scrollTop
  showBackTop.value = scrollTop > 300
}, 100)

// 回到顶部处理函数
// 使用smooth滚动效果平滑回到页面顶部
const scrollToTop = () => {
  window.scrollTo({
    top: 0,
    behavior: 'smooth'
  })
}

// ==================== 生命周期管理 ====================
// 组件挂载时添加滚动事件监听器
// 使用passive选项优化滚动性能
onMounted(() => {
  window.addEventListener('scroll', handleScroll, { passive: true })
})

// 组件卸载时移除滚动事件监听器，防止内存泄漏
onUnmounted(() => {
  window.removeEventListener('scroll', handleScroll)
})
</script>

<style scoped lang="less">
.custom-back-top {
  :deep(.van-back-top__icon) {
    display: none;
  }

  .floating-bubble-content {
    max-height: 100px;
    display: flex;
    flex-direction: column;
    align-items: center;
    background-color: transparent;
  }

  .bubble-item {
    display: flex;
    justify-content: center;
    align-items: center;
    width: 50px;
    height: 50px;
    cursor: pointer;

    img {
      width: 44px;
      height: 44px;
    }

    .cart-badge {
      position: relative;

      :deep(.van-badge) {
        position: absolute;
        top: 5px;
        right: 9px;
        background: var(--wo-biz-theme-color);
        border: 2px solid #FFFFFF;
      }
    }
  }
}
</style>
