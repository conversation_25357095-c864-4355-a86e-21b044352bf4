<!--
/**
 * 赠品展示弹窗组件
 *
 * 功能描述：
 * - 以底部弹窗形式展示商品的赠品明细信息
 * - 支持根据购买数量动态计算赠品数量
 * - 显示赠品获得条件和可获得数量
 * - 提供关闭弹窗功能
 *
 * 主要特性：
 * - 响应式设计，适配移动端
 * - 支持滚动查看多个赠品
 * - 动态图片加载（京东CDN或自定义URL）
 * - 赠品数量智能计算
 */
-->
<template>
  <!-- 底部弹窗容器，占屏幕高度80%，支持圆角和关闭事件 -->
  <van-popup
    v-model:show="visible"
    round
    position="bottom"
    :style="{ height: '80%', maxHeight: '80vh' }"
    @close="handleClose"
  >
    <!-- 赠品展示弹窗主容器 -->
    <div class="gift-display-popup">
      <!-- 弹窗头部区域：包含标题和关闭按钮 -->
      <div class="popup-header">
        <!-- 弹窗标题 -->
        <h3 class="title">商品明细</h3>
        <!-- 关闭按钮：点击关闭弹窗 -->
        <div class="close-btn" @click="handleClose">
          <img src="@/static/images/close.png" alt="关闭" />
        </div>
      </div>

      <!-- 赠品提示信息区域：显示重要提醒文案 -->
      <div class="gift-tips">
        <span class="tips-text">商品数量有限，送完为止，请勿虚假购买</span>
      </div>

      <!-- 赠品列表区域：可滚动的赠品展示列表 -->
      <div class="gift-list" ref="giftListRef">
        <!-- 单个赠品项：循环渲染每个赠品信息 -->
        <div v-for="item in giftList" :key="item.id" class="gift-item">
          <!-- 赠品图片区域：支持京东CDN和自定义图片URL -->
          <div class="gift-image">
            <img
              :src="item.imagePath ? 'https://img13.360buyimg.com/n4/' + item.imagePath : item.listImageUrl"
              alt="赠品图片"
            >
          </div>
          <!-- 赠品信息区域：包含名称、条件和数量 -->
          <div class="gift-info">
            <!-- 赠品信息左侧：名称和获得条件 -->
            <div class="gift-info-left">
              <!-- 赠品名称：优先显示skuName，否则显示name -->
              <div class="gift-name">{{ item.skuName || item.name }}</div>
              <!-- 赠品获得条件：仅在有最小购买数量要求时显示 -->
              <div
                class="gift-condition"
                v-if="isGiftsAvailableIf(item.belongToSkuMinNum)"
              >
                条件：{{ item.condition }}
              </div>
            </div>
            <!-- 赠品信息右侧：可获得的赠品数量 -->
            <div class="gift-info-right">
              <!-- 赠品数量：仅在满足获得条件时显示 -->
              <div
                v-if="isGiftsAvailable(item.belongToSkuMinNum)"
                class="quantity"
              >
                x{{ giftNum(item.belongToSkuMaxNum, item.belongToSkuMinNum, item.giftNum) }}
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 底部操作栏：包含确定按钮 -->
      <WoActionBar class="action-bar">
        <!-- 确定按钮：点击关闭弹窗 -->
        <WoButton type="primary" block @click="handleClose">
          确定
        </WoButton>
      </WoActionBar>
    </div>
  </van-popup>
</template>

<script setup>
import { toRefs } from 'vue'
import WoButton from '@/components/WoElementCom/WoButton/WoButton.vue'
import WoActionBar from '@/components/WoElementCom/WoActionBar.vue'

// ===================== 组件属性定义 ======================
// 定义组件接收的props参数
const props = defineProps({
  // 弹窗显示状态，控制弹窗的显示和隐藏
  visible: {
    type: Boolean,
    default: false
  },
  // 赠品列表数据，包含赠品的详细信息
  giftList: {
    type: Array,
    default: () => []
  },
  // 当前商品购买数量，用于计算赠品数量
  goodsNum: {
    type: Number,
    default: 0
  }
})

// 使用toRefs解构props，保持响应性
const { visible, giftList, goodsNum } = toRefs(props)

// 定义组件向外发射的事件
const emit = defineEmits([
  'close',           // 弹窗关闭事件
  'update:visible',   // 更新visible状态的事件（v-model支持）
  'select',
  'create',
  'edit',
  'delete'
])

// ==================== 赠品条件判断相关 ====================
// 判断是否需要展示赠品获得条件
// 当最小购买数量不为'0'时，说明有条件限制，需要显示条件文案
const isGiftsAvailableIf = (giftBelongToSkuMinNum) => {
  return giftBelongToSkuMinNum !== '0'
}

// 判断当前购买数量是否满足赠品获得条件
// 当前购买数量大于等于最小购买数量时，可以获得赠品
const isGiftsAvailable = (giftBelongToSkuMinNum) => {
  return goodsNum.value >= giftBelongToSkuMinNum
}

// ==================== 赠品数量计算相关 ====================
// 复杂的赠品数量计算函数
// 根据最小购买数量、最大购买数量和单次赠品数量计算最终可获得的赠品数量
const giftNum = (giftBelongToSkuMaxNum, giftBelongToSkuMinNum, giftNum) => {
  // 参数默认值处理，避免undefined导致计算错误
  if (giftBelongToSkuMaxNum === undefined) {
    giftBelongToSkuMaxNum = '0'
  }
  if (giftBelongToSkuMinNum === undefined) {
    giftBelongToSkuMinNum = '0'
  }

  // 情况1：无任何购买数量限制（最大和最小都为0）
  // 赠品数量 = 购买数量 × 单次赠品数量
  if (giftBelongToSkuMaxNum === '0' && giftBelongToSkuMinNum === '0') {
    return goodsNum.value * giftNum
  }

  // 情况2：只有最小购买数量限制（最大为0，最小不为0）
  // 需要达到最小购买数量才能获得赠品，按倍数计算
  if (giftBelongToSkuMaxNum === '0' && giftBelongToSkuMinNum !== '0') {
    if (goodsNum.value < giftBelongToSkuMinNum) {
      return 0  // 未达到最小购买数量，无赠品
    } else {
      // 赠品数量 = (购买数量 ÷ 最小购买数量的整数部分) × 单次赠品数量
      return Math.floor(goodsNum.value / giftBelongToSkuMinNum) * giftNum
    }
  }

  // 情况3：只有最大购买数量限制（最大不为0，最小为0）
  // 超过最大购买数量后，赠品数量不再增加
  if (giftBelongToSkuMaxNum !== '0' && giftBelongToSkuMinNum === '0') {
    if (goodsNum.value > giftBelongToSkuMaxNum) {
      // 超过最大限制，按最大限制计算
      return giftBelongToSkuMaxNum * giftNum
    } else {
      // 未超过最大限制，按实际购买数量计算
      return goodsNum.value * giftNum
    }
  }

  // 情况4：最小购买数量大于最大购买数量（配置错误）
  if (giftBelongToSkuMinNum > giftBelongToSkuMaxNum) {
    return 0  // 配置错误，无赠品
  }

  // 情况5：最小和最大购买数量相等且都不为0
  // 只有购买指定数量才能获得1份赠品
  if (giftBelongToSkuMaxNum !== '0' && giftBelongToSkuMinNum !== '0' && giftBelongToSkuMinNum === giftBelongToSkuMaxNum) {
    return 1 * giftNum
  }

  // 情况6：有最小和最大购买数量限制，且最小小于最大
  if (giftBelongToSkuMinNum < giftBelongToSkuMaxNum) {
    if (goodsNum.value < giftBelongToSkuMinNum) {
      return 0  // 未达到最小购买数量，无赠品
    } else if (goodsNum.value >= giftBelongToSkuMinNum && goodsNum.value < giftBelongToSkuMaxNum) {
      // 在最小和最大之间，按最小购买数量的倍数计算
      return Math.floor(goodsNum.value / giftBelongToSkuMinNum) * giftNum
    } else if (goodsNum.value >= giftBelongToSkuMaxNum) {
      // 超过最大购买数量，按最大限制计算
      return giftBelongToSkuMaxNum * giftNum
    }
  }

  // 默认返回0（其他未覆盖的情况）
  return 0
}

// ==================== 弹窗操作相关 ====================
// 关闭弹窗处理函数
// 同时触发visible状态更新和close事件，确保父组件能正确响应
const handleClose = () => {
  emit('update:visible', false)  // 更新v-model绑定的visible状态
  emit('close')                  // 触发关闭事件，供父组件监听
}
</script>

<style scoped lang="less">
.gift-display-popup {
  background: #FFFFFF;
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;

  .popup-header {
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    padding: 16px 21px;
    flex-shrink: 0;

    .title {
      font-size: 17px;
      font-weight: 600;
      color: #171E24;
      margin: 0;
      text-align: center;
    }

    .close-btn {
      position: absolute;
      right: 20px;
      width: 24px;
      height: 24px;
      border: none;
      background: none;
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 0;

      img {
        width: 24px;
        height: 24px;
      }

      &:hover {
        opacity: 0.7;
      }
    }
  }

  .gift-tips {
    padding: 0 17px 7px 17px;
    box-sizing: border-box;
    .tips-text {
      color: #FF4141;
      font-size: 12px;
      font-weight: 500;
      line-height: 1.5;
    }
  }

  .gift-list {
    flex: 1;
    overflow-y: auto;
    padding: 0 21px 55px 21px;

    .gift-item {
      display: flex;
      background: #FFFFFF;
      margin-bottom: 20px;

      &:last-child {
        margin-bottom: 0;
      }

      .gift-image {
        position: relative;
        width: 75px;
        height: 75px;
        margin-right: 17px;
        flex-shrink: 0;

        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
          border-radius: 4px;
        }
      }

      .gift-info {
        flex: 1;
        display: flex;

        .gift-info-left {
          flex: 1;
          display: flex;
          flex-direction: column;
          justify-content: space-between;
          margin-right: 10px;

          .gift-name {
            font-size: 13px;
            color: #171E24;
            line-height: 1.5;
            display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
        word-break: break-all;
          }

          .gift-condition {
            font-size: 12px;
            color: #FF4141;
          }
        }

        .gift-info-right {
          .quantity {
            font-size: 13px;
            color: #718096;
          }
        }
      }
    }

    &::-webkit-scrollbar {
      width: 4px;
    }

    &::-webkit-scrollbar-track {
      background: #F1F1F1;
      border-radius: 2px;
    }

    &::-webkit-scrollbar-thumb {
      background: #C1C1C1;
      border-radius: 2px;
    }

    &::-webkit-scrollbar-thumb:hover {
      background: #A8A8A8;
    }
  }
}
</style>
