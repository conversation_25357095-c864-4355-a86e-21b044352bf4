<!--
===================== 过期提示弹窗组件 ExpirationPopup =======================
功能描述：
- 用于显示售后申请时效过期的提示信息
- 支持自定义标题、主要文本、副文本和确认按钮文本
- 提供关闭和确认操作的事件回调
- 可控制确认按钮的显示/隐藏
- 采用底部弹出的交互方式，提供良好的用户体验
============================================================================
-->

<template>
  <!-- 底部弹出的过期提示弹窗容器 -->
  <van-popup
    class="popup common-popup"
    :style="{ minHeight: '240px' }"
    safe-area-inset-bottom
    lock-scroll
    round
    position="bottom"
    v-model:show="visible"
  >
    <!-- 弹窗头部区域：包含标题和关闭按钮 -->
    <div class="popup-header">
      <!-- 弹窗标题文本 -->
      <p class="title">{{ title }}</p>
      <!-- 关闭按钮：点击触发关闭事件 -->
      <img @click="handleClose" class="close" src="./assets/popupClose.png" alt="" srcset="">
    </div>
    
    <!-- 弹窗内容区域：显示过期提示信息 -->
    <div class="popup-content">
      <div class="after-sales-expiration-content">
        <!-- 主要提示文本：显示过期的核心信息 -->
        <p class="after-sales-expiration-tips">{{ mainText }}</p>
        <!-- 副提示文本：显示补充说明信息 -->
        <p class="after-sales-expiration-sub-tips">{{ subText }}</p>
      </div>
    </div>
    
    <!-- 操作按钮区域：根据 showConfirmButton 控制显示 -->
    <div class="popup-op" v-if="showConfirmButton">
      <!-- 确认按钮：点击触发确认事件并关闭弹窗 -->
      <div class="popup-op-btn" @click="handleConfirm">
        {{ confirmText }}
      </div>
    </div>
  </van-popup>
</template>

<script setup>
import { defineProps, defineEmits, toRefs } from 'vue'

// ===================== 组件属性定义和响应式处理 =======================
// 定义组件接收的属性配置
const props = defineProps({
  // 弹窗显示状态：控制弹窗的显示和隐藏
  visible: {
    type: Boolean,
    default: false
  },
  // 弹窗标题：显示在头部的标题文本
  title: {
    type: String,
    default: ''
  },
  // 主要提示文本：显示过期的核心信息
  mainText: {
    type: String,
    default: '抱歉，订单已过售后申请时效'
  },
  // 副提示文本：显示补充说明信息
  subText: {
    type: String,
    default: '商品已超过售后期限，如需售后可联系客服处理'
  },
  // 确认按钮文本：自定义确认按钮显示的文字
  confirmText: {
    type: String,
    default: '确定'
  },
  // 确认按钮显示控制：决定是否显示确认按钮
  showConfirmButton: {
    type: Boolean,
    default: true
  }
})

// 使用 toRefs 解构 props 以保持响应性
const {
  visible,
  title,
  mainText,
  subText,
  confirmText,
  showConfirmButton
} = toRefs(props)

// 定义组件向父组件发射的事件
const emit = defineEmits(['update:visible', 'close', 'confirm'])

// ===================== 弹窗交互事件处理 =======================
// 处理关闭弹窗操作：更新显示状态并发射关闭事件
const handleClose = () => {
  emit('update:visible', false)  // 更新弹窗显示状态为隐藏
  emit('close')                  // 发射关闭事件通知父组件
}

// 处理确认操作：更新显示状态并发射确认事件
const handleConfirm = () => {
  emit('update:visible', false)  // 更新弹窗显示状态为隐藏
  emit('confirm')                // 发射确认事件通知父组件
}
</script>

<style scoped lang="less">
.popup {
  box-sizing: border-box;
  padding: 20px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;

  .popup-header {
    display: flex;
    align-items: center;
    margin-bottom: 14px;

    .title {
      flex: 1;
      font-size: 17px;
      color: #171E24;
      text-align: center;
      line-height: 1.5;
      font-weight: 400;
    }

    .close {
      width: 14px;
      height: 14px;
    }
  }

  .popup-content {
    margin-bottom: 50px;

    .after-sales-expiration-content {
      .after-sales-expiration-tips {
        font-size: 19px;
        color: #171E24;
        text-align: center;
        font-weight: 700;
        margin-bottom: 15px;
      }

      .after-sales-expiration-sub-tips {
        margin-top: 10px;
        font-size: 13px;
        color: #4A5568;
        text-align: center;
        font-weight: 400;
      }
    }
  }

  .popup-op {
    width: 100%;
    height: 35px;
    margin-top: 20px;

    .popup-op-btn {
      background-image: var(--wo-biz-theme-gradient-1);
      border-radius: 49px;
      font-size: 17px;
      color: #FFFFFF;
      font-weight: 400;
      width: 100%;
      height: 35px;
      text-align: center;
      line-height: 35px;
      cursor: pointer;

      &:active {
        background-image: var(--wo-biz-theme-gradient-3);
      }
    }
  }
}
</style>
