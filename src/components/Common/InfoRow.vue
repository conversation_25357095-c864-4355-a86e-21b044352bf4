<!--
/**
 * 信息行展示组件
 * 
 * 功能描述：
 * - 以标签-值的形式展示信息行
 * - 支持自定义值的样式类
 * - 支持显示右侧箭头图标
 * - 支持通过插槽自定义值的内容
 * 
 * 主要特性：
 * - 响应式布局，标签固定宽度，值自适应
 * - 支持文本溢出处理（省略号）
 * - 可配置箭头图标和替代文本
 * - 灵活的样式定制能力
 */
-->
<template>
  <!-- 信息行容器：左右布局，标签在左，值在右 -->
  <div class="row-section">
    <!-- 标签区域：显示信息项的名称 -->
    <div class="label">{{ label }}</div>
    <!-- 值区域：显示信息项的内容，支持自定义样式 -->
    <div class="value" :class="valueClass">
      <!-- 值内容插槽：允许父组件自定义值的展示方式 -->
      <slot name="value">
        <!-- 不显示箭头时：仅显示文本内容 -->
        <span v-if="!showArrow" class="value-text">{{ value }}</span>
        <!-- 显示箭头时：文本内容 + 箭头图标 -->
        <template v-else>
          <!-- 值文本内容 -->
          <span class="value-text">{{ value }}</span>
          <!-- 右侧箭头图标：支持自定义图标或使用默认图标 -->
          <img
            v-if="showArrow"
            :src="arrowIcon || arrowIconImg"
            :alt="arrowAlt"
            class="arrow-icon"
          />
        </template>
      </slot>
    </div>
  </div>
</template>

<script setup>
import { toRefs } from 'vue'
import arrowIconImg from '../../static/images/arrow-right-gray.png'

// ===================== 组件属性定义 ======================
// 定义组件接收的props参数
const props = defineProps({
  // 标签文本，必填项，显示在左侧的信息项名称
  label: {
    type: String,
    required: true
  },
  // 值文本，显示在右侧的信息项内容
  value: {
    type: String,
    default: ''
  },
  // 值区域的自定义样式类，支持字符串、数组或对象形式
  valueClass: {
    type: [String, Array, Object],
    default: ''
  },
  // 是否显示右侧箭头图标，用于表示可点击或可展开
  showArrow: {
    type: Boolean,
    default: false
  },
  // 自定义箭头图标路径，为空时使用默认图标
  arrowIcon: {
    type: String,
    default: ''
  },
  // 箭头图标的替代文本，用于无障碍访问
  arrowAlt: {
    type: String,
    default: '箭头'
  }
})

// 使用toRefs解构props，保持响应性
const { label, value, valueClass, showArrow, arrowIcon, arrowAlt } = toRefs(props)
</script>

<style scoped lang="less">
.row-section {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 12px;

  &:last-child {
    margin-bottom: 0;
  }

  .label {
    font-size: 13px;
    color: #4A5568;
    line-height: 1.5;
    min-width: 65px;
    flex-shrink: 0;
  }

  .value {
    font-size: 13px;
    color: #171E24;
    line-height: 1.5;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    flex: 1;
    margin-left: 12px;
    text-align: right;
    overflow: hidden;

    .value-text {
      overflow: hidden; text-overflow: ellipsis; white-space: nowrap;;
      max-width: 100%;
    }

    &.activity-text {
      color: var(--wo-biz-theme-color);
      font-weight: 500;
    }

    &.activity-no-text {
      color: #718096;
      font-weight: 400;
    }

    .arrow-icon {
      margin-left: 10px;
      width: 6px;
      height: 12px;
      color: #718096;
      font-size: 16px;
      vertical-align: middle;
      flex-shrink: 0;
    }
  }
}
</style>
