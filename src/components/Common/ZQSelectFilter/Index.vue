<!--
/**
 * 右侧筛选弹窗组件
 *
 * 主要功能：
 * 1. 提供右侧全屏弹窗的筛选界面，支持省份和服务商的选择
 * 2. 支持单选模式，用户可以选择一个省份和一个服务商
 * 3. 提供临时选择机制，用户确认前可以随时取消修改
 * 4. 集成重置功能，支持一键清空所有选择
 * 5. 提供确认和取消操作，确保用户操作的可控性
 * 6. 与外部状态管理集成，支持数据的持久化存储
 *
 * 技术特点：
 * - 使用van-popup组件实现右侧全屏弹窗效果
 * - 采用临时状态管理，避免直接修改外部数据
 * - 支持响应式数据绑定和状态同步
 * - 集成Pinia状态管理进行数据持久化
 *
 * 使用场景：
 * - 列表页面的数据筛选功能
 * - 需要按省份和服务商进行数据过滤的业务场景
 * - 需要提供用户友好的筛选交互体验的场景
 */
-->

<template>
  <!-- 筛选弹窗主容器 -->
  <van-popup
    v-model:show="visible"
    position="right"
    :style="{ height: '100%', width: '75%' }"
    safe-area-inset-bottom
    @closed="onPopupClosed"
  >
    <div class="province-drawer">
      <!-- 弹窗头部区域，包含关闭按钮 -->
      <div class="drawer-header">
        <!-- 关闭按钮，点击关闭弹窗 -->
        <div class="drawer-close" @click="handleClose">×</div>
      </div>

      <!-- 筛选内容区域 -->
      <div class="drawer-content">
        <!-- 省份筛选区块 -->
        <div class="service-section">
          <!-- 省份区块标题 -->
          <div class="section-title">选择省分</div>
          <!-- 省份选项列表容器 -->
          <div class="province-grid">
            <!-- 全部选项 -->
            <div
              class="province-item"
              :class="{ active: tempSelectedAreaId === '' }"
              @click="selectProvince('')"
            >
              <div class="province-name">全部</div>
            </div>
            <!-- 遍历省份列表，为每个省份创建可点击的选项 -->
            <!-- 根据临时选择状态添加active样式 -->
            <div
              class="province-item"
              v-for="province in provinceList"
              :key="province.areaId"
              :class="{ active: tempSelectedAreaId === province.areaId }"
              @click="selectProvince(province.areaId)"
            >
              <div class="province-name">{{ province.areaName }}</div>
            </div>
          </div>
        </div>

        <!-- 服务商筛选区块 -->
        <div class="service-section">
          <!-- 服务商区块标题 -->
          <div class="section-title">选择服务商</div>
          <!-- 服务商选项列表容器 -->
          <div class="service-grid">
            <!-- 全部选项 -->
            <div
              class="service-item"
              :class="{ active: tempSelectedIsvId === '' }"
              @click="selectService('')"
            >
              <div class="service-name">全部</div>
            </div>
            <!-- 遍历服务商列表，为每个服务商创建可点击的选项 -->
            <!-- 根据临时选择状态添加active样式 -->
            <div
              class="service-item"
              v-for="service in serviceList"
              :key="service.isvId"
              :class="{ active: tempSelectedIsvId === service.code }"
              @click="selectService(service.code)"
            >
              <div class="service-name">{{ service.name }}</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 底部操作区域，包含重置和确定按钮 -->
      <div class="action-buttons">
        <!-- 重置按钮，清空所有选择 -->
        <div class="reset-btn" @click="resetSelection">重置</div>
        <!-- 确定按钮，确认当前选择并关闭弹窗 -->
        <div class="confirm-btn" @click="confirmSelection">确定</div>
      </div>
    </div>
  </van-popup>
</template>

<script setup>
import { computed, onMounted, ref, watch, toRefs } from 'vue'
import { useProvinceServiceStore } from '@/store/modules/provinceService.js'

// ==================== 组件属性定义 ====================
// 定义组件接收的props参数
const props = defineProps({
  // v-model绑定的值，控制弹窗显示状态
  modelValue: {
    type: Boolean,
    default: false
  }
})

// 使用toRefs解构props，保持响应性
const { modelValue } = toRefs(props)

// 定义组件向父组件发射的事件
const emit = defineEmits(['update:modelValue', 'confirm'])

// ==================== 状态管理集成 ====================
// 获取省份服务商状态管理实例
const provinceServiceStore = useProvinceServiceStore()

// ==================== 临时选择状态管理 ====================
// 临时选择状态，用于在确认前保存用户的选择
// 临时选择的省份ID，用于在用户确认前暂存选择
const tempSelectedAreaId = ref('')
// 临时选择的服务商ID，用于在用户确认前暂存选择
const tempSelectedIsvId = ref('')

// ==================== 数据获取和计算 ====================
// 计算属性：获取省份列表数据
// 从provinceServiceStore中获取省份列表
const provinceList = computed(() => provinceServiceStore.provinceList)
// 计算属性：获取服务商列表数据
// 从provinceServiceStore中获取服务商列表
const serviceList = computed(() => provinceServiceStore.serviceList)

// ==================== 弹窗显示控制 ====================
// 弹窗显示状态，与modelValue双向绑定
const visible = computed({
  get() {
    return props.modelValue
  },
  set(val) {
    emit('update:modelValue', val)
  }
})

// 计算选中的名称，基于临时状态
// 计算属性：获取当前选择的省份名称
// 用于外部组件显示当前选择状态
const selectedProvinceName = computed(() => {
  if (!tempSelectedAreaId.value) return '全部'
  const province = provinceList.value.find(p => p.areaId === tempSelectedAreaId.value)
  return province ? province.areaName : '全部'
})

// 计算属性：获取当前选择的服务商名称
// 用于外部组件显示当前选择状态
const selectedServiceName = computed(() => {
  if (!tempSelectedIsvId.value) return '全部'
  const service = serviceList.value.find(s => s.code === tempSelectedIsvId.value)
  return service ? service.name : '全部'
})

// ==================== 用户操作处理 ====================
// 选择省份的处理函数
// 当用户点击省份选项时触发
const selectProvince = (areaId) => {
  // 更新临时选择的省份ID
  tempSelectedAreaId.value = areaId
}

// 选择服务商的处理函数
// 当用户点击服务商选项时触发
const selectService = (code) => {
  // 更新临时选择的服务商ID
  tempSelectedIsvId.value = code
}

// 关闭弹窗的处理函数
// 当用户点击关闭按钮或其他关闭操作时触发
const handleClose = () => {
  // 设置弹窗为不可见状态
  visible.value = false
}

// 弹窗关闭后的处理函数
// 当弹窗完全关闭后触发，重置临时选择状态
const onPopupClosed = () => {
  // 从store中恢复当前选择状态到临时状态
  tempSelectedAreaId.value = provinceServiceStore.selectedAreaId
  tempSelectedIsvId.value = provinceServiceStore.selectedIsvId
}

// 重置选择的处理函数
// 当用户点击重置按钮时触发
const resetSelection = () => {
  // 重置临时选择状态
  tempSelectedAreaId.value = ''
  tempSelectedIsvId.value = ''
  // 触发确认逻辑
  confirmSelection()
}

// 确认选择的处理函数
// 当用户点击确定按钮时触发
const confirmSelection = () => {
  // 只有在确认时才更新store
  // 将临时选择的省份保存到store中
  provinceServiceStore.selectProvince(tempSelectedAreaId.value)
  // 将临时选择的服务商保存到store中
  provinceServiceStore.selectService(tempSelectedIsvId.value)

  // 关闭弹窗
  visible.value = false
  // 向父组件发射确认事件，传递选择的数据
  emit('confirm', {
    areaId: tempSelectedAreaId.value,
    isvId: tempSelectedIsvId.value,
    provinceName: selectedProvinceName.value,
    serviceName: selectedServiceName.value
  })
}

// 获取服务商列表的函数
const fetchServiceList = () => {
  return provinceServiceStore.fetchServiceList()
}

// 监听弹窗显示状态，当弹窗打开时初始化临时状态
// 监听visible变化，同步弹窗显示状态
// 当弹窗打开时，初始化临时选择状态
watch(visible, (newVal) => {
  if (newVal) {
    // 弹窗打开时，将store中的当前值复制到临时状态
    tempSelectedAreaId.value = provinceServiceStore.selectedAreaId
    tempSelectedIsvId.value = provinceServiceStore.selectedIsvId
  }
})

// ==================== 生命周期处理 ====================
onMounted(async () => {
  // 如果serviceList为空，则自动获取服务商列表
  if (!serviceList.value || serviceList.value.length === 0) {
    await fetchServiceList()
  }

  // 初始化临时状态
  tempSelectedAreaId.value = provinceServiceStore.selectedAreaId
  tempSelectedIsvId.value = provinceServiceStore.selectedIsvId
})
</script>

<style lang="less" scoped>
.province-drawer {
  display: flex;
  flex-direction: column;
  height: 100%;

  .drawer-header {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    padding: 10px;

    .drawer-close {
      font-size: 24px;
      color: #999999;
      cursor: pointer;
    }
  }

  .drawer-content {
    padding: 10px;
    flex: 1;
    overflow: auto;

    .section-title {
      font-size: 16px;
      font-weight: 500;
      color: #333;
      margin-bottom: 16px;
    }

    .province-grid {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      gap: 12px;
      margin-bottom: 30px;

      .province-item {
        padding: 5px;
        text-align: center;
        background: #f8f9fa;
        border-radius: 4px;
        cursor: pointer;
        box-sizing: border-box;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;

        &.active {
          background: #ff6b35;
          color: white;
        }

        .province-name {
          font-size: 13px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }
    }

    .service-section {
      .section-title {
        font-size: 16px;
        font-weight: 500;
        color: #333;
        margin-bottom: 16px;
      }

      .service-grid {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: 12px;
        margin-bottom: 30px;

        .service-item {
          padding: 6px;
          text-align: center;
          background: #f8f9fa;
          border-radius: 4px;
          cursor: pointer;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;

          &.active {
            background: #ff6b35;
            color: white;
          }

          .service-name {
            font-size: 13px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
        }
      }
    }
  }

  .action-buttons {
    display: flex;
    gap: 12px;
    padding: 10px;
    box-sizing: border-box;

    .reset-btn,
    .confirm-btn {
      flex: 1;
      padding: 8px;
      text-align: center;
      border-radius: 4px;
      cursor: pointer;
      font-size: 16px;
      box-sizing: border-box;
    }

    .reset-btn {
      background: #f8f9fa;
      color: #666666;
    }

    .confirm-btn {
      background: #ff6b35;
      color: white;
    }
  }
}
</style>
