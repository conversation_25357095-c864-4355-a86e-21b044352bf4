import homeImg from './assets/tab-home.png'
import homeActiveImg from './assets/tab-home-active.png'
import jdHomeActiveImg from './assets/jd-tab-home-active.png'
import categoryImg from './assets/tab-category.png'
import categoryActiveImg from './assets/tab-category-active.png'
import jdCategoryActiveImg from './assets/jd-tab-category-active.png'
import userImg from './assets/tab-user.png'
import userActiveImg from './assets/tab-user-active.png'
import jdUserActiveImg from './assets/jd-tab-user-active.png'
import cartImg from './assets/tab-cart.png'
import cartActiveImg from './assets/tab-cart-active.png'
import jdCartActiveImg from './assets/jd-tab-cart-active.png'
import lnzxHomeImg from './assets/lnzx-home.png'

/**
 * 默认标签页配置
 * @type {Array<{title: string, path: string, img: string, activeImg: string}>}
 */
const defaultTabs = [
  {
    title: '首页',
    path: '/home',
    img: homeImg,
    activeImg: homeActiveImg
  },
  {
    title: '分类',
    path: '/category',
    img: categoryImg,
    activeImg: categoryActiveImg
  },
  {
    title: '购物车',
    path: '/cart',
    img: cartImg,
    activeImg: cartActiveImg
  },
  {
    title: '我的',
    path: '/user',
    img: userImg,
    activeImg: userActiveImg
  }
]

/**
 * 业务特定的标签页配置映射
 * @type {Object<string, Array<{title: string, path: string, img: string, activeImg: string}>>}
 */
const tabConfigs = {
  ygjd: [
    {
      title: '首页',
      path: '/home',
      img: homeImg,
      activeImg: jdHomeActiveImg
    },
    {
      title: '分类',
      path: '/category',
      img: categoryImg,
      activeImg: jdCategoryActiveImg
    },
    {
      title: '购物车',
      path: '/cart',
      img: cartImg,
      activeImg: jdCartActiveImg
    },
    {
      title: '我的',
      path: '/user',
      img: userImg,
      activeImg: jdUserActiveImg
    }
  ],

  lnzx: [
    {
      title: '',
      path: '/home',
      img: lnzxHomeImg,
      activeImg: lnzxHomeImg
    },
    {
      title: '购物车',
      path: '/cart',
      img: cartImg,
      activeImg: cartActiveImg
    },
    {
      title: '我的',
      path: '/user',
      img: userImg,
      activeImg: userActiveImg
    }
  ],

  zq: [
    {
      title: '首页',
      path: '/home',
      img: homeImg,
      activeImg: homeActiveImg
    },
    {
      title: '分类',
      path: '/category',
      img: categoryImg,
      activeImg: categoryActiveImg
    },
    {
      title: '购物车',
      path: '/cart',
      img: cartImg,
      activeImg: cartActiveImg
    },
    {
      title: '我的',
      path: '/user',
      img: userImg,
      activeImg: userActiveImg
    }
  ]
}

/**
 * 根据业务代码获取标签页配置
 * @param {string} bizCode - 业务代码
 * @returns {Array<{title: string, path: string, img: string, activeImg: string}>} 标签页配置数组
 */
export const getTabConfig = (bizCode) => {
  return tabConfigs[bizCode] || defaultTabs
}

/**
 * 获取所有图片资源用于预加载
 * @returns {{}[]} 图片资源数组
 */
export const getAllImages = () => {
  return [
    homeImg, homeActiveImg, jdHomeActiveImg,
    categoryImg, categoryActiveImg, jdCategoryActiveImg,
    userImg, userActiveImg, jdUserActiveImg,
    cartImg, cartActiveImg, jdCartActiveImg,
    lnzxHomeImg
  ]
}
