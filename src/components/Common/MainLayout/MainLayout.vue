<!--
===================== 主布局组件 - 移动端底部导航布局 =======================
功能描述：
- 提供移动端应用的主要布局结构，包含内容区域和底部导航栏
- 支持动态配置导航标签页，根据业务代码显示不同的导航项
- 提供滚动控制和导航显示控制功能
- 支持导航项的激活状态管理和路由跳转
- 针对不同业务场景提供个性化的导航样式
==============================================================================
-->
<template>
  <!-- 主布局容器 - 采用flex布局实现全屏高度分配 -->
  <div class="main-layout">
    <!-- 内容区域 - 动态计算高度以适配底部导航栏，支持滚动控制 -->
    <div class="main-layout__content" :class="contentClass">
      <!-- 页面内容插槽 - 用于插入具体的页面组件 -->
      <slot />
    </div>
    <!-- 底部导航栏 - 根据showNav属性控制显示，固定在页面底部 -->
    <nav v-if="showNav" class="main-layout__nav" ref="navRef">
      <!-- 导航项循环 - 遍历tabs数组生成导航按钮 -->
      <button 
        v-for="(item, index) in tabs" 
        :key="`tab-${index}`" 
        class="nav-item" 
        :class="getNavItemClass(item, index)"
        type="button" 
        @click="handleTabClick(index)" 
        :aria-label="item.title || '导航'"
        :aria-current="activeTab === index ? 'page' : undefined">
        <!-- 导航图标容器 - 包装图标元素以便于样式控制 -->
        <div class="nav-item__icon-wrapper">
          <!-- 导航图标 - 根据激活状态显示不同图标，支持预加载和优化渲染 -->
          <img 
            class="nav-item__icon" 
            :class="getIconClass(item)" 
            :src="getIconSrc(item, index)" 
            :alt="item.title || ''"
            loading="eager" 
            decoding="async">
        </div>
        <!-- 导航文字 - 仅在有标题时显示，支持激活状态样式 -->
        <span v-if="item.title" class="nav-item__text" :class="getTextClass(index)">
          {{ item.title }}
        </span>
      </button>
    </nav>
  </div>
</template>

<script setup>
// ===================== 外部依赖导入 =======================
import { ref, computed, onMounted, toRefs } from 'vue' // Vue 3 组合式 API
import { useRouter, useRoute } from 'vue-router' // Vue Router 路由管理
import { getBizCode } from '@/utils/curEnv' // 业务代码获取工具
import { getTabConfig, getAllImages } from './tabConfigs' // 导航配置和图片资源管理

// ===================== 组件属性定义和响应式处理 =======================
// 定义组件接收的属性
const props = defineProps({
  scroll: {
    type: String, // 内容区域滚动模式：'auto' | 'none'
    default: 'auto'
  },
  showNav: {
    type: Boolean, // 是否显示底部导航栏
    default: true
  }
})

// 使用 toRefs 解构 props 以保持响应性
const { scroll, showNav } = toRefs(props)

// ===================== 路由和导航状态管理 =======================
const router = useRouter() // 路由实例，用于编程式导航
const route = useRoute() // 当前路由信息，用于判断激活状态
const navRef = ref(null) // 导航栏DOM引用
const tabs = ref([]) // 导航标签页配置数组

// ===================== 图片资源预加载管理 =======================
// 预加载所有导航图标，提升用户体验
const preloadImages = () => {
  const allImages = getAllImages() // 获取所有导航图标的URL列表
  allImages.forEach(src => {
    const img = new Image() // 创建图片对象进行预加载
    img.src = src // 设置图片源，触发浏览器下载
  })
}

// ===================== 业务状态计算和导航管理 =======================
// 当前业务代码，用于区分不同的业务场景
const bizCode = computed(() => getBizCode() || '')

// 当前激活的导航标签索引，基于路由路径匹配
const activeTab = computed(() => {
  const path = route.path // 获取当前路由路径
  // 查找路径匹配的导航项索引（使用indexOf进行前缀匹配）
  return tabs.value.findIndex(item => path.indexOf(item.path) === 0)
})

// 内容区域的CSS类名，根据滚动模式和导航显示状态动态计算
const contentClass = computed(() => {
  // 根据导航显示状态确定滚动模式
  const scrollValue = showNav.value ? scroll.value : 'none'
  const classes = [`main-layout__content--${scrollValue}`]
  
  // 如果不显示导航，添加无导航样式类
  if (!showNav.value) {
    classes.push('main-layout__content--no-nav')
  }

  return classes
})

// ===================== 导航样式计算函数 =======================
// 计算导航项的CSS类名，处理推广样式和激活状态
const getNavItemClass = (item, index) => {
  const classes = []
  // 如果有className属性，添加推广样式
  if (item.className) classes.push('nav-item--promotion')
  // 如果是当前激活的标签，添加激活样式
  if (activeTab.value === index) classes.push('nav-item--active')
  return classes
}

// 计算导航图标的CSS类名，处理普通图标和大图标样式
const getIconClass = (item) => {
  const classes = []
  // 没有特殊className的普通图标使用标准尺寸
  if (!item.className) classes.push('nav-item__icon--normal')
  // 没有标题的图标使用大尺寸（如推广图标）
  if (!item.title) classes.push('nav-item__icon--large')
  return classes
}

// 根据激活状态获取对应的图标源地址
const getIconSrc = (item, index) => {
  // 激活状态显示激活图标，否则显示普通图标
  return activeTab.value === index ? item.activeImg : item.img
}

// 计算导航文字的CSS类名，处理激活状态和特殊业务样式
const getTextClass = (index) => {
  const classes = []
  // 如果是当前激活的标签
  if (activeTab.value === index) {
    classes.push('nav-item__text--active') // 添加激活文字样式
    // 京东业务特殊处理
    if (bizCode.value === 'ygjd') {
      classes.push('nav-item__text--jd')
    }
  }
  return classes
}

// ===================== 导航交互事件处理 =======================
// 处理导航标签点击事件，实现路由跳转逻辑
const handleTabClick = (index) => {
  // 特殊处理：当导航项超过4个且点击第3个时，使用页面跳转而非路由跳转
  if (tabs.value.length > 4 && index === 2) {
    window.location.href = tabs.value[index].path
    return
  }
  
  // 如果点击的是当前激活标签，不执行任何操作
  if (activeTab.value === index) return
  
  const targetPath = tabs.value[index].path
  
  // 根据不同路径采用不同的跳转策略
  if (targetPath === '/home') {
    // 首页直接跳转
    router.push({ path: targetPath })
  } else if (targetPath === '/category') {
    // 分类页面添加时间戳参数，强制刷新
    router.push({ path: targetPath, query: { _t: Date.now().toString() } })
  } else {
    // 其他页面直接跳转
    router.push({ path: targetPath })
  }
}

// 获取推广图标（预留功能，目前仅支持自营业务）
const getPromotionIcon = async () => {
  // 仅自营业务需要处理推广图标
  if (getBizCode() !== 'ziying') return
  // TODO: 实现推广图标获取逻辑
}

// ===================== 组件生命周期管理 =======================
// 组件挂载时初始化导航配置和预加载资源
onMounted(() => {
  preloadImages() // 预加载所有导航图标
  const bizCode = getBizCode() // 获取当前业务代码
  tabs.value = getTabConfig(bizCode) // 根据业务代码配置导航标签
  getPromotionIcon() // 获取推广图标（如果需要）
})
</script>

<style lang="less" scoped>
* {
  box-sizing: border-box;
}
// 安卓8以下单独处理
.android_8 {
  .main-layout {
    display: flex;
    height: 100vh;

    .main-layout__content {
      flex: 1;
      overflow: hidden;
      padding-bottom: 50px;

      &--none {
        height: calc(100vh - 50px);
      }

      &--no-nav {
        height: 100vh;
        padding-bottom: 0;
      }
    }

    .main-layout__nav {
      height: 50px;
    }
  }
}

.main-layout {
  display: flex;
  height: 100vh;
  flex-direction: column;

  &__content {
    flex: 1;
    overflow: hidden;
    padding-bottom: 50px;

    &--none {
      height: calc(100vh - 50px - var(--saib));
      padding-bottom: calc(var(--saib));
    }

    &--auto {
      height: calc(100vh - 50px - var(--saib));
      padding-bottom: 50px;
    }

    &--no-nav {
      height: 100vh;
      padding-bottom: calc(var(--saib));
    }
  }

  &__nav {
    position: fixed;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 10;
    display: flex;
    height: 50px;
    border-top: 1px solid #E2E8EE;
    background: #FFFFFF;
    transform: translateZ(0);
    will-change: transform;
  }
}

.nav-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  flex: 1;
  padding: 6px;
  text-align: center;
  background: none;
  border: none;
  cursor: pointer;
  -webkit-tap-highlight-color: transparent;
  transition: opacity 0.3s ease;

  &:active {
    opacity: 0.7;
  }

  &--promotion {
    padding: 0;
  }

  &__icon-wrapper {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  &__icon {
    display: block;
    image-rendering: -webkit-optimize-contrast;
    backface-visibility: hidden;
    transform: translateZ(0);

    &--normal {
      width: 23px;
      height: 23px;
    }

    &--large {
      width: 40px;
      height: 40px;
    }
  }

  &__text {
    margin: 2px 0 0;
    font-size: 12px;
    color: #718096;
    line-height: 1;
    font-feature-settings: 'tnum';

    &--active {
      color: var(--wo-biz-theme-color);
    }

    &--jd {
      color: var(--wo-biz-theme-color) !important;
    }
  }

  &--promotion &__icon {
    width: 60px;
    height: 50px;
  }
}

.nav-item__icon {
  opacity: 1;
  transition: opacity 0.1s ease;
}

@media (-webkit-min-device-pixel-ratio: 2) {
  .nav-item__icon {
    image-rendering: -webkit-optimize-contrast;
  }
}
</style>
