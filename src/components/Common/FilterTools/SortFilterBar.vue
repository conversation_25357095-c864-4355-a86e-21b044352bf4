<!--
===================== 排序筛选工具栏组件 =======================
功能描述：
- 提供商品列表的排序功能（综合、价格、销量）
- 提供筛选面板的开关控制
- 支持排序方向切换（升序/降序）
- 根据业务代码显示不同的筛选图标
- 动态显示当前激活的排序类型和筛选状态
==============================================================
-->
<template>
  <!-- 排序筛选工具栏容器 -->
  <div class="sort-filter-bar">
    <!-- 综合排序选项：默认排序方式 -->
    <div class="sort-item" :class="{ active: sortType === 'sort' }"
         @click="changeSortType('sort')">
      综合
    </div>
    <!-- 价格排序选项：支持升序降序切换 -->
    <div class="sort-item" :class="{ active: sortType === 'price' }" @click="changeSortType('price')">
      价格
      <!-- 价格排序激活时显示的排序图标，根据排序方向旋转 -->
      <img
        v-if="sortType === 'price'"
        :src="sortSelectImg"
        :style="{ transform: sortOrder === 'desc' ? 'rotate(180deg)' : 'rotate(0deg)' }"
        alt="排序"
        class="sort-icon"
      />
      <!-- 价格排序未激活时显示的默认图标 -->
      <img
        v-else
        :src="sortNoSelectImg"
        alt="排序"
        class="sort-icon"
      />
    </div>
    <!-- 销量排序选项：支持升序降序切换 -->
    <div class="sort-item" :class="{ active: sortType === 'sale' }" @click="changeSortType('sale')">
      销量
      <!-- 销量排序激活时显示的排序图标，根据排序方向旋转 -->
      <img
        v-if="sortType === 'sale'"
        :src="sortSelectImg"
        :style="{ transform: sortOrder === 'desc' ? 'rotate(180deg)' : 'rotate(0deg)' }"
        alt="排序"
        class="sort-icon"
      />
      <!-- 销量排序未激活时显示的默认图标 -->
      <img
        v-else
        :src="sortNoSelectImg"
        alt="排序"
        class="sort-icon"
      />
    </div>
    <!-- 筛选选项：控制筛选面板的显示隐藏 -->
    <div class="filter-item" :class="{ active: hasFilterConditions }" @click="toggleFilter">
      筛选
      <!-- 筛选图标：根据是否有筛选条件和业务代码显示不同图标 -->
      <img
        :src="hasFilterConditions ? (bizCode === 'ygjd' ? JDFilterSelectImg : WoFilterSelectImg) : filterNoSelectImg"
        alt="筛选"
        class="filter-icon"
      />
    </div>
  </div>
</template>

<script setup>
// ===================== 外部依赖导入 =======================
// 排序和筛选相关图标资源
import sortNoSelectImg from '@/static/images/sort-noselect.png'
import sortSelectImg from '@/static/images/sort-select.png'
import filterNoSelectImg from '@/static/images/filter-noselect.png'
import WoFilterSelectImg from '@/static/images/wo-filter-select.png'
import JDFilterSelectImg from '@/static/images/jd-filter-select.png'
import { getBizCode } from '@/utils/curEnv'
import { toRefs } from 'vue'

// ===================== 组件属性定义和响应式处理 =======================
// 定义组件接收的属性
const props = defineProps({
  // 当前排序类型：'sort'(综合)、'price'(价格)、'sale'(销量)
  sortType: {
    type: String,
    default: 'sort'
  },
  // 排序方向：'asc'(升序)、'desc'(降序)、''(默认)
  sortOrder: {
    type: String,
    default: ''
  },
  // 是否存在筛选条件：用于控制筛选按钮的激活状态
  hasFilterConditions: {
    type: Boolean,
    default: false
  }
})

// 使用 toRefs 解构 props 保持响应性
const { sortType, sortOrder, hasFilterConditions } = toRefs(props)

// 定义组件向外发射的事件
const emit = defineEmits(['sort-change', 'filter-toggle'])

// ===================== 业务环境配置 =======================
// 获取当前业务代码，用于区分不同业务的图标显示
const bizCode = getBizCode()

// ===================== 排序功能事件处理 =======================
// 排序类型切换处理：发送排序变更事件给父组件
const changeSortType = (type) => {
  // 传递新的排序类型和当前状态给父组件处理
  emit('sort-change', { type, currentSortType: props.sortType, currentSortOrder: props.sortOrder })
}

// ===================== 筛选功能事件处理 =======================
// 筛选面板切换处理：控制筛选面板的显示隐藏
const toggleFilter = () => {
  // 通知父组件切换筛选面板状态
  emit('filter-toggle')
}
</script>

<style scoped lang="less">
.sort-filter-bar {
  display: flex;
  align-items: center;
  height: 35px;
  background-color: #FFFFFF;

  .sort-item,
  .filter-item {
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100%;
    font-size: 14px;
    color: #4A5568;
    position: relative;

    &.active {
      color: var(--wo-biz-theme-color);
      font-weight: 500;
    }

    .sort-icon {
      width: 8px;
      height: 12px;
      margin-left: 2px;
    }

    .filter-icon {
      width: 12px;
      height: 12px;
      margin-left: 2px;
    }
  }
}
</style>
