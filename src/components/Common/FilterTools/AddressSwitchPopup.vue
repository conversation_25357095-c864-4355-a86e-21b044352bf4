<!--
===================== 地址切换弹窗组件 AddressSwitchPopup =======================
功能描述：
- 用于显示和切换用户收货地址的右侧弹出窗口
- 支持查看用户所有收货地址列表
- 支持切换默认收货地址，并实时更新到服务器
- 支持跳转到新建收货地址页面
- 根据业务代码动态显示不同的地址选中图标
- 提供地址变更事件通知父组件
============================================================================
-->

<template>
  <!-- 右侧弹出的地址切换弹窗容器 -->
  <van-popup
    v-model:show="isVisible"
    closeable
    position="right"
    :close-on-click-overlay="true"
    :style="{ width: '80%',height:'100vh',overflow:'hidden' }"
    @open="popupOpen"
    @close="popupClose"
  >
    <!-- 地址列表滚动区域 -->
    <div class="filter-area" ref="scrollContainer">
      <div class="filter-area-address-list">
        <!-- 地址项循环：显示每个收货地址的详细信息 -->
        <div class="filter-area-address-item" @click="onAddrClick(item)" v-for="item in addressList" :key="item.addressId">
          <!-- 地址选择状态图标区域 -->
          <div class="filter-area-address-item-left">
            <!-- 默认地址显示选中图标：根据业务代码动态切换图标 -->
            <img :src="addrSelectIcon" v-if="item.isDefault==='1'"/>
            <!-- 非默认地址显示普通地址图标 -->
            <img src="./assets/address.png" alt="" srcset="" v-else>
          </div>
          <!-- 地址详细信息区域 -->
          <div class="filter-area-address-item-right">
            <!-- 基础地址信息：省市区镇四级地址 -->
            <div class="base-address">
              <span>{{ item.provinceName }}</span>
              <span>{{ item.cityName }}</span>
              <span>{{ item.countyName }}</span>
              <span>{{ item.townName }}</span>
            </div>
            <!-- 详细地址信息：根据是否为默认地址应用不同样式 -->
            <div class="address-detail" :class="{'address-detail-select':item.isDefault==='1'}">{{ item.addrDetail }}</div>
          </div>
        </div>
      </div>
    </div>
    <!-- 底部操作按钮区域 -->
    <div class="filter-area-operator">
      <!-- 新建收货地址按钮：点击跳转到地址添加页面 -->
      <button class="btn-base btn3" @click="onAddClick">新建收货地址</button>
    </div>
  </van-popup>
</template>

<script setup>
import { ref, computed, watch, toRefs } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '@store/modules/user.js'
import { updateUserDefaultAddr } from '@api/index.js'
import { closeToast, showLoadingToast, showToast } from 'vant'
import { getBizCode } from '@utils/curEnv.js'
import woAddrSelect from './assets/wo-address-select.png'
import jdAddrSelect from './assets/jd-address-select.png'

// ===================== 外部依赖和工具初始化 =======================
// 路由实例：用于页面跳转
const router = useRouter()
// 用户状态管理：处理地址相关数据
const userStore = useUserStore()

// ===================== 组件属性定义和响应式处理 =======================
// 定义组件接收的属性配置
const props = defineProps({
  // 弹窗显示状态：控制弹窗的显示和隐藏
  show: {
    type: Boolean,
    default: false
  }
})

// 使用 toRefs 解构 props 以保持响应性
const { show } = toRefs(props)

// 定义组件向父组件发射的事件
const emit = defineEmits(['update:show', 'address-changed'])

// ===================== 弹窗显示状态管理 =======================
// 内部弹窗显示状态：双向绑定弹窗显示状态
const isVisible = computed({
  get: () => show.value,
  set: (value) => emit('update:show', value)
})

// ===================== 业务图标动态切换 =======================
// 地址选中图标：根据业务代码动态切换不同的选中图标
const addrSelectIcon = computed(() => (getBizCode() === 'ygjd' ? jdAddrSelect : woAddrSelect))

// ===================== 地址列表数据管理 =======================
// 地址列表数据：存储从服务器获取的用户地址列表
const addressList = ref([])
// 滚动容器引用：用于控制地址列表的滚动行为
const scrollContainer = ref(null)

// 监听弹窗显示状态变化：当弹窗打开时加载地址列表数据
watch(isVisible, async (newVal) => {
  if (newVal) {
    // 清空当前地址列表数据
    addressList.value = []
    // 显示加载提示
    showLoadingToast()
    try {
      // 强制刷新用户地址列表数据
      await userStore.queryAddrList({ force: true })

      // 滚动容器回到顶部位置
      if (scrollContainer.value) {
        scrollContainer.value.scrollTo({
          top: 0,
          behavior: 'smooth'
        })
      }

      // 获取存储中的地址列表数据
      const storeAddressList = userStore.addressList || []
      // 查找默认地址项
      const defaultItem = storeAddressList.find(item => item.isDefault === '1')

      // 将默认地址移动到列表第一位以便优先显示
      if (defaultItem && storeAddressList[0] !== defaultItem) {
        const defaultIndex = storeAddressList.indexOf(defaultItem)
        storeAddressList.unshift(...storeAddressList.splice(defaultIndex, 1))
      }

      // 更新组件内部地址列表数据
      addressList.value = storeAddressList
    } catch (error) {
      console.error('获取地址列表失败:', error)
      showToast('获取地址列表失败')
    } finally {
      // 关闭加载提示
      closeToast()
    }
  }
})

// ===================== 弹窗生命周期事件处理 =======================
// 弹窗打开事件处理：记录弹窗打开日志
const popupOpen = () => {
  console.log('地址切换弹窗打开')
}

// 弹窗关闭事件处理：记录弹窗关闭日志
const popupClose = () => {
  console.log('地址切换弹窗关闭')
}

// ===================== 地址操作事件处理 =======================
// 地址点击处理：切换默认地址并同步到服务器
const onAddrClick = async (item) => {
  // 如果点击的已经是默认地址，则不执行任何操作
  if (item.isDefault === '1') {
    return
  }

  // 更新本地地址列表状态：将所有地址设为非默认
  addressList.value.forEach(function (addr) {
    addr.isDefault = '0'
  })
  // 将点击的地址设为默认地址
  item.isDefault = '1'

  try {
    // 调用API更新服务器端默认地址
    const [err] = await updateUserDefaultAddr(item.addressId)
    if (err) {
      // 如果更新失败，恢复原始状态
      item.isDefault = '0'
      if (addressList.value && addressList.value.length > 0) {
        addressList.value[0].isDefault = '1'
      }
      showToast(err.msg)
      return
    }

    // 刷新用户存储中的默认地址数据
    await userStore.queryDefaultAddr({ force: true })
    showToast('更新地址成功')
    // 通知父组件地址已更改
    emit('address-changed')

    // 关闭弹窗
    isVisible.value = false
  } catch (error) {
    // 异常情况下恢复原始状态
    item.isDefault = '0'
    if (addressList.value && addressList.value.length > 0) {
      addressList.value[0].isDefault = '1'
    }
    console.error('更新默认地址失败:', error)
    showToast('更新默认地址失败')
  }
}

// 新建地址点击处理：跳转到地址添加页面
const onAddClick = () => {
  router.push('/address/add')
}
</script>

<style scoped lang="less">
.filter-area {
  position: relative;
  height: 80vh;
  margin-top: 50px;
  padding: 0 20px 0 20px;
  overflow: scroll;

  .filter-area-address-list {
    width: 100%;

    .filter-area-address-item {
      width: 100%;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: flex-start;
      margin-bottom: 10px;

      .filter-area-address-item-left {
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 10px;

        img {
          width: 15px;
          height: 15px;
          vertical-align: middle;
        }
      }

      .filter-area-address-item-right {
        flex: 1;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        height: 100%;

        .base-address {
          margin-bottom: 3px;
          font-size: 13px;
          line-height: 1.5;
          color: #718096;
        }

        .address-detail {
          font-size: 15px;
          color: #171E24;
          line-height: 1.5;
          display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
        word-break: break-all;
        }

        .address-detail-select {
          font-weight: 700;
        }
      }
    }
  }
}

.filter-area-operator {
  padding: 0 20px;
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: fixed;
  left: 50%;
  bottom: 0;
  transform: translateX(-50%);
  height: 65px;
  background: #FFFFFF;
  box-sizing: border-box;

  .btn-base {
    height: 40px;
    width: 103px;
    font-size: 18px;
    color: var(--wo-biz-theme-color);
    text-align: center;
    line-height: 18px;
    font-weight: 400;
    border: none;
  }

  .btn3 {
    flex: 1 !important;
    color: #FFFFFF;
    background-image: var(--wo-biz-theme-gradient-1);
    border-radius: 50px;
  }
}
</style>
