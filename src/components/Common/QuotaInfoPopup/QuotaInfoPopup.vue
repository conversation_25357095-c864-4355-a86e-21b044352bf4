<!--
/**
 * 慰问活动额度信息弹窗组件
 *
 * 主要功能：
 * 1. 展示用户的慰问活动额度信息，包括可用活动和不可用活动两个分类
 * 2. 支持分页展示，通过标签页切换查看不同状态的活动
 * 3. 提供活动详情展示，包括活动名称、有效期、余额等信息
 * 4. 支持特殊活动的积点详情展示，包括已发放、已使用、剩余积点
 * 5. 集成空状态展示，当无活动数据时显示友好提示
 * 6. 提供底部操作栏，支持确认关闭弹窗
 *
 * 技术特点：
 * - 使用van-popup组件实现底部弹窗效果
 * - 采用van-tabs组件实现标签页切换
 * - 支持响应式布局和滚动展示
 * - 集成金额格式化工具函数
 *
 * 使用场景：
 * - 用户查看慰问活动额度详情时
 * - 支付页面展示可用优惠活动时
 * - 活动管理相关的业务场景
 */
-->

<template>
  <!-- 慰问活动额度信息弹窗主容器 -->
  <div class="quota-info-popup">
    <!-- 底部弹窗容器，设置圆角和70%高度 -->
    <!-- 监听关闭事件，当用户点击遮罩或返回键时触发 -->
    <van-popup 
      class="quota-info-popup-content" 
      v-model:show="show" 
      round 
      position="bottom" 
      :style="{ height: '70%' }"
      @close="onClose"
    >
      <!-- 弹窗标题区域 -->
      <div class="quota-info-popup-title">慰问活动</div>
      
      <!-- 弹窗内容容器 -->
      <div class="quota-info-popup-container">
        <!-- 标签页容器 -->
        <div class="quota-info-popup-tabs">
          <!-- 活动分类标签页，支持可用和不可用活动切换 -->
          <!-- 监听标签页切换事件，同步当前激活状态 -->
          <van-tabs ref="popupTabs" v-model:active="active" @change="onChange">
            <!-- 可用活动标签页 -->
            <van-tab title="可用活动">
              <!-- 可用活动内容区域，当有活动数据时显示 -->
              <div 
                class="quota-info-content"
                v-if="availableActivityQuotaList && availableActivityQuotaList.length > 0"
              >
                <!-- 提示信息区域，告知用户可在支付时选择优惠 -->
                <div class="quota-info-tips">
                  <img class="tips-icon" src="./assets/tips.png" alt="tips">
                  <span class="tips-content">已为您展示可用优惠，可在支付时选择</span>
                </div>
                
                <!-- 活动详情列表容器，支持滚动展示 -->
                <div ref="quotaInfoDetail" class="quota-info-detail">
                  <!-- 普通活动列表展示，当无特殊积点活动时显示 -->
                  <template v-if="grantAmount <= 0">
                    <!-- 遍历可用活动列表，为每个活动创建卡片 -->
                    <div 
                      class="quota-info-card" 
                      v-for="item in availableActivityQuotaList" 
                      :key="item.activityNo"
                    >
                      <!-- 活动余额显示，使用fenToYuan转换分为元 -->
                      <div class="quota-money">{{ fenToYuan(item.balanceAmount) }}</div>
                      <!-- 活动基本信息区域 -->
                      <div class="quota-info">
                        <!-- 活动名称 -->
                        <h3 class="active-name">{{ item.activityName }}</h3>
                        <!-- 活动有效期 -->
                        <p class="active-validity-period">有效期：{{ item.startTime }} ~ {{ item.endTime }}</p>
                      </div>
                    </div>
                  </template>
                  
                  <!-- 特殊积点活动展示，当有积点发放时显示 -->
                  <template v-if="grantAmount > 0">
                    <div class="quota-info-special">
                      <!-- 特殊活动名称 -->
                      <h3 class="active-name">{{ availableActivityQuota.activityName }}</h3>
                      <!-- 积点详情卡片，展示发放、使用、剩余积点 -->
                      <div class="quota-money-card">
                        <!-- 已发放积点 -->
                        <div class="quota-money-item">
                          <div class="quota-money-number">{{ fenToYuan(availableActivityQuota.grantAmount) }}</div>
                          <div class="quota-money-type">已发放积点</div>
                        </div>
                        <!-- 已使用积点 -->
                        <div class="quota-money-item">
                          <div class="quota-money-number">{{ fenToYuan(availableActivityQuota.usedAmount) }}</div>
                          <div class="quota-money-type">已使用积点</div>
                        </div>
                        <!-- 剩余积点 -->
                        <div class="quota-money-item">
                          <div class="quota-money-number">{{ fenToYuan(availableActivityQuota.amount) }}</div>
                          <div class="quota-money-type">剩余积点</div>
                        </div>
                      </div>
                    </div>
                  </template>
                </div>
              </div>
              <!-- 无可用活动时的空状态展示 -->
              <van-empty v-else class="custom-image" :image="noDataImg" description="暂无活动" />
            </van-tab>
            
            <!-- 不可用活动标签页 -->
            <van-tab title="不可用活动">
              <!-- 不可用活动内容区域，当有不可用活动数据时显示 -->
              <div 
                class="quota-info-content quota-info-content-no"
                v-if="unAvailableActivityQuotaList && unAvailableActivityQuotaList.length > 0"
              >
                <!-- 不可用活动详情列表容器 -->
                <div ref="quotaInfoDetail" class="quota-info-detail">
                  <!-- 遍历不可用活动列表，为每个活动创建灰色卡片 -->
                  <div 
                    class="quota-info-card quota-info-card-no" 
                    v-for="item in unAvailableActivityQuotaList"
                    :key="item.activityNo"
                  >
                    <!-- 活动余额显示 -->
                    <div class="quota-money">{{ fenToYuan(item.balanceAmount) }}</div>
                    <!-- 活动信息区域，包含不可用原因 -->
                    <div class="quota-info">
                      <!-- 活动名称 -->
                      <h3 class="active-name">{{ item.activityName }}</h3>
                      <!-- 活动有效期 -->
                      <p class="active-validity-period">有效期：{{ item.startTime }} ~ {{ item.endTime }}</p>
                      <!-- 不可用原因说明 -->
                      <p class="active-unavailable">不可用原因：活动不适用于该商城</p>
                    </div>
                  </div>
                </div>
              </div>
              <!-- 无不可用活动时的空状态展示 -->
              <van-empty v-else class="custom-image" :image="noDataImg" description="暂无活动" />
            </van-tab>
          </van-tabs>
        </div>
      </div>
      
      <!-- 底部操作栏，提供确定按钮 -->
      <WoActionBar class="quota-info-op">
        <!-- 确定按钮，点击关闭弹窗 -->
        <WoButton type="primary" block size="xlarge" @click="onClose">确定</WoButton>
      </WoActionBar>
    </van-popup>
  </div>
</template>
<script setup>
import { ref, computed, watch, toRefs } from 'vue'
import { fenToYuan } from '@/utils/amount'
import { get, defaultTo } from 'lodash'
import noActive from './assets/no-active.png'
import WoButton from '@/components/WoElementCom/WoButton/WoButton.vue'
import WoActionBar from '@/components/WoElementCom/WoActionBar.vue'

// ==================== 组件属性定义 ====================
// 定义组件接收的props参数
const props = defineProps({
  // v-model绑定的值，控制弹窗显示状态
  modelValue: { type: Boolean, default: false },
  // 可用活动额度数据对象
  availableActivityQuota: {
    type: Object,
    default: () => ({})
  },
  // 不可用活动额度数据对象
  unAvailableActivityQuota: {
    type: Object,
    default: () => ({})
  }
})

// 使用toRefs解构props，保持响应性
const { modelValue, availableActivityQuota, unAvailableActivityQuota } = toRefs(props)

// 定义组件向父组件发射的事件
const emit = defineEmits(['update:modelValue', 'change'])

// ==================== 弹窗显示控制 ====================
// 弹窗显示状态，与modelValue双向绑定
const show = ref(false)

// 监听modelValue变化，同步弹窗显示状态
watch(
  modelValue,
  (val) => {
    show.value = val
  },
  { immediate: true }
)

// 关闭弹窗的处理函数
// 当用户点击确定按钮或其他关闭操作时触发
const onClose = () => {
  // 设置弹窗为不可见状态
  show.value = false
  // 向父组件发射update:modelValue事件，实现v-model双向绑定
  emit('update:modelValue', false)
}

// ==================== 标签页切换控制 ====================
// 当前激活的标签页索引
const active = ref(0)

// 标签页切换的处理函数
// 当用户点击不同标签页时触发
const onChange = (name) => {
  // 更新当前激活的标签页索引
  active.value = name
  // 向父组件发射change事件，通知标签页变化
  emit('change', name)
}

// ==================== 数据处理和计算 ====================
// 空状态图片资源
const noDataImg = ref(noActive)

// 计算属性：获取可用活动列表数据
// 从availableActivityQuota对象中提取quotaInfo数组
const availableActivityQuotaList = computed(() => {
  const quotaInfo = get(availableActivityQuota.value, 'quotaInfo', [])
  return defaultTo(quotaInfo, [])
})

// 计算属性：获取不可用活动列表数据
// 从unAvailableActivityQuota对象中提取quotaInfo数组
const unAvailableActivityQuotaList = computed(() => {
  const quotaInfo = get(unAvailableActivityQuota.value, 'quotaInfo', [])
  return defaultTo(quotaInfo, [])
})

// 计算属性：获取已发放积点金额
// 用于判断是否显示特殊积点活动展示模式
const grantAmount = computed(() => {
  return availableActivityQuota.value?.grantAmount || 0
})
</script>

<style scoped lang="less">
.quota-info-popup :deep(.van-popup) {
  box-sizing: border-box;
  padding: 16px;
}

.quota-info-popup {
  height: 100%;

  .quota-info-popup-content {
    display: flex;
    flex-direction: column;
    height: 100%;

    .quota-info-op {
      width: 100%;
      margin-top: 16px;
      flex-shrink: 0;

      // 覆盖WoActionBar的固定定位，在弹窗中使用相对定位
      :deep(.action-bar) {
        position: relative;
        left: auto;
        right: auto;
        bottom: auto;
        padding: 0;
        background-color: transparent;
        box-shadow: none;
        z-index: auto;
        min-height: auto;
      }
    }
  }

  .quota-info-popup-title {
    margin-bottom: 16px;
    font-size: 18px;
    color: #171E24;
    font-weight: 600;
    text-align: center;
    flex-shrink: 0;
  }

  .quota-info-popup-container {
    width: 100%;
    flex: 1;
    overflow: hidden;
    min-height: 0;

    .quota-info-popup-tabs {
      width: 100%;
      height: 100%;

      :deep(.van-tabs) {
        height: 100%;
        display: flex;
        flex-direction: column;
      }

      :deep(.van-tabs__wrap) {
        height: 44px;
        flex-shrink: 0;
      }

      :deep(.van-tabs__content) {
        flex: 1;
        overflow: hidden;
      }

      :deep(.van-tab__pane) {
        height: 100%;
        overflow: hidden;
      }

      :deep(.van-tabs__line) {
        width: 30px !important;
        height: 2px !important;
        background: var(--wo-biz-theme-color) !important;
        border-radius: 1px !important;
      }

      :deep(.van-tab--active .van-tab__text) {
        font-size: 15px;
        color: var(--wo-biz-theme-color) !important;
        line-height: 1.5;
        font-weight: 600 !important;
      }

      :deep(.van-tab .van-tab__text) {
        font-size: 15px;
        line-height: 1.5;
        font-weight: 400;
        color: #4A5568;
      }

      :deep(.van-empty) {
        height: 100% !important;
        display: flex !important;
        flex-direction: column !important;
        justify-content: center !important;
        align-items: center !important;
        padding: 40px 20px !important;
      }
    }

    .quota-info-content {
      height: 100%;
      display: flex;
      flex-direction: column;
      padding: 12px 0 0;
      box-sizing: border-box;

      .quota-info-tips {
        display: flex;
        align-items: center;
        padding: 10px 12px;
        margin-bottom: 12px;
        background: #fff7f0;
        border: 1px solid #ffe4d1;
        border-radius: 6px;
        flex-shrink: 0;

        .tips-icon {
          margin-right: 8px;
          width: 16px;
          height: 16px;
          flex-shrink: 0;
        }

        .tips-content {
          font-size: 13px;
          color: var(--wo-biz-theme-color);
          font-weight: 400;
          line-height: 1.4;
        }
      }

      .quota-info-detail {
        flex: 1;
        overflow-y: auto;
        padding-right: 2px;
        min-height: 0;

        &::-webkit-scrollbar {
          width: 3px;
        }

        &::-webkit-scrollbar-track {
          background: #F8F9FA;
          border-radius: 2px;
        }

        &::-webkit-scrollbar-thumb {
          background: #CBD5E0;
          border-radius: 2px;
        }

        &::-webkit-scrollbar-thumb:hover {
          background: #718096;
        }

        .quota-info-card {
          position: relative;
          box-sizing: border-box;
          display: flex;
          align-items: center;
          margin-bottom: 12px;
          background: #FFFFFF;
          border: 1px solid #E2E8EE;
          border-radius: 8px;
          overflow: hidden;
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
          transition: all 0.2s ease;

          &:hover {
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
          }

          &:last-child {
            margin-bottom: 0;
          }

          // 左侧红色装饰条
          &::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            bottom: 0;
            width: 3px;
            background: var(--wo-biz-theme-color);
          }

          .quota-money {
            display: flex;
            align-items: baseline;
            justify-content: center;
            min-width: 90px;
            padding: 16px 12px;
            font-size: 20px;
            color: var(--wo-biz-theme-color);
            line-height: 1.2;
            font-weight: 700;
            flex-shrink: 0;
            text-align: center;

            &::before {
              content: '￥';
              font-size: 14px;
              font-weight: 500;
              margin-right: 2px;
            }
          }

          .quota-info {
            flex: 1;
            min-width: 0;
            padding: 16px 16px 16px 12px;

            .active-name {
              margin-bottom: 8px;
              font-size: 15px;
              color: #171E24;
              line-height: 1.4;
              font-weight: 500;
              overflow: hidden;
              display: -webkit-box;
              -webkit-line-clamp: 2;
              line-clamp: 2;
              -webkit-box-orient: vertical;
              text-overflow: ellipsis;
              word-break: break-all;
            }

            .active-validity-period {
              font-size: 12px;
              color: #718096;
              font-weight: 400;
              line-height: 1.4;
              margin-bottom: 0;
            }

            .active-unavailable {
              font-size: 12px;
              color: #4A5568;
              line-height: 1.4;
              font-weight: 400;
              margin-top: 4px;
            }
          }
        }

        .quota-info-special {
          position: relative;
          box-sizing: border-box;
          padding: 16px;
          margin-bottom: 12px;
          background: #FFFFFF;
          border: 1px solid #E2E8EE;
          border-radius: 8px;
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
          transition: all 0.2s ease;

          &:hover {
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
          }

          &:last-child {
            margin-bottom: 0;
          }

          // 左侧红色装饰条
          &::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            bottom: 0;
            width: 3px;
            background: var(--wo-biz-theme-color);
          }

          .active-name {
            margin-bottom: 16px;
            font-size: 16px;
            color: #171E24;
            line-height: 1.4;
            font-weight: 600;
            text-align: center;
            overflow: hidden;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            line-clamp: 2;
            -webkit-box-orient: vertical;
            text-overflow: ellipsis;
            word-break: break-all;
          }

          .quota-money-card {
            display: flex;
            justify-content: space-between;
            align-items: center;
            gap: 12px;
            background: #F8F9FA;
            border-radius: 6px;
            padding: 12px 8px;

            .quota-money-item {
              flex: 1;
              text-align: center;
              position: relative;

              &:not(:last-child)::after {
                content: '';
                position: absolute;
                right: -6px;
                top: 50%;
                transform: translateY(-50%);
                width: 1px;
                height: 24px;
                background: #E2E8EE;
              }

              .quota-money-number {
                display: flex;
                align-items: baseline;
                justify-content: center;
                font-size: 18px;
                color: var(--wo-biz-theme-color);
                font-weight: 700;
                margin-bottom: 4px;
                line-height: 1.2;

                &::before {
                  content: '￥';
                  font-size: 13px;
                  font-weight: 500;
                  margin-right: 1px;
                }
              }

              .quota-money-type {
                font-size: 12px;
                color: #718096;
                line-height: 1.2;
                font-weight: 400;
              }
            }
          }
        }

        .quota-info-card-no {
          background: #F8F9FA;
          border-color: #E2E8EE;
          opacity: 0.7;

          &::before {
            background: #CBD5E0;
          }

          .quota-money {
            color: #718096;

            &::before {
              color: #718096;
            }
          }

          .quota-info {
            .active-name {
              color: #718096;
            }

            .active-validity-period {
              color: #CBD5E0;
            }

            .active-unavailable {
              color: #718096;
            }
          }
        }
      }
    }

    .quota-info-content-no {
      padding-top: 12px;
    }
  }
}
</style>
