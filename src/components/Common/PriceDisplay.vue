<!--
价格显示组件 - PriceDisplay

功能描述：
- 支持单个价格和价格区间的显示
- 提供多种尺寸规格（small、medium、large）
- 支持多种颜色主题（primary、orange、red、white）
- 支持加粗显示和自定义区间标签
- 自动格式化价格，分离整数和小数部分
- 处理空值和异常数据的显示
-->

<template>
  <!-- 价格区间显示容器 - 当存在高价或低价时显示价格范围 -->
  <span v-if="isRange" class="price-display price-range" :class="[sizeClass, colorClass, { 'price-bold': bold }]">
    <!-- 区间标签 - 可选的价格区间前缀文本 -->
    <span class="range-label" v-if="rangeLabel">{{ rangeLabel }}：</span>
    <!-- 低价格项 - 价格区间的起始价格 -->
    <span class="price-item">
      <!-- 人民币符号 -->
      <span class="currency">¥</span>
      <!-- 低价格整数部分 -->
      <span class="integer">{{ lowIntegerPart }}</span>
      <!-- 低价格小数部分 - 仅在存在小数时显示 -->
      <span class="decimal" v-if="lowDecimalPart">.{{ lowDecimalPart }}</span>
    </span>
    <!-- 价格区间分隔符 - 连接低价和高价的横线 -->
    <span class="range-separator"> - </span>
    <!-- 高价格项 - 价格区间的结束价格 -->
    <span class="price-item">
      <!-- 人民币符号 -->
      <span class="currency">¥</span>
      <!-- 高价格整数部分 -->
      <span class="integer">{{ highIntegerPart }}</span>
      <!-- 高价格小数部分 - 仅在存在小数时显示 -->
      <span class="decimal" v-if="highDecimalPart">.{{ highDecimalPart }}</span>
    </span>
  </span>
  <!-- 单个价格显示容器 - 当只有单一价格时显示 -->
  <span v-else class="price-display" :class="[sizeClass, colorClass, { 'price-bold': bold }]">
    <!-- 人民币符号 -->
    <span class="currency">¥</span>
    <!-- 价格整数部分 -->
    <span class="integer">{{ integerPart }}</span>
    <!-- 价格小数部分 - 仅在存在小数时显示 -->
    <span class="decimal" v-if="decimalPart">.{{ decimalPart }}</span>
  </span>
</template>

<script setup>
import { computed, toRefs } from 'vue'

// ===================== 组件属性定义 ======================
// 定义组件接收的props参数
const props = defineProps({
  // 单个价格值，支持数字或字符串格式
  price: {
    type: [Number, String],
    required: false
  },
  // 价格区间的高价值，用于显示价格范围的上限
  highPrice: {
    type: [Number, String],
    required: false
  },
  // 价格区间的低价值，用于显示价格范围的下限
  lowPrice: {
    type: [Number, String],
    required: false
  },
  // 价格区间的标签文本，显示在价格前面
  rangeLabel: {
    type: String,
    default: ''
  },
  // 价格显示尺寸，控制字体大小
  size: {
    type: String,
    default: 'medium',
    validator: value => ['small', 'medium', 'large'].includes(value)
  },
  // 价格显示颜色主题
  color: {
    type: String,
    default: 'primary',
    validator: value => ['primary', 'orange', 'red', 'white'].includes(value)
  },
  // 是否加粗显示价格
  bold: {
    type: Boolean,
    default: true
  }
})

// 使用toRefs解构props，保持响应性
const { price, highPrice, lowPrice, rangeLabel, size, color, bold } = toRefs(props)

// ===================== 价格类型判断 ======================
// 判断是否为价格区间显示模式
const isRange = computed(() => {
  // 当存在高价或低价时，启用区间显示模式
  return (highPrice.value !== null && highPrice.value !== undefined) ||
    (lowPrice.value !== null && lowPrice.value !== undefined)
})

// ===================== 价格格式化工具函数 ======================
// 格式化价格的通用函数，处理各种输入格式并转换为标准显示格式
const formatPrice = (priceValue) => {  
  // 处理空值情况，返回占位符
  if (priceValue === null || priceValue === undefined) {
    return '--.-'
  }
  // 处理字符串格式的价格（如 "¥100"），提取数字部分
  if (typeof priceValue === 'string') {
    const numericPrice = parseFloat(priceValue.replace(/[^\d.]/g, ''))
    return isNaN(numericPrice) ? '--.-' : (numericPrice/100).toFixed(2)
  }
  // 处理数字格式，假设传入的是分为单位，转换为元并保留两位小数
  const finalPrice = priceValue / 100
  return finalPrice.toFixed(2)
}

// ===================== 单个价格计算属性 ======================
// 格式化后的单个价格字符串
const priceFormatted = computed(() => formatPrice(price.value))

// 单个价格的整数部分
const integerPart = computed(() => {
  // 处理空值情况，返回占位符
  if (price.value === null || price.value === undefined) {
    return '--'
  }
  // 从格式化价格中提取整数部分
  return priceFormatted.value.split('.')[0]
})

// 单个价格的小数部分
const decimalPart = computed(() => {
  // 处理空值情况，返回占位符
  if (price.value === null || price.value === undefined) {
    return '-'
  }
  // 从格式化价格中提取小数部分
  return priceFormatted.value.split('.')[1]
})

// ===================== 高价格计算属性 ======================
// 格式化后的高价格字符串
const highPriceFormatted = computed(() => formatPrice(highPrice.value))

// 高价格的整数部分
const highIntegerPart = computed(() => {
  // 处理空值情况，返回占位符
  if (highPrice.value === null || highPrice.value === undefined) {
    return '--'
  }
  // 从格式化高价格中提取整数部分
  return highPriceFormatted.value.split('.')[0]
})

// 高价格的小数部分
const highDecimalPart = computed(() => {
  // 处理空值情况，返回占位符
  if (highPrice.value === null || highPrice.value === undefined) {
    return '-'
  }
  // 从格式化高价格中提取小数部分
  return highPriceFormatted.value.split('.')[1]
})

// ===================== 低价格计算属性 ======================
// 格式化后的低价格字符串
const lowPriceFormatted = computed(() => formatPrice(lowPrice.value))

// 低价格的整数部分
const lowIntegerPart = computed(() => {
  // 处理空值情况，返回占位符
  if (lowPrice.value === null || lowPrice.value === undefined) {
    return '--'
  }
  // 从格式化低价格中提取整数部分
  return lowPriceFormatted.value.split('.')[0]
})

// 低价格的小数部分
const lowDecimalPart = computed(() => {
  // 处理空值情况，返回占位符
  if (lowPrice.value === null || lowPrice.value === undefined) {
    return '-'
  }
  // 从格式化低价格中提取小数部分
  return lowPriceFormatted.value.split('.')[1]
})

// ===================== 样式类计算属性 ======================
// 根据尺寸属性生成对应的CSS类名
const sizeClass = computed(() => `price-${size.value}`)
// 根据颜色属性生成对应的CSS类名
const colorClass = computed(() => `price-${color.value}`)
</script>

<style scoped lang="less">
.price-display {
  font-family: 'D-DIN-PRO SemiBold';
  display: inline-flex;
  align-items: baseline;

  .currency {
    margin-right: 2px;
    font-weight: 400;
  }

  .integer {
    font-weight: 400;
  }

  .decimal {
    font-weight: 400;
  }

  // 尺寸变体
  &.price-small {
    .currency {
      font-size: 12px;
    }

    .integer {
      font-size: 16px;
    }

    .decimal {
      font-size: 12px;
    }
  }

  &.price-medium {
    .currency {
      font-size: 14px;
    }

    .integer {
      font-size: 20px;
    }

    .decimal {
      font-size: 14px;
    }
  }

  &.price-large {
    .currency {
      font-size: 16px;
    }

    .integer {
      font-size: 24px;
    }

    .decimal {
      font-size: 16px;
    }
  }

  // 颜色变体
  &.price-primary {
    color: #171E24;
  }

  &.price-orange {
    color: var(--wo-biz-theme-color);
  }

  &.price-red {
    color: var(--wo-biz-theme-color);
  }

  &.price-white {
    color: #FFFFFF;
  }

  // 加粗变体
  &.price-bold {
    .currency {
      font-weight: 600;
    }

    .integer {
      font-weight: 700;
    }

    .decimal {
      font-weight: 700;
    }
  }

  // 价格区间样式
  &.price-range {
    display: inline-flex;
    align-items: baseline;

    .range-label {
      //margin-right: 4px;
      font-size: 12px;
      font-weight: 400;
    }

    .price-item {
      display: inline-flex;
      align-items: baseline;
    }

    .range-separator {
      margin: 0 4px;
      font-size: inherit;
      font-weight: 400;
    }
  }
}
</style>
