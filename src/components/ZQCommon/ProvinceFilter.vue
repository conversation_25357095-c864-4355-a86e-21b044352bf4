<!--
/**
 * 省分筛选触发器组件
 *
 * 主要功能：
 * 1. 为特定角色用户（roleType为'4'的企业管理员）提供省分筛选功能的入口按钮
 * 2. 根据用户角色权限控制筛选按钮的显示与隐藏
 * 3. 触发省分服务选择器弹窗，并处理用户的筛选确认操作
 * 4. 支持自定义按钮标题文本和外部控制显示状态
 *
 * 使用场景：
 * - 企业管理员需要按省分筛选商品或服务时
 * - 需要根据用户权限动态显示筛选功能时
 */
-->

<template>
  <div>
    <!-- 省分筛选触发按钮区域 -->
    <!-- 只有当shouldShow为true时才显示筛选按钮，点击后打开选择器弹窗 -->
    <div
      class="global-filtering"
      @click="showDrawer = true"
      v-if="shouldShow"
    >
      <!-- 显示筛选按钮的标题文本，支持通过props自定义 -->
      <div class="global-filtering-title">{{ title }}</div>
      <!-- 筛选图标，使用静态资源图片 -->
      <img
        class="global-filtering-icon"
        src="@/static/images/filtering.png"
        alt="筛选图标"
      >
    </div>

    <!-- 省分服务选择器弹窗组件 -->
    <!-- 使用v-if进行条件渲染，只有在需要显示时才创建组件实例，提高性能 -->
    <!-- 通过v-model双向绑定控制弹窗显示状态 -->
    <!-- 监听confirm事件，当用户确认选择后触发父组件的处理逻辑 -->
    <ZqSelectFilter
      v-if="showDrawer"
      v-model="showDrawer"
      @confirm="handleSelectionConfirm"
    />
  </div>
</template>

<script setup>
import { ref, computed, toRefs } from 'vue'
import ZqSelectFilter from '@components/ZQCommon/ZQSelectFilter/ZQSelectFilter.vue'
import { getEnterpriseManagerInfo } from '@/utils/zqInfo'

// ==================== 组件属性定义 ====================
// 定义组件接收的props参数
const props = defineProps({
  // 筛选按钮显示的标题文本
  title: {
    type: String,
    default: '省分在售商品'
  },
  // 用户角色类型，用于权限控制
  roleType: {
    type: String,
    default: ''
  },
  // 控制组件整体是否可见
  visible: {
    type: Boolean,
    default: true
  }
})

// 使用toRefs解构props，保持响应性
const { title, roleType, visible } = toRefs(props)

// 定义组件向父组件发射的事件
const emit = defineEmits(['confirm'])

// ==================== 弹窗显示控制 ====================
// 控制省分选择器弹窗的显示状态
// 初始值为false，表示弹窗默认不显示
const showDrawer = ref(false)

// ==================== 权限控制逻辑 ====================
// 计算是否应该显示筛选按钮
// 基于用户角色权限和组件可见性进行判断
const shouldShow = computed(() => {
  // 如果组件被设置为不可见，直接返回false
  if (!visible.value) return false

  // 如果外部传入了roleType参数，优先使用传入的值进行判断
  if (roleType.value) {
    // 只有roleType为'4'的用户（企业管理员）才能看到筛选按钮
    return roleType.value === '4'
  }

  // 如果没有传入roleType，从工具函数获取当前用户的角色信息
  const { roleType: currentRoleType = '' } = getEnterpriseManagerInfo() || {}

  // 判断当前用户是否为企业管理员角色
  return currentRoleType === '4'
})

// ==================== 事件处理函数 ====================
// 处理用户在选择器中确认选择的操作
// 将选择结果通过emit传递给父组件
const handleSelectionConfirm = (selection) => {
  // 向父组件通信事件，传递用户的选择数据
  emit('confirm', selection)
}
</script>

<style scoped lang="less">
.global-filtering {
  flex-shrink: 0;
  width: 100%;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  padding: 10px;
  background: #fff;

  .global-filtering-title {
    font-size: 15px;
    color: #5A6066;
  }

  .global-filtering-icon {
    margin-left: 10px;
    width: 18px;
    height: 18px;
  }
}
</style>
