<!--
/**
 * 省分服务选择器弹窗组件
 *
 * 主要功能：
 * 1. 提供省分和服务商的双重筛选功能，支持用户同时选择省分和服务商
 * 2. 采用右侧滑出的弹窗形式，提供良好的用户交互体验
 * 3. 支持"全部"选项，允许用户清除特定筛选条件
 * 4. 实现临时选择机制，用户可以在确认前自由切换选择，避免误操作
 * 5. 集成Pinia状态管理，自动同步选择状态到全局store
 * 6. 支持重置功能，一键清除所有筛选条件
 *
 * 技术特点：
 * - 使用van-popup组件实现弹窗效果
 * - 采用网格布局展示选项，支持响应式设计
 * - 实现双向数据绑定，支持v-model模式
 * - 集成状态管理，数据持久化存储
 *
 * 使用场景：
 * - 企业管理员需要按省分和服务商筛选数据时
 * - 需要提供多维度筛选功能的业务场景
 */
-->

<template>
  <!-- 右侧滑出弹窗容器 -->
  <!-- 设置弹窗从右侧滑出，占据75%宽度，100%高度 -->
  <!-- 启用安全区域底部插入，适配有底部导航栏的设备 -->
  <!-- 监听弹窗关闭事件，重置临时选择状态 -->
  <van-popup
    v-model:show="visible"
    position="right"
    :style="{ height: '100%', width: '75%' }"
    safe-area-inset-bottom
    @closed="onPopupClosed"
  >
    <!-- 省分选择器主容器 -->
    <div class="province-drawer">
      <!-- 弹窗头部区域 -->
      <div class="drawer-header">
        <!-- 关闭按钮，点击关闭弹窗 -->
        <div class="drawer-close" @click="handleClose">×</div>
      </div>

      <!-- 弹窗内容区域，包含省分选择和服务商选择 -->
      <div class="drawer-content">
        <!-- 省分选择区域 -->
        <div class="service-section">
          <!-- 省分选择标题 -->
          <div class="section-title">选择省分</div>
          <!-- 省分选项网格容器 -->
          <div class="province-grid">
            <!-- "全部"选项，用于清除省分筛选条件 -->
            <!-- 当tempSelectedAreaId为空字符串时显示为选中状态 -->
            <div
              class="province-item"
              :class="{ active: tempSelectedAreaId === '' }"
              @click="selectProvince('')"
            >
              <div class="province-name">全部</div>
            </div>

            <!-- 具体省分选项列表 -->
            <!-- 遍历provinceList数组，为每个省分创建选项 -->
            <!-- 根据tempSelectedAreaId判断当前选中状态 -->
            <div
              class="province-item"
              v-for="province in provinceList"
              :key="province.areaId"
              :class="{ active: tempSelectedAreaId === province.areaId }"
              @click="selectProvince(province.areaId)"
            >
              <!-- 显示省分名称 -->
              <div class="province-name">{{ province.areaName }}</div>
            </div>
          </div>
        </div>

        <!-- 服务商选择区域 -->
        <div class="service-section">
          <!-- 服务商选择标题 -->
          <div class="section-title">选择服务商</div>
          <!-- 服务商选项网格容器 -->
          <div class="service-grid">
            <!-- "全部"选项，用于清除服务商筛选条件 -->
            <!-- 当tempSelectedIsvId为空字符串时显示为选中状态 -->
            <div
              class="service-item"
              :class="{ active: tempSelectedIsvId === '' }"
              @click="selectService('')"
            >
              <div class="service-name">全部</div>
            </div>

            <!-- 具体服务商选项列表 -->
            <!-- 遍历serviceList数组，为每个服务商创建选项 -->
            <!-- 根据tempSelectedIsvId判断当前选中状态 -->
            <div
              class="service-item"
              v-for="service in serviceList"
              :key="service.isvId"
              :class="{ active: tempSelectedIsvId === service.code }"
              @click="selectService(service.code)"
            >
              <!-- 显示服务商名称 -->
              <div class="service-name">{{ service.name }}</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 底部操作按钮区域 -->
      <div class="action-buttons">
        <!-- 重置按钮，清除所有选择并立即确认 -->
        <div class="reset-btn" @click="resetSelection">重置</div>
        <!-- 确认按钮，应用当前选择并关闭弹窗 -->
        <div class="confirm-btn" @click="confirmSelection">确定</div>
      </div>
    </div>
  </van-popup>
</template>

<script setup>
import { computed, onMounted, ref, watch, toRefs } from 'vue'
import { useProvinceServiceStore } from '@store/modules/provinceService.js'

// ==================== 组件属性定义 ====================
// 定义组件接收的props参数
const props = defineProps({
  // v-model绑定的值，控制弹窗显示状态
  modelValue: {
    type: Boolean,
    default: false
  }
})

// 使用toRefs解构props，保持响应性
const { modelValue } = toRefs(props)

// 定义组件向父组件发射的事件
const emit = defineEmits(['update:modelValue', 'confirm'])

// ==================== 状态管理集成 ====================
// 获取省分服务状态管理store实例
const provinceServiceStore = useProvinceServiceStore()

// ==================== 临时选择状态管理 ====================
// 临时选择的省分ID，用于在用户确认前保存选择状态
// 避免直接修改store中的数据，提供更好的用户体验
const tempSelectedAreaId = ref('')

// 临时选择的服务商ID，用于在用户确认前保存选择状态
const tempSelectedIsvId = ref('')

// 省分选择处理函数
// 当用户点击省分选项时触发
const selectProvince = (areaId) => {
  // 更新临时选择的省分ID
  tempSelectedAreaId.value = areaId
}

// 服务商选择处理函数
// 当用户点击服务商选项时触发
const selectService = (code) => {
  // 更新临时选择的服务商ID
  tempSelectedIsvId.value = code
}


// ==================== 弹窗显示控制 ====================
// 计算属性：控制弹窗的显示状态，实现v-model双向绑定
const visible = computed({
  // getter：返回props中的modelValue值
  get() {
    return modelValue.value
  },
  // setter：当弹窗状态改变时，向父组件发射update:modelValue事件
  set(val) {
    emit('update:modelValue', val)
  }
})

// 弹窗关闭时的处理函数
// 当用户点击遮罩层或其他方式关闭弹窗时触发
const onPopupClosed = () => {
  // 恢复临时选择状态为store中的实际值，丢弃未确认的更改
  tempSelectedAreaId.value = provinceServiceStore.selectedAreaId
  tempSelectedIsvId.value = provinceServiceStore.selectedIsvId
}

// 手动关闭弹窗的处理函数
// 当用户点击关闭按钮时触发
const handleClose = () => {
  // 设置弹窗为不可见状态
  visible.value = false
}


// ==================== 数据获取和计算 ====================
// 计算属性：获取省分列表数据
// 从store中获取省分列表，自动响应数据变化
const provinceList = computed(() => provinceServiceStore.provinceList)

// 计算属性：获取服务商列表数据
// 从store中获取服务商列表，自动响应数据变化
const serviceList = computed(() => provinceServiceStore.serviceList)

// 计算属性：根据临时选择状态获取省分名称
// 用于在确认时提供可读的省分名称
const selectedProvinceName = computed(() => {
  // 如果没有选择省分，返回"全部"
  if (!tempSelectedAreaId.value) return '全部'

  // 在省分列表中查找对应的省分对象
  const province = provinceList.value.find(p => p.areaId === tempSelectedAreaId.value)

  // 返回省分名称，如果找不到则返回"全部"
  return province ? province.areaName : '全部'
})

// 计算属性：根据临时选择状态获取服务商名称
// 用于在确认时提供可读的服务商名称
const selectedServiceName = computed(() => {
  // 如果没有选择服务商，返回"全部"
  if (!tempSelectedIsvId.value) return '全部'

  // 在服务商列表中查找对应的服务商对象
  const service = serviceList.value.find(s => s.code === tempSelectedIsvId.value)

  // 返回服务商名称，如果找不到则返回"全部"
  return service ? service.name : '全部'
})

// 获取服务商列表的异步函数
// 调用store中的方法从服务器获取服务商数据
const fetchServiceList = () => {
  return provinceServiceStore.fetchServiceList()
}

// ==================== 用户操作处理 ====================
// 重置选择的处理函数
// 当用户点击重置按钮时触发
const resetSelection = () => {
  // 清空临时选择状态，恢复到"全部"状态
  tempSelectedAreaId.value = ''
  tempSelectedIsvId.value = ''

  // 立即触发确认逻辑，应用重置结果
  confirmSelection()
}

// 确认选择的处理函数
// 当用户点击确定按钮或重置后触发
const confirmSelection = () => {
  // 将临时选择状态同步到store中，持久化用户的选择
  provinceServiceStore.selectProvince(tempSelectedAreaId.value)
  provinceServiceStore.selectService(tempSelectedIsvId.value)

  // 关闭弹窗
  visible.value = false

  // 向父组件发射confirm事件，传递选择结果
  emit('confirm', {
    areaId: tempSelectedAreaId.value,        // 选择的省分ID
    isvId: tempSelectedIsvId.value,          // 选择的服务商ID
    provinceName: selectedProvinceName.value, // 选择的省分名称
    serviceName: selectedServiceName.value    // 选择的服务商名称
  })
}

// ==================== 生命周期和监听器 ====================
// 监听弹窗显示状态变化
// 当弹窗打开时进行初始化操作
watch(visible, async (newVal) => {
  if (newVal) {
    // 弹窗打开时，将store中的当前选择值复制到临时状态
    // 确保临时状态与实际状态同步
    tempSelectedAreaId.value = provinceServiceStore.selectedAreaId
    tempSelectedIsvId.value = provinceServiceStore.selectedIsvId

    // 检查服务商列表是否为空，如果为空则从服务器获取数据
    if (!serviceList.value || serviceList.value.length === 0) {
      await fetchServiceList()
    }
  }
})

// 组件挂载时的初始化操作
onMounted(() => {
  // 初始化临时选择状态，与store中的当前值保持一致
  tempSelectedAreaId.value = provinceServiceStore.selectedAreaId
  tempSelectedIsvId.value = provinceServiceStore.selectedIsvId
})
</script>

<style lang="less" scoped>
.province-drawer {
  display: flex;
  flex-direction: column;
  height: 100%;

  .drawer-header {
    display: flex;
    justify-content: flex-end;
    align-items: center;
    padding: 10px;

    .drawer-close {
      font-size: 24px;
      color: #999999;
      cursor: pointer;
    }
  }

  .drawer-content {
    padding: 10px;
    flex: 1;
    overflow: auto;

    .section-title {
      font-size: 16px;
      font-weight: 500;
      color: #666666;
      margin-bottom: 16px;
    }

    .province-grid {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      gap: 12px;
      margin-bottom: 30px;

      .province-item {
        padding: 5px;
        text-align: center;
        background: #F8F9FA;
        border-radius: 4px;
        cursor: pointer;
        box-sizing: border-box;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;

        &.active {
          background: var(--wo-biz-theme-color);
          color: #FFFFFF;
        }

        .province-name {
          font-size: 13px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }
    }

    .service-section {
      .section-title {
        font-size: 16px;
        font-weight: 500;
        color: #333333;
        margin-bottom: 16px;
      }

      .service-grid {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: 12px;
        margin-bottom: 30px;

        .service-item {
          padding: 6px;
          text-align: center;
          background: #f8f9fa;
          border-radius: 4px;
          cursor: pointer;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;

          &.active {
            background: var(--wo-biz-theme-color);
            color: #FFFFFF;
          }

          .service-name {
            font-size: 13px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
        }
      }
    }
  }

  .action-buttons {
    display: flex;
    gap: 12px;
    padding: 10px;
    box-sizing: border-box;

    .reset-btn,
    .confirm-btn {
      flex: 1;
      padding: 8px;
      text-align: center;
      border-radius: 4px;
      cursor: pointer;
      font-size: 16px;
      box-sizing: border-box;
    }

    .reset-btn {
      background: #F8F9FA;
      color: #666666;
    }

    .confirm-btn {
      background: var(--wo-biz-theme-color);
      color: #FFFFFF;
    }
  }
}
</style>
