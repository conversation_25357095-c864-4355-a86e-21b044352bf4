<!--
/**
 * 空状态展示组件
 *
 * 主要功能：
 * 1. 提供统一的空状态展示界面，用于无数据场景
 * 2. 支持多种预设图片类型和自定义图片URL
 * 3. 支持自定义图片尺寸，适配不同展示需求
 * 4. 支持自定义描述文字，提供用户友好的提示信息
 * 5. 提供操作按钮插槽，支持添加刷新、重试等交互操作
 * 6. 基于Vant UI组件库，保持设计一致性
 *
 * 技术特点：
 * - 基于van-empty组件封装，继承其完整功能
 * - 使用插槽系统，支持自定义操作按钮
 * - 支持响应式图片尺寸配置
 * - 集成统一的间距和布局样式
 * - 支持多种图片类型：default、error、network、search
 *
 * 使用场景：
 * - 列表数据为空时的占位显示
 * - 搜索结果为空的提示页面
 * - 网络错误或加载失败的状态展示
 * - 功能模块暂无内容的引导页面
 */
-->

<template>
  <!-- 空状态容器 -->
  <!-- 提供统一的空状态展示区域，包含图片、描述和操作按钮 -->
  <div class="wo-empty">
    <!-- Vant空状态组件 -->
    <!-- 根据配置显示对应的空状态图片、尺寸和描述文字 -->
    <van-empty
      :image="image"
      :image-size="imageSize"
      :description="description"
    >
      <!-- 操作按钮插槽 -->
      <!-- 允许父组件插入自定义的操作按钮，如刷新、重试等 -->
      <template #default>
        <div class="wo-empty-action">
          <slot></slot>
        </div>
      </template>
    </van-empty>
  </div>
</template>

<script setup>
// ===== 依赖导入 =====
// 导入Vue 3 Composition API相关函数
import { toRefs } from 'vue'

// ===== Props定义和解构 =====
// 定义组件接收的所有属性
const props = defineProps({
  // 空状态图片类型或自定义图片URL
  // 可选值：'default'、'error'、'network'、'search'，或自定义图片URL
  image: {
    type: String,
    default: 'default'
  },
  // 空状态图片尺寸，支持数字或字符串格式
  // 单位为px，用于控制图片显示大小
  imageSize: {
    type: [Number, String],
    default: 120
  },
  // 空状态描述文字
  // 显示在图片下方，用于向用户说明当前状态
  description: {
    type: String,
    default: '暂无数据'
  }
})

// 使用toRefs解构props，保持响应性
// 解构出常用的props属性，便于在模板和逻辑中直接使用
const {
  image,
  imageSize,
  description
} = toRefs(props)
</script>

<style scoped lang="less">
.wo-empty {
  padding: 60px 20px;

  &-action {
    margin-top: 24px;
  }
}
</style>
