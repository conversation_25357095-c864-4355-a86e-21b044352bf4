<!--
/**
 * 底部操作栏占位符组件
 *
 * 主要功能：
 * 1. 为底部固定操作栏提供占位空间，防止页面内容被遮挡
 * 2. 支持自定义占位高度，适配不同操作栏的实际高度
 * 3. 确保页面滚动时内容能够完整显示，提升用户体验
 * 4. 采用透明占位设计，不影响页面视觉效果
 *
 * 技术特点：
 * - 使用动态样式绑定实现高度自适应
 * - 提供默认高度值，与WoActionBar组件保持一致
 * - 采用简洁的DOM结构，性能优化
 *
 * 使用场景：
 * - 配合WoActionBar组件使用，放置在页面内容底部
 * - 需要为固定定位元素预留空间的场景
 * - 确保页面内容不被底部操作栏遮挡时
 */
-->

<template>
  <!-- 底部操作栏占位符容器 -->
  <!-- 使用动态高度样式，为底部固定操作栏预留空间 -->
  <!-- 防止页面内容被底部固定元素遮挡 -->
  <div class="action-bar-placeholder" :style="{ height: height + 'px' }"></div>
</template>

<script setup>
import { toRefs } from 'vue'

// ==================== 组件属性定义 ====================
// 定义组件接收的props参数
const props = defineProps({
  // 占位符高度，用于匹配底部操作栏的实际高度
  // 默认值65px与WoActionBar组件的最小高度保持一致
  height: {
    type: Number,
    default: 65
  }
})

// 使用toRefs解构props，保持响应性
// 确保在模板样式绑定中能够正确响应props的变化
const { height } = toRefs(props)
</script>

<style scoped>
.action-bar-placeholder {
  width: 100%;
}
</style>
