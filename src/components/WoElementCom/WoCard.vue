<!--
/**
 * 通用卡片容器组件
 *
 * 主要功能：
 * 1. 提供统一的卡片样式容器，用于内容分组和展示
 * 2. 支持可选的标题显示，提供内容区域的标识
 * 3. 提供插槽内容区域，允许自定义卡片内容
 * 4. 集成统一的间距和圆角设计，保持界面一致性
 * 5. 支持响应式布局，适配不同屏幕尺寸
 *
 * 技术特点：
 * - 基于CSS的卡片样式系统，提供统一的视觉效果
 * - 使用条件渲染优化性能，只在需要时显示标题
 * - 支持插槽内容，提供最大的灵活性
 * - 集成边框和阴影效果，提升视觉层次
 *
 * 使用场景：
 * - 表单内容的分组展示
 * - 列表项的卡片化展示
 * - 信息面板和详情页面的内容容器
 * - 设置页面的功能模块分组
 */
-->

<template>
  <!-- 卡片容器 -->
  <!-- 提供统一的卡片样式，包括背景、圆角、间距等 -->
  <div class="card-section">
    <!-- 卡片标题区域 -->
    <!-- 当提供title属性时显示标题，带有底部分割线 -->
    <div v-if="title" class="card-title">
      {{ title }}
    </div>

    <!-- 卡片内容区域 -->
    <!-- 插槽容器，允许父组件插入任意内容 -->
    <div class="card-content">
      <slot />
    </div>
  </div>
</template>

<script setup>
// ===== 依赖导入 =====
// 导入Vue 3 Composition API相关函数
import { toRefs } from 'vue'

// ===== Props定义和解构 =====
// 定义组件接收的所有属性
const props = defineProps({
  // 卡片标题文本，可选属性
  // 当提供时会在卡片顶部显示带有分割线的标题
  title: {
    type: String,
    default: ''
  }
})

// 使用toRefs解构props，保持响应性
// 解构出常用的props属性，便于在模板和逻辑中直接使用
const {
  title
} = toRefs(props)
</script>

<style scoped lang="less">
.card-section {
  background-color: #FFFFFF;
  padding: 13px 15px;
  margin-bottom: 10px;
  border-radius: 10px;
}

.card-title {
  font-size: 15px;
  font-weight: 600;
  color: #171E24;
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid #E2E8EE;
}

.card-content {
  width: 100%;
}
</style>
