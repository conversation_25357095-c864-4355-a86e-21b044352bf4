<!--
/**
 * 底部固定操作栏组件
 *
 * 主要功能：
 * 1. 提供底部固定定位的操作栏容器，支持自定义底部距离
 * 2. 采用插槽机制，允许完全自定义操作栏内容
 * 3. 提供默认的新增收货地址按钮，支持自定义按钮尺寸
 * 4. 自动适配不同屏幕尺寸，提供响应式布局
 * 5. 集成事件发射机制，支持父组件监听操作事件
 *
 * 技术特点：
 * - 使用fixed定位实现底部固定效果
 * - 支持动态底部距离调整，适配不同页面布局
 * - 采用flex布局确保内容居中对齐
 * - 提供默认样式和交互效果
 *
 * 使用场景：
 * - 表单页面需要固定提交按钮时
 * - 列表页面需要固定操作按钮时
 * - 需要在页面底部提供持续可见的操作入口时
 */
-->

<template>
  <!-- 底部固定操作栏容器 -->
  <!-- 使用fixed定位固定在页面底部，支持动态调整底部距离 -->
  <!-- 提供白色背景和适当的内边距，确保操作按钮清晰可见 -->
  <div class="action-bar">
    <!-- 默认插槽，允许父组件自定义操作栏内容 -->
    <!-- 如果父组件未提供内容，则显示默认的新增收货地址按钮 -->
    <slot>
      <!-- 默认新增收货地址按钮 -->
      <!-- 使用主题色样式，全宽显示，支持自定义尺寸 -->
      <!-- 点击时触发handleAddClick事件处理函数 -->
      <WoButton type="primary" block :size="size" @click="handleAddClick">新增收货地址</WoButton>
    </slot>
  </div>
</template>

<script setup>
import { toRefs } from 'vue'
import WoButton from '@components/WoElementCom/WoButton/WoButton.vue'

// ==================== 组件属性定义 ====================
// 定义组件接收的props参数
const props = defineProps({
  // 底部距离，支持数字或字符串类型，用于调整操作栏距离页面底部的距离
  bottom: {
    type: [Number, String],
    default: 0
  },
  // 按钮尺寸，控制默认按钮的显示大小
  size: {
    type: String,
    default: 'default'
  }
})

// 使用toRefs解构props，保持响应性
// 确保在模板和样式绑定中能够正确响应props的变化
const { bottom, size } = toRefs(props)

// 定义组件向父组件发射的事件
const emit = defineEmits(['add'])

// ==================== 默认按钮操作处理 ====================
// 处理默认新增按钮点击事件
// 当用户点击默认的新增收货地址按钮时触发
const handleAddClick = () => {
  // 向父组件发射add事件，通知父组件执行新增操作
  emit('add')
}
</script>

<style scoped lang="less">
.action-bar {
  position: fixed;
  left: 0;
  right: 0;
  bottom: v-bind('bottom + "px"');
  padding: 10px 10px;
  background-color: #FFFFFF;
  //box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.05);
  z-index: 10;
  min-height: 55px;
  display: flex;
  align-items: center;
  box-sizing: border-box;
}
</style>
