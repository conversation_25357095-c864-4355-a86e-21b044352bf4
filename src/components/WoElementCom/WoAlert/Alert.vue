<!--
/**
 * 通用弹窗提示组件
 *
 * 主要功能：
 * 1. 基于Vant Dialog组件封装的通用弹窗提示组件
 * 2. 支持文本和HTML内容显示，提供灵活的内容展示方式
 * 3. 提供完整的按钮配置选项，支持确认、取消按钮的显示和样式定制
 * 4. 支持多种对齐方式和自定义样式，满足不同设计需求
 * 5. 集成事件回调机制，支持确认和取消操作的处理
 * 6. 提供编程式调用接口，支持show/close方法控制显示状态
 *
 * 技术特点：
 * - 基于Vant UI组件库，保证组件稳定性和一致性
 * - 支持插槽内容，允许自定义弹窗内容
 * - 提供丰富的配置选项，适应不同业务场景
 * - 集成响应式设计，自动适配不同屏幕尺寸
 *
 * 使用场景：
 * - 需要用户确认的操作提示
 * - 重要信息的展示和通知
 * - 表单提交前的确认对话框
 * - 错误信息或成功信息的展示
 */
-->

<template>
  <!-- 弹窗组件容器 -->
  <div>
    <!-- Vant Dialog弹窗组件 -->
    <!-- 绑定显示状态和各种配置属性，监听确认和取消事件 -->
    <!-- 支持完整的弹窗配置，包括标题、按钮、样式等 -->
    <Dialog
      v-model:show="isShow"
      :title="title"
      :show-confirm-button="showConfirmButton"
      :show-cancel-button="showCancelButton"
      :confirm-button-text="confirmButtonText"
      :cancel-button-text="cancelButtonText"
      :confirm-button-color="confirmButtonColor"
      :cancel-button-color="cancelButtonColor"
      :close-on-click-overlay="closeOnClickOverlay"
      :width="width"
      :class-name="className"
      :theme="theme"
      :before-close="beforeClose"
      :transition="transition"
      :get-container="getContainer"
      @confirm="onConfirm"
      @cancel="onCancel"
    >
      <!-- 文本内容显示区域 -->
      <!-- 当提供message属性时显示纯文本内容 -->
      <!-- 应用自定义样式和对齐方式 -->
      <div
        v-if="message"
        class="alert-message"
        :style="messageStyle"
        :class="messageAlignClass"
      >
        {{ message }}
      </div>

      <!-- HTML内容显示区域 -->
      <!-- 当提供messageHtml属性时显示HTML格式内容 -->
      <!-- 支持富文本内容展示，应用相同的样式和对齐方式 -->
      <div
        v-if="messageHtml"
        class="alert-message"
        v-html="messageHtml"
        :style="messageStyle"
        :class="messageAlignClass"
      ></div>

      <!-- 自定义内容插槽 -->
      <!-- 允许父组件插入自定义内容到弹窗中 -->
      <slot></slot>
    </Dialog>
  </div>
</template>

<script setup>
// ==================== 依赖导入 ====================
// 导入Vant UI组件库的Dialog组件
import { Dialog } from 'vant'
// 导入Vue 3 Composition API相关函数
import { ref, computed, toRefs } from 'vue'

// ===== 组件配置 =====
// 定义组件名称
defineOptions({
  name: 'WoAlert'
})

// ==================== Props定义和解构 ====================
// 定义组件接收的所有属性，包括弹窗配置、内容设置、按钮配置等
const props = defineProps({
  // 弹窗标题文本
  title: {
    type: String,
    default: ''
  },
  // 弹窗显示的纯文本内容
  message: {
    type: String,
    default: ''
  },
  // 弹窗显示的HTML格式内容
  messageHtml: {
    type: String,
    default: ''
  },
  // 消息内容的自定义样式对象
  messageStyle: {
    type: Object,
    default: () => ({})
  },
  // 内容文本对齐方式：left左对齐、center居中、right右对齐
  messageAlign: {
    type: String,
    default: 'center'
  },
  // 是否显示确认按钮
  showConfirmButton: {
    type: Boolean,
    default: true
  },
  // 是否显示取消按钮
  showCancelButton: {
    type: Boolean,
    default: false
  },
  // 确认按钮显示的文字
  confirmButtonText: {
    type: String,
    default: '确定'
  },
  // 取消按钮显示的文字
  cancelButtonText: {
    type: String,
    default: '取消'
  },
  // 确认按钮的背景颜色
  confirmButtonColor: {
    type: String,
    default: 'var(--wo-biz-theme-color)'
  },
  // 取消按钮的背景颜色
  cancelButtonColor: {
    type: String,
    default: '#171E24'
  },
  // 是否允许点击遮罩层关闭弹窗
  closeOnClickOverlay: {
    type: Boolean,
    default: false
  },
  // 弹窗的宽度，支持字符串和数字类型
  width: {
    type: [String, Number],
    default: '300px'
  },
  // 弹窗的主题样式：default默认、round圆角
  theme: {
    type: String,
    default: 'default',
    validator: value => ['default', 'round'].includes(value)
  },
  // 自定义CSS类名，用于额外样式定制
  className: {
    type: String,
    default: ''
  },
  // 弹窗关闭前的回调函数，可用于阻止关闭
  beforeClose: {
    type: Function,
    default: null
  },
  // 弹窗显示/隐藏的过渡动画类名
  transition: {
    type: String,
    default: ''
  },
  // 指定弹窗挂载的DOM节点，支持选择器字符串、DOM对象或函数
  getContainer: {
    type: [String, Object, Function],
    default: null
  },
  // 确认按钮点击回调函数
  onConfirmCallback: {
    type: Function,
    default: null
  },
  // 取消按钮点击回调函数
  onCancelCallback: {
    type: Function,
    default: null
  }
})

// 使用toRefs解构props，保持响应性
// 解构出常用的props属性，便于在模板和逻辑中直接使用
const {
  messageAlign,
  onConfirmCallback,
  onCancelCallback
} = toRefs(props)

// ==================== 事件定义 ====================
// 定义组件向父组件发射的事件
const emit = defineEmits([
  'confirm',  // 确认按钮点击事件
  'cancel'    // 取消按钮点击事件
])

// ==================== 弹窗显示状态管理 ====================
// 内部维护的弹窗显示状态
const isShow = ref(false)

// ==================== 样式计算 ====================
// 计算消息内容的CSS类名
// 根据对齐方式生成对应的类名，用于样式控制
const messageAlignClass = computed(() => {
  if (messageAlign.value === 'left') return 'text-left'
  if (messageAlign.value === 'right') return 'text-right'
  return 'text-center'
})

// ==================== 公共方法 ====================
// 显示弹窗的方法
// 设置内部显示状态为true，返回Promise便于链式调用
function showDialog() {
  isShow.value = true
  return Promise.resolve()
}

// 隐藏弹窗的方法
// 设置内部显示状态为false，返回Promise便于链式调用
function close() {
  isShow.value = false
  return Promise.resolve()
}

// ==================== 事件处理函数 ====================
// 确认按钮点击处理函数
// 执行回调函数并向父组件发射confirm事件
function onConfirm() {
  if (onConfirmCallback.value) {
    onConfirmCallback.value()
  }
  emit('confirm')
}

// 取消按钮点击处理函数
// 执行回调函数并向父组件发射cancel事件
function onCancel() {
  if (onCancelCallback.value) {
    onCancelCallback.value()
  }
  emit('cancel')
}

// ==================== 组件暴露 ====================
// 向父组件暴露公共方法，支持通过ref调用
defineExpose({
  show: showDialog,  // 显示弹窗方法
  close,             // 隐藏弹窗方法
  onConfirm,         // 确认事件处理方法
  onCancel           // 取消事件处理方法
})
</script>

<style scoped lang="less">
.alert-message {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 110px;
  padding: 20px;
  line-height: 1.5;
  font-size: 16px;
  color: #171E24;
  white-space: pre-wrap;
  box-sizing: border-box;
}

.text-left {
  text-align: left;
}

.text-center {
  text-align: center;
}

.text-right {
  text-align: right;
}
</style>
