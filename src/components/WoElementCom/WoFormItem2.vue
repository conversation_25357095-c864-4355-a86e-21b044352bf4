<!--
  WoFormItem2 表单项组件（简化版）
  
  功能描述：
  - 基于 Vant Field 组件封装的轻量级表单输入项
  - 相比 WoFormItem 组件，提供更简洁的样式设计
  - 支持多种输入类型和基础的表单验证功能
  - 提供丰富的插槽支持，可自定义各个区域内容
  - 内置箭头指示器，适用于选择器和导航场景
  - 采用更紧凑的布局和简化的视觉效果
  
  技术特点：
  - 基于 Vue 3 Composition API 开发
  - 使用 v-model 双向数据绑定
  - 支持响应式属性传递
  - 提供多个插槽扩展点
  - 简化的样式主题设计
  
  使用场景：
  - 简洁风格的表单输入项
  - 列表式表单布局
  - 移动端友好的表单控件
  - 信息录入和展示
-->
<template>
  <!-- 表单项容器，根据是否显示箭头添加对应样式类 -->
  <div class="wo-form-item" :class="{'wo-form-item-arrow': arrow}">
    <!-- Vant Field 组件，提供完整的表单输入功能 -->
    <van-field
      v-model="fieldValue"
      :label="label"
      :placeholder="placeholder"
      :readonly="readonly"
      :disabled="disabled"
      :required="required"
      :error="error"
      :error-message="errorMessage"
      :label-width="labelWidth"
      :label-align="labelAlign"
      :input-align="inputAlign"
      :type="type"
      :maxlength="maxlength"
      :rows="rows"
      :autosize="autosize"
      :clearable="clearable"
      :clickable="clickable"
      @input="handleInput"
      @focus="handleFocus"
      @blur="handleBlur"
      @click="handleClick"
      @clear="handleClear"
    >
      <!-- 左侧图标插槽：允许自定义左侧显示的图标内容 -->
      <template #left-icon v-if="$slots.leftIcon">
        <slot name="leftIcon"></slot>
      </template>

      <!-- 右侧图标插槽：支持自定义右侧图标或显示默认箭头 -->
      <template #right-icon v-if="$slots.rightIcon || arrow">
        <slot name="rightIcon">
          <!-- 当启用箭头模式时显示默认的右箭头图标 -->
          <img v-if="arrow" src="../../static/images/arrow-right-black.png" alt="箭头" class="arrow-icon">
        </slot>
      </template>

      <!-- 输入框内容插槽：允许完全自定义输入框的显示内容 -->
      <template #input v-if="$slots.input">
        <slot name="input"></slot>
      </template>

      <!-- 标签内容插槽：允许自定义标签区域的显示内容 -->
      <template #label v-if="$slots.label">
        <slot name="label"></slot>
      </template>

      <!-- 按钮插槽：允许在输入框右侧添加自定义按钮 -->
      <template #button v-if="$slots.button">
        <slot name="button"></slot>
      </template>

      <!-- 额外内容插槽：允许在表单项底部添加额外的内容 -->
      <template #extra v-if="$slots.extra">
        <slot name="extra"></slot>
      </template>
    </van-field>
  </div>
</template>

<script setup>
// ===== 依赖导入 =====
// Vue 3 Composition API 核心函数
import { ref, watch, toRefs } from 'vue'

// ===== 组件配置 =====
// 组件名称定义
defineOptions({
  name: 'WoFormItem2'
})

// ===== Props 定义 =====
// 定义组件接收的属性，包含基础属性和 Vant Field 的所有相关属性
const props = defineProps({
  // 基础表单项属性
  label: {
    type: String,
    required: true // 标签文本，必填属性
  },
  arrow: {
    type: Boolean,
    default: false // 是否显示右侧箭头图标
  },
  
  // Vant Field 组件相关属性
  modelValue: {
    type: [String, Number],
    default: '' // v-model 绑定的值
  },
  placeholder: {
    type: String,
    default: '请输入' // 输入框占位符文本
  },
  readonly: {
    type: Boolean,
    default: false // 是否为只读状态
  },
  disabled: {
    type: Boolean,
    default: false // 是否禁用输入框
  },
  required: {
    type: Boolean,
    default: false // 是否显示必填标记
  },
  error: {
    type: Boolean,
    default: false // 是否显示为错误状态
  },
  errorMessage: {
    type: String,
    default: '' // 错误提示信息
  },
  labelWidth: {
    type: [String, Number],
    default: '6.2em' // 标签宽度
  },
  labelAlign: {
    type: String,
    default: 'left' // 标签对齐方式
  },
  inputAlign: {
    type: String,
    default: 'left' // 输入框内容对齐方式
  },
  type: {
    type: String,
    default: 'text' // 输入框类型（text、password、number等）
  },
  maxlength: {
    type: [String, Number],
    default: undefined // 最大输入长度限制
  },
  rows: {
    type: [String, Number],
    default: 1 // 文本域行数
  },
  autosize: {
    type: [Boolean, Object],
    default: false // 是否自适应高度
  },
  clearable: {
    type: Boolean,
    default: false // 是否显示清除按钮
  },
  clickable: {
    type: Boolean,
    default: false // 是否可点击
  }
})

// ===== Props 解构 =====
// 使用 toRefs 解构 props，保持响应性
const { modelValue } = toRefs(props)

// ===== 事件定义 =====
// 定义组件向外发射的事件
const emit = defineEmits([
  'update:modelValue', // v-model 更新事件
  'input',            // 输入事件
  'focus',            // 获得焦点事件
  'blur',             // 失去焦点事件
  'click',            // 点击事件
  'clear'             // 清除事件
])

// ===== 响应式数据管理 =====
// 内部表单值状态，用于双向数据绑定
const fieldValue = ref(modelValue.value)

// 监听外部 modelValue 变化，同步到内部状态
watch(modelValue, (newVal) => {
  fieldValue.value = newVal
})

// 监听内部值变化，向外发射更新事件
watch(fieldValue, (newVal) => {
  emit('update:modelValue', newVal)
})

// ===== 事件处理函数 =====
// 输入事件处理：当用户输入内容时触发
const handleInput = (value) => {
  emit('input', value)
}

// 获得焦点事件处理：当输入框获得焦点时触发
const handleFocus = (event) => {
  emit('focus', event)
}

// 失去焦点事件处理：当输入框失去焦点时触发
const handleBlur = (event) => {
  emit('blur', event)
}

// 点击事件处理：当输入框被点击时触发
const handleClick = (event) => {
  emit('click', event)
}

// 清除事件处理：当点击清除按钮时触发
const handleClear = (event) => {
  emit('clear', event)
}
</script>

<style scoped lang="less">
.wo-form-item {
  position: relative;
  :deep(.van-field) {
    padding: 20px 0;
    border-bottom: 1px solid #E2E8EE;
    background: transparent;

    .van-field__label {
      font-size: 14px;
      color: #171E24;
      font-weight: 400;
      min-width: 55px;
      margin-right: 0;
      width: auto;
    }

    .van-field__control {
      font-size: 14px;
      color: #171E24;
      &::placeholder {
        color: #718096;
        opacity: 1;
      }
    }

    .van-field__body {
      align-items: center;
    }

    // 移除默认边框
    &::after {
      display: none;
    }
  }

  &-arrow {
    :deep(.van-field) {
      .van-field__body {
        display: flex;
        align-items: center;
      }
    }
  }
}

.arrow-icon {
  width: 6px;
  height: 11px;
  margin-left: 5px;
}
</style>
