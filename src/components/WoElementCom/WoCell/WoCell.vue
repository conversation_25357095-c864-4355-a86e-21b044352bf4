<!--
/**
 * 通用单元格组件
 *
 * 主要功能：
 * 1. 提供左右布局的单元格结构，适用于表单和列表场景
 * 2. 支持左侧标题显示，可配置必填标识
 * 3. 支持右侧内容显示，可通过插槽自定义或使用默认文本
 * 4. 支持可选的右箭头图标，用于导航或展开操作
 * 5. 提供多种布局选项：居中对齐、边框显示、垂直布局等
 * 6. 支持右侧区域点击事件，用于交互操作
 *
 * 技术特点：
 * - 基于Flexbox布局，提供灵活的对齐方式
 * - 使用插槽系统，支持右侧内容的完全自定义
 * - 集成条件样式类，支持多种视觉状态
 * - 支持自定义箭头图标，提供默认图标兜底
 * - 响应式设计，适配不同屏幕尺寸
 *
 * 使用场景：
 * - 表单字段的标签和输入区域布局
 * - 设置页面的选项列表
 * - 详情页面的信息展示
 * - 导航列表的菜单项
 */
-->

<template>
  <!-- 单元格容器 -->
  <!-- 根据配置应用不同的样式类：居中对齐、底部边框 -->
  <div class="wo-cell" :class="{ 'is-center': isCenter, 'is-border': isBorder }">
    <!-- 左侧标题区域 -->
    <!-- 显示标题文本，支持必填标识的红色星号 -->
    <div class="cell-left" :class="{ 'is-require': isRequire }">
      <div class="left-title">{{ leftTitle }}</div>
    </div>
    
    <!-- 右侧内容区域 -->
    <!-- 支持垂直布局，点击时触发rightClick事件 -->
    <div class="cell-right" :class="{ 'is-vertical': isVertical }" @click="handleRightClick">
      <!-- 右侧内容插槽 -->
      <!-- 允许父组件自定义右侧内容，默认显示rightTitle文本 -->
      <slot name="right">
        <div class="right-title">{{ rightTitle }}</div>
      </slot>
      
      <!-- 右箭头图标 -->
      <!-- 当showArrow为true时显示，支持自定义图标或使用默认图标 -->
      <img v-if="showArrow" class="right-arrow" :src="arrowSrc || defaultArrowSrc" alt="" srcset="">
    </div>
  </div>
</template>

<script setup>
// ===== 依赖导入 =====
// 导入Vue 3 Composition API相关函数
import { toRefs } from 'vue'
// 导入默认箭头图标资源
import defaultArrowImg from '@/static/images/arrow-right-gray.png'

// ===== 组件配置 =====
// 定义组件名称
defineOptions({
  name: 'WoCell'
})

// ===== 静态资源配置 =====
// 设置默认箭头图标路径，用于右侧箭头显示
const defaultArrowSrc = defaultArrowImg

// ===== Props定义和解构 =====
// 定义组件接收的所有属性
const props = defineProps({
  // 左侧标题文本
  leftTitle: {
    type: String,
    default: ''
  },
  // 右侧标题文本，当未使用插槽时显示
  rightTitle: {
    type: String,
    default: ''
  },
  // 是否垂直居中对齐
  isCenter: {
    type: Boolean,
    default: true
  },
  // 是否显示底部边框
  isBorder: {
    type: Boolean,
    default: false
  },
  // 是否显示必填标识（红色星号）
  isRequire: {
    type: Boolean,
    default: false
  },
  // 右侧内容是否垂直布局
  isVertical: {
    type: Boolean,
    default: false
  },
  // 是否显示右箭头图标
  showArrow: {
    type: Boolean,
    default: false
  },
  // 自定义箭头图标路径，为空时使用默认图标
  arrowSrc: {
    type: String,
    default: ''
  }
})

// 使用toRefs解构props，保持响应性
// 解构出常用的props属性，便于在模板和逻辑中直接使用
const {
  leftTitle,
  rightTitle,
  isCenter,
  isBorder,
  isRequire,
  isVertical,
  showArrow,
  arrowSrc
} = toRefs(props)

// ===== 事件定义 =====
// 定义组件向父组件发送的事件
const emit = defineEmits([
  // 右侧区域点击事件
  'rightClick'
])

// ===== 事件处理函数 =====
// 处理右侧区域点击事件
// 当用户点击右侧内容区域时触发，用于导航或展开操作
const handleRightClick = () => {
  emit('rightClick')
}
</script>


<style scoped lang="less">
.wo-cell {
  width: 100%;
  min-height: 55px;
  display: flex;
  justify-content: space-between;
  padding: 15px 0;
  box-sizing: border-box;
  &.is-center {
    align-items: center;
  }

  &.is-border {
    border-bottom: 1px solid #E2E8EE;
  }

  .is-require {
    position: relative;

    &:after {
      content: '*';
      position: absolute;
      top: -3px;
      right: -10px;
      color: #EF4444;
      font-size: 25px;
    }
  }

  .is-vertical {
    flex-direction: column;
  }

  .cell-left {
    min-width: 65px;
    margin-right: 10px;

    .left-title {
      font-size: 16px;
      color: #171E24;
      line-height: 1;
      font-weight: 400;
    }
  }

  .cell-right {
    display: flex;
    align-items: center;

    .right-title {
      font-size: 15px;
      color: #171E24;
      line-height: 1;
      font-weight: 400;
    }

    .right-arrow {
      margin-left: 5px;
      width: 7px;
      height: 12px;
    }
  }
}
</style>
