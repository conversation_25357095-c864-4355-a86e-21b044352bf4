<!--
/**
 * 通用按钮组件
 *
 * 主要功能：
 * 1. 提供多种按钮类型，包括主要、次要、第三级、危险、文本、渐变和取消按钮
 * 2. 支持多种尺寸规格，从超大到小型，满足不同场景需求
 * 3. 提供块级按钮和圆角按钮样式选项
 * 4. 集成禁用状态处理，防止误操作
 * 5. 支持插槽内容，允许自定义按钮内容
 * 6. 提供点击事件处理，支持事件冒泡控制
 *
 * 技术特点：
 * - 基于CSS类名的样式系统，支持动态样式切换
 * - 使用CSS变量实现主题色适配
 * - 集成过渡动画效果，提升用户体验
 * - 支持响应式设计，适配不同屏幕尺寸
 * - 提供完整的交互状态反馈
 *
 * 使用场景：
 * - 表单提交和操作确认
 * - 页面导航和功能触发
 * - 对话框和弹窗中的操作按钮
 * - 列表项的快捷操作按钮
 */
-->

<template>
  <!-- 按钮容器 -->
  <!-- 应用动态类名，包括类型、尺寸、状态等样式 -->
  <!-- 阻止事件冒泡，避免触发父元素的点击事件 -->
  <div
    class="wo-button"
    :class="[
      `wo-button-${type}`,
      { 'wo-button-block': block },
      { 'wo-button-disabled': disabled },
      `wo-button-size-${size}`,
      { 'wo-button-round': round }
    ]"
    @click.stop="handleClick"
  >
    <!-- 按钮内容插槽 -->
    <!-- 允许父组件插入自定义内容，如文字、图标等 -->
    <slot></slot>
  </div>
</template>

<script setup>
// ===== 依赖导入 =====
// 导入Vue 3 Composition API相关函数
import { toRefs } from 'vue'

// ===== 组件配置 =====
// 定义组件名称
defineOptions({
  name: 'WoButton'
})

// ===== Props定义和解构 =====
// 定义组件接收的所有属性，包括按钮类型、尺寸、状态等配置
const props = defineProps({
  // 按钮类型：primary主要、secondary次要、tertiary第三级、danger危险、text文本、gradient渐变、cancel取消、default默认
  type: {
    type: String,
    default: 'default',
    validator: (value) => ['primary', 'secondary', 'tertiary', 'danger', 'text', 'gradient', 'cancel', 'default'].includes(value)
  },
  // 按钮尺寸：xlarge超大、large大、medium中等、small小
  size: {
    type: String,
    default: 'large',
    validator: (value) => ['xlarge', 'large', 'medium', 'small'].includes(value)
  },
  // 是否为块级按钮，占满父容器宽度
  block: {
    type: Boolean,
    default: false
  },
  // 是否禁用按钮，禁用后不可点击
  disabled: {
    type: Boolean,
    default: false
  },
  // 是否为圆角按钮，应用圆角样式
  round: {
    type: Boolean,
    default: false
  }
})

// 使用toRefs解构props，保持响应性
// 解构出常用的props属性，便于在模板和逻辑中直接使用
const {
  disabled
} = toRefs(props)

// ===== 事件定义 =====
// 定义组件向父组件发射的事件
const emit = defineEmits([
  'click'  // 按钮点击事件
])

// ===== 事件处理函数 =====
// 按钮点击处理函数
// 检查按钮是否禁用，只有在非禁用状态下才发射点击事件
// 接收原生点击事件对象，并传递给父组件
const handleClick = (event) => {
  if (!disabled.value) {
    emit('click', event)
  }
}
</script>

<style scoped lang="less">
.wo-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;
  text-align: center;
  border: none;
  cursor: pointer;
  border-radius: 9999px;
  font-weight: 500;
  transition: all 0.3s;
  position: relative;
  overflow: hidden;

  // 类型样式.
  &-default {
    background-color: #FFFFFF;
    color: #171E24;
    border: 1px solid rgba(198,201,204,1);

  }

  &-primary {
    background-color: var(--wo-biz-theme-color);
    color: #FFFFFF;

    &:active {
      background-color: var(--wo-biz-theme-active);
    }
  }

  &-secondary {
    background-color: #FFFFFF;
    color: var(--wo-biz-theme-color);
    border: 1px solid var(--wo-biz-theme-color);

    &:active {
      background-color: var(--wo-biz-theme-bg-1);
    }
  }

  &-tertiary {
    background-color: var(--wo-biz-theme-bg-1);
    color: var(--wo-biz-theme-color);

    &:active {
      background-color: var(--wo-biz-theme-bg-2);
    }
  }

  &-danger {
    background-color: #EF4444;
    color: #FFFFFF;

    &:active {
      background-color: #DC2626;
    }
  }

  &-text {
    background-color: transparent;
    color: var(--wo-biz-theme-color);
    padding: 0;

    &:active {
      opacity: 0.7;
    }
  }

  &-gradient {
    background-image: var(--wo-biz-theme-gradient-1);
    color: #FFFFFF;

    &:active {
      background-image: var(--wo-biz-theme-gradient-3);
    }
  }

  &-cancel {
    background-color: #F8F9FA;
    color: #718096;

    &:active {
      background-color: #E2E8F0;
    }
  }

  // 尺寸样式
  &-size-xlarge {
    height: 42px;
    font-size: 16px;
    width: 100%;
    border-radius: 22px;
  }

  &-size-large {
    height: 36px;
    font-size: 15px;
    width: 160px;
    border-radius: 22px;
  }

  &-size-medium {
    height: 36px;
    font-size: 15px;
    width: 90px;
    border-radius: 18px;
  }

  &-size-small {
    height: 28px;
    font-size: 12px;
    width: 80px;
    border-radius: 15px;
  }

  &-size-special {
    height: 38px;
    font-size: 15px;
    width: 119px;
    border-radius: 20px;
  }

  // 块级按钮
  &-block {
    width: 100%;
  }

  // 圆角按钮
  &-round {
    border-radius: 50px;
  }

  // 禁用状态
  &-disabled {
    opacity: 0.5;
    cursor: not-allowed;

    //&:active {
    //  background-color: inherit;
    //  background-image: inherit;
    //}
  }
}
</style>
