<!--
订单商品卡片组件

功能描述：
- 展示订单中的商品信息，支持单商品和多商品显示
- 提供商品图片、名称、规格、价格、数量等信息展示
- 支持物流信息显示
- 提供操作按钮区域，支持更多操作弹窗
- 支持自定义操作按钮和提示信息

技术特点：
- 响应式设计，适配不同屏幕尺寸
- 支持多商品横向滚动展示
- 集成全局弹窗状态管理
- 支持ZQ业务的价格区间显示
- 灵活的插槽系统支持自定义操作

使用场景：
- 订单详情页面
- 订单列表页面
- 售后订单展示
-->
<template>
  <!-- 商品卡片容器 -->
  <div 
    class="goods-item" 
    id="goods-item" 
    :class="{ 'has-actions': showActions }"
    :style="{ minHeight: minHeight + 'px' }" 
    @click="handleCardClick"
  >
    <!-- 商品详情区域 -->
    <div class="goods-detail">
      <!-- 多商品图片展示区域 -->
      <div class="multiple-goods-images" v-if="isMultipleGoods">
        <div class="images-scroll-container">
          <div 
            v-for="(goodsItem, index) in multiGoodsData" 
            :key="index" 
            class="single-goods-image" 
            :style="imageStyle"
          >
            <img :src="getImageUrl(goodsItem.detailImageUrl)" :alt="goodsItem.name" />
          </div>
        </div>
      </div>
      <!-- 单商品图片展示区域 -->
      <div v-else class="single-goods-image" :style="imageStyle">
        <img :src="getImageUrl(singleGoodsData.detailImageUrl)" :alt="item.name" />
      </div>
      
      <!-- 商品信息区域 -->
      <div class="goods-info" :class="{ 'single-goods-flex': !isMultipleGoods }">
        <!-- 单商品信息左侧：商品名称和规格 -->
        <div class="goods-info-left" v-if="!isMultipleGoods">
          <div class="goods-name">{{ singleGoodsData.name }}</div>
          <div class="goods-spec">{{ singleGoodsData.params }}</div>
        </div>
        
        <!-- 商品信息右侧：价格和数量 -->
        <div class="goods-info-right">
          <!-- ZQ业务单商品价格区间显示 -->
          <template v-if="isZQ && !isMultipleGoods && (singleGoodsData?.highPrice || singleGoodsData?.lowPrice)">
            <PriceDisplay
              :high-price="singleGoodsData.highPrice"
              :low-price="singleGoodsData.lowPrice"
              range-label=""
              size="small"
              class="goods-price"
            />
          </template>
          <!-- 普通价格显示 -->
          <template v-else>
            <PriceDisplay :price="displayPrice" size="small" class="goods-price" />
          </template>
          <!-- 商品数量显示 -->
          <span class="goods-quantity">共{{ displayQuantity }}件</span>
        </div>
      </div>
    </div>

    <!-- 物流信息展示区域 -->
    <div v-if="item.logistics" class="logistics-info">
      <div class="logistics-content">
        <span v-if="item.logistics.tracking" class="logistics">{{ item.logistics.tracking }}</span>
      </div>
    </div>

    <!-- 操作按钮区域 -->
    <div v-if="showActions" class="goods-action">
      <!-- 更多操作弹窗 -->
      <div v-if="showMore && moreActions && moreActions.length > 0" class="more-action" @click.stop>
        <van-popover 
          v-model:show="isPopoverVisible" 
          :actions="moreActions" 
          placement="bottom-start"
          :close-on-click-outside="true" 
          @select="onSelectAction" 
          class="more-popover-content"
        >
          <template #reference>
            <div class="more-button">
              <span>更多</span>
            </div>
          </template>
        </van-popover>
      </div>
      
      <!-- 左侧提示信息插槽 -->
      <div v-if="showTips" class="action-group-left">
        <slot name="tips" :item="item"></slot>
      </div>
      
      <!-- 右侧操作按钮插槽 -->
      <div class="action-group-right">
        <slot name="actions" :item="item"></slot>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed, toRefs } from 'vue'
import PriceDisplay from '@components/Common/PriceDisplay.vue'
import { useGlobalPopover } from '@/composables/index.js'
import { compact } from 'lodash-es'
import { getBizCode } from '@utils/curEnv.js'

// ==================== Props 定义 ====================
const props = defineProps({
  item: {
    type: Object,
    required: true,
    // 商品项数据，包含商品信息、价格、数量等
  },
  itemId: {
    type: [String, Number],
    default: Math.random(),
    // 商品项唯一标识，用于弹窗状态管理
  },
  imageSize: {
    type: Number,
    default: 90,
    // 商品图片尺寸（宽高相等）
  },
  minHeight: {
    type: Number,
    default: 135,
    // 卡片最小高度
  },
  showActions: {
    type: Boolean,
    default: false,
    // 是否显示操作按钮区域
  },
  showMore: {
    type: Boolean,
    default: true,
    // 是否显示更多操作按钮
  },
  showTips: {
    type: Boolean,
    default: true,
    // 是否显示提示信息插槽
  },
  moreActions: {
    type: Array,
    default: () => [],
    // 更多操作列表，用于弹窗显示
  }
})

// 使用 toRefs 解构 props，保持响应性
const { item, itemId, imageSize, moreActions } = toRefs(props)

// ==================== 事件定义 ====================
const emit = defineEmits(['click'])

// ==================== 业务逻辑计算 ====================
// 判断当前是否为ZQ业务
const isZQ = computed(() => getBizCode() === 'zq')

// 图片URL处理函数
const getImageUrl = (imageUrl) => {
  // 如果是数组，取第一个元素
  if (Array.isArray(imageUrl)) {
    return imageUrl.length > 0 ? imageUrl[0] : ''
  }
  // 如果是字符串或空值，直接返回
  return imageUrl || ''
}

// ==================== 商品数据处理 ====================
// 计算商品列表，统一处理新旧数据结构
const goodsList = computed(() => {
  const { skuNumInfoList, skuList } = item.value
  const list = skuNumInfoList || skuList

  // 如果没有商品列表数据，返回空数组
  if (!list || !Array.isArray(list)) {
    return []
  }

  // 处理不同的数据结构
  return list.map(listItem => {
    if (listItem.sku) {
      // 新数据结构：sku字段包含商品信息，需要拍平
      return { ...listItem.sku, skuNum: listItem.skuNum }
    } else {
      // 老数据结构：直接使用商品信息
      return { ...listItem, skuNum: '1' }
    }
  })
})

// 判断是否为多商品订单
const isMultipleGoods = computed(() => {
  // 优先检查 skuNumInfoList
  if (Array.isArray(item.value.skuNumInfoList)) {
    return item.value.skuNumInfoList.length > 1
  }
  // 其次检查 skuList
  if (Array.isArray(item.value.skuList)) {
    return item.value.skuList.length > 1
  }
  // 都不存在时返回 false
  return false
})

// 多商品数据处理
const multiGoodsData = computed(() => {
  const list = goodsList.value
  if (list.length > 1) {
    // 多商品情况，返回完整列表用于展示
    return list
  } else if (list.length === 1) {
    // 单商品情况，返回第一个元素
    return list[0]
  }
  return null
})

// 单商品数据处理
const singleGoodsData = computed(() => {
  const list = goodsList.value
  if (list.length > 0) {
    const goodsItem = list[0]
    // 拼接商品规格参数，过滤空值
    goodsItem.params = compact([goodsItem.param, goodsItem.param1, goodsItem.param2, goodsItem.param3, goodsItem.param4]).join(' ')
    return goodsItem
  }
  return null
})

// ==================== 样式计算 ====================
// 计算商品图片样式
const imageStyle = computed(() => ({
  width: imageSize.value + 'px',
  height: imageSize.value + 'px'
}))

// ==================== 价格和数量计算 ====================
// 计算显示价格
const displayPrice = computed(() => {
  if (isMultipleGoods.value) {
    // 多商品时显示总价
    return item.value.price || item.value.totalPrice
  }
  // 单商品时显示商品价格
  return item.value.price
})

// 计算显示数量
const displayQuantity = computed(() => {
  const list = goodsList.value

  if (isMultipleGoods.value) {
    // 多商品时累加所有商品的数量
    return list.reduce((sum, goods) => sum + (parseInt(goods.skuNum) || 0), 0)
  } else {
    // 单商品时返回第一个商品的数量
    return list.length > 0 ? (parseInt(list[0].skuNum) || 0) : 0
  }
})

// ==================== 弹窗状态管理 ====================
// 使用全局弹窗状态管理
const { setActivePopover, closeAllPopovers, isPopoverActive } = useGlobalPopover()

// 计算当前弹窗是否应该显示
const isPopoverVisible = computed({
  get() {
    return isPopoverActive(itemId.value)
  },
  set(value) {
    if (value) {
      // 显示当前弹窗
      setActivePopover(itemId.value)
    } else {
      // 关闭所有弹窗
      closeAllPopovers()
    }
  }
})

// ==================== 用户交互事件处理 ====================
// 处理卡片点击事件
const handleCardClick = (event) => {
  // 如果点击的是操作按钮区域，阻止事件冒泡
  if (event.target.closest('.goods-action')) {
    return
  }
  // 触发点击事件，传递商品数据
  emit('click', item.value)
}

// 处理更多操作选择
const onSelectAction = (action, index) => {
  // 如果操作有处理函数，执行它
  if (action && typeof action.handler === 'function') {
    action.handler()
  }
  // 关闭所有弹窗
  closeAllPopovers()
}

</script>

<style scoped lang="less">
.goods-item {
  position: relative;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  margin-bottom: 20px;
  cursor: pointer;
  transition: background-color 0.2s ease;

  :deep(.van-popover__action) {
    width: 110px;
    height: 32px;
  }


  &:last-child {
    border-bottom: none;
    margin-bottom: 0;
  }

  .goods-detail {
    width: 100%;
    display: flex;
    justify-content: space-between;
  }

  .multiple-goods-images {
    width: 100%;
    margin-right: 10px;
    flex: 1;
    overflow-x: auto;
    scroll-behavior: smooth;

    .images-scroll-container {
      width: 100%;
      display: flex;
      gap: 2px;

      // 隐藏滚动条但保持滚动功能
      &::-webkit-scrollbar {
        height: 2px;
      }

      &::-webkit-scrollbar-track {
        background: transparent;
      }

      &::-webkit-scrollbar-thumb {
        background: rgba(0, 0, 0, 0.2);
        border-radius: 1px;
      }

      .single-goods-image {
        border-radius: 4px;
        overflow: hidden;
        flex-shrink: 0;
        margin-right: 5px;

        &:last-child {
          margin-right: 0;
        }

        img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
      }
    }
  }

  .single-goods-image {
    border-radius: 4px;
    overflow: hidden;
    margin-right: 10px;
    flex-shrink: 0;

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }

  .goods-info {
    display: flex;
    justify-content: flex-end;

    &.single-goods-flex {
      flex: 1;
    }

    .goods-info-left {
      display: flex;
      flex-direction: column;
      flex: 1;
      margin-right: 20px;
      flex-shrink: 0;

      .goods-name {
        font-size: 13px;
        color: #171E24;
        line-height: 1.5;
        margin-bottom: 7px;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
        word-break: break-all;
      }

      .goods-spec {
        font-size: 11px;
        color: #718096;
        margin-bottom: 7px;
      }
    }

    .goods-info-right {
      display: flex;
      flex-direction: column;
      align-items: flex-end;

      .goods-price {
        margin-bottom: 7px;
      }

      .goods-quantity {
        font-size: 11px;
        color: #718096;
        margin-bottom: 7px;
      }
    }
  }

  .logistics-info {
    width: 100%;
    margin: 7px 0;

    .logistics-content {
      display: flex;
      align-items: center;
      font-size: 11px;
      color: #718096;
      line-height: 1.5;
      padding: 7px;
      box-sizing: border-box;
      background: #f5f5f5;
      border-radius: 4px;

      .logistics-label {
        margin-right: 4px;
        color: #4A5568;
      }

      .logistics-text {
        margin-right: 8px;
        color: #171E24;
      }

      .logistics-company,
      .logistics-number {
        margin-right: 8px;

        &:last-child {
          margin-right: 0;
        }
      }
    }
  }

  .goods-action {
    width: 100%;
    display: flex;
    gap: 12px;
    align-items: center;
    justify-content: space-between;

    .more-action {
      flex-shrink: 0;
    }

    .more-button {
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 12px;
      color: #666666;
      cursor: pointer;
    }

    .action-group-left {
      display: flex;
      align-items: center;
      justify-content: space-between;
      gap: 10px;
    }

    .action-group-right {
      display: flex;
      align-items: center;
      justify-content: space-between;
      gap: 10px;
    }

  }
}
</style>
