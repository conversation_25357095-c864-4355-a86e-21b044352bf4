<!--
/**
 * 瀑布流商品卡片组件
 *
 * 主要功能：
 * 1. 在瀑布流布局中展示单个商品信息，包括图片、标题、规格、价格等
 * 2. 支持多种业务场景的价格展示：单价格、价格区间、参考价等
 * 3. 根据不同业务代码（ZQ、YGJD等）显示对应的购物车图标和功能
 * 4. 提供商品点击和加购事件处理，支持用户交互操作
 * 5. 响应式设计，适配瀑布流布局的动态高度特性
 * 6. 图片懒加载优化，提升页面性能
 *
 * 技术特点：
 * - 使用article语义化标签，提升可访问性
 * - 支持事件冒泡控制，防止误触发
 * - 动态计算购物车图标，适配不同业务场景
 * - 文本溢出处理，确保布局稳定性
 *
 * 使用场景：
 * - 商品搜索结果的瀑布流展示
 * - 分类页面的商品网格布局
 * - 推荐商品的瀑布流展示
 * - 移动端商品列表页面
 */
-->

<template>
  <!-- 瀑布流商品项容器，使用article语义化标签 -->
  <!-- 点击整个卡片区域触发商品详情查看 -->
  <article class="waterfall-item" @click="handleItemClick">
    <!-- 商品卡片主体 -->
    <div class="waterfall-card">
      <!-- 商品图片区域 -->
      <div class="waterfall-image">
        <!-- 商品主图，使用懒加载优化性能 -->
        <!-- alt属性提供图片描述，提升可访问性 -->
        <img :src="item.showImageUrl" :alt="item.name" loading="lazy" />
      </div>
      
      <!-- 商品信息内容区域 -->
      <div class="waterfall-content">
        <!-- 商品标题，使用h3标签提供语义化结构 -->
        <h3 class="waterfall-title">{{ item.name }}</h3>
        
        <!-- 商品规格参数，将参数数组用空格连接显示 -->
        <p class="waterfall-spec">{{ item.params.join(' ') }}</p>
        
        <!-- 销量信息，仅在非ZQ业务场景下显示 -->
        <p class="waterfall-sales" v-if="!isZQ">销量{{ item.realSaleVolume }}件</p>
        
        <!-- 底部操作区域：价格和购物车按钮 -->
        <div class="waterfall-footer">
          <!-- ZQ业务场景的价格区间展示 -->
          <!-- 当为ZQ业务且存在高低价格时显示价格区间 -->
          <template v-if="isZQ && (item.highPrice || item.lowPrice)">
            <PriceDisplay
              :high-price="item.highPrice"
              :low-price="item.lowPrice"
              range-label="参考价"
              size="small"
              color="orange"
            />
          </template>
          
          <!-- 标准单价格展示 -->
          <!-- 其他业务场景或无价格区间时显示单一价格 -->
          <template v-else>
            <PriceDisplay :price="item.price" size="small" color="orange" />
          </template>
          
          <!-- 购物车快捷添加按钮 -->
          <!-- 仅在非ZQ业务场景下显示，使用stop修饰符防止事件冒泡 -->
          <button
            v-if="!isZQ"
            class="cart-btn"
            @click.stop="handleAddCart"
            type="button"
            aria-label="加入购物车"
          >
            <!-- 根据业务代码动态显示对应的购物车图标 -->
            <img :src="quickCartIcon" alt="" width="25" height="25" />
          </button>
        </div>
      </div>
    </div>
  </article>
</template>

<script setup>
import { toRefs, computed } from 'vue'
import PriceDisplay from '@components/Common/PriceDisplay.vue'
import { getBizCode } from '@utils/curEnv.js'
import woQuickCart from '@/static/images/wo-quick-cart.png'
import jdQuickCart from '@/static/images/jd-quick-cart.png'

// ==================== 组件属性定义 ====================
// 定义组件接收的props参数
const props = defineProps({
  // 商品数据对象，包含商品的所有展示信息
  // 包括：showImageUrl(图片)、name(名称)、params(规格)、price(价格)等
  item: {
    type: Object,
    required: true
  }
})

// 使用toRefs解构props，保持响应性
// 确保在组件内部使用商品数据时能够正确响应props的变化
const { item } = toRefs(props)

// 定义组件向父组件发射的事件
const emit = defineEmits(['item-click', 'add-cart'])

// ==================== 业务逻辑计算 ====================
// 根据业务代码动态选择购物车图标
// YGJD业务使用京东风格图标，其他业务使用沃商城图标
const quickCartIcon = computed(() => (getBizCode() === 'ygjd' ? jdQuickCart : woQuickCart))

// 判断是否为ZQ业务场景
// ZQ业务有特殊的价格展示逻辑和功能限制
const isZQ = computed(() => getBizCode() === 'zq')

// ==================== 用户交互事件处理 ====================
// 处理商品卡片点击事件
// 当用户点击商品卡片时，向父组件传递商品信息用于跳转详情页
const handleItemClick = () => {
  emit('item-click', item.value)
}

// 处理商品加购事件
// 当用户点击购物车按钮时，向父组件传递商品信息用于加入购物车
const handleAddCart = () => {
  emit('add-cart', item.value)
}
</script>

<style scoped lang="less">
.waterfall-item {
  break-inside: avoid;
  cursor: pointer;

  .waterfall-card {
    background-color: #FFFFFF;
    overflow: hidden;

    .waterfall-image {
      width: 100%;
      overflow: hidden;
      position: relative;
      border-radius: 8px;

      img {
        width: 100%;
        height: auto;
        object-fit: cover;
        border-radius: 8px;
      }
    }

    .waterfall-content {
      margin-top: 10px;

      .waterfall-title {
        font-size: 13px;
        color: #333333;
        margin: 0;
        font-weight: normal;
        line-height: 1.5;
        display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      overflow: hidden;
      word-break: break-all;
      }

      .waterfall-spec {
        font-size: 11px;
        color: #666666;
        line-height: 1.5;
        margin: 4px 0 0 0;
      }

      .waterfall-sales {
        font-size: 11px;
        color: #999999;
        margin: 4px 0 0 0;
      }

      .waterfall-footer {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-top: 8px;

        .cart-btn {
          background: none;
          border: none;
          padding: 0;
          cursor: pointer;

          img {
            width: 25px;
            height: 25px;
          }
        }
      }
    }
  }
}
</style>
