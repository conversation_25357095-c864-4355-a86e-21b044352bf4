<!--
/**
 * 商品骨架屏加载组件
 *
 * 主要功能：
 * 1. 在商品数据加载期间显示骨架屏占位符，提升用户体验
 * 2. 支持列表模式和瀑布流模式两种不同的骨架屏布局
 * 3. 列表模式：横向布局，模拟商品列表项的结构（图片+信息）
 * 4. 瀑布流模式：网格布局，模拟瀑布流商品卡片的结构
 * 5. 可配置骨架屏数量，根据不同场景调整显示的占位符个数
 * 6. 内置流光动画效果，增强加载状态的视觉反馈
 *
 * 技术特点：
 * - 使用CSS渐变和动画实现流光效果
 * - 响应式设计，适配不同屏幕尺寸
 * - 模块化结构，易于维护和扩展
 * - 轻量级实现，不依赖外部动画库
 *
 * 使用场景：
 * - 商品列表页面的初始加载
 * - 搜索结果页面的数据获取
 * - 分页加载时的过渡状态
 * - 网络较慢时的用户体验优化
 */
-->

<template>
  <!-- 骨架屏容器 -->
  <div class="skeleton-container">
    <!-- 列表模式骨架屏 -->
    <!-- 当非瀑布流模式时显示，模拟标准商品列表项的布局结构 -->
    <div v-if="!isWaterfall" class="list-skeleton">
      <!-- 循环生成指定数量的骨架屏项目 -->
      <div v-for="i in skeletonCount" :key="i" class="skeleton-item">
        <!-- 商品图片占位区域 -->
        <div class="skeleton-image">
          <div class="skeleton-block"></div>
        </div>
        <!-- 商品信息占位区域 -->
        <div class="skeleton-info">
          <!-- 主要信息区域：标题和规格 -->
          <div class="skeleton-info-main">
            <!-- 商品标题占位条 -->
            <div class="skeleton-title"></div>
            <!-- 商品规格占位条 -->
            <div class="skeleton-spec"></div>
          </div>
          <!-- 底部信息区域：价格和购物车按钮 -->
          <div class="skeleton-info-footer">
            <!-- 价格占位条 -->
            <div class="skeleton-price"></div>
            <!-- 购物车按钮占位区域 -->
            <div class="skeleton-cart">
              <div class="skeleton-block"></div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 瀑布流模式骨架屏 -->
    <!-- 当为瀑布流模式时显示，模拟瀑布流商品卡片的布局结构 -->
    <div v-else class="waterfall-skeleton">
      <!-- 网格布局容器，实现两列瀑布流效果 -->
      <div class="skeleton-grid">
        <!-- 循环生成指定数量的骨架屏卡片 -->
        <div v-for="i in skeletonCount" :key="i" class="skeleton-item">
          <!-- 商品图片占位区域 -->
          <div class="skeleton-image">
            <div class="skeleton-block"></div>
          </div>
          <!-- 商品内容占位区域 -->
          <div class="skeleton-content">
            <!-- 商品标题占位条 -->
            <div class="skeleton-title"></div>
            <!-- 商品规格占位条 -->
            <div class="skeleton-spec"></div>
            <!-- 销量信息占位条 -->
            <div class="skeleton-sales"></div>
            <!-- 底部信息区域：价格和购物车按钮 -->
            <div class="skeleton-footer">
              <!-- 价格占位条 -->
              <div class="skeleton-price"></div>
              <!-- 购物车按钮占位区域 -->
              <div class="skeleton-cart">
                <div class="skeleton-block"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { toRefs } from 'vue'

// ==================== 组件属性定义 ====================
// 定义组件接收的props参数
const props = defineProps({
  // 是否使用瀑布流模式的骨架屏布局
  // true: 显示网格布局的瀑布流骨架屏
  // false: 显示列表布局的骨架屏
  isWaterfall: {
    type: Boolean,
    default: false
  },
  // 骨架屏显示的数量
  // 根据不同的加载场景可以调整显示的占位符个数
  skeletonCount: {
    type: Number,
    default: 3
  }
})

// 使用toRefs解构props，保持响应性
// 确保在模板中使用这些值时能够正确响应props的变化
const { isWaterfall, skeletonCount } = toRefs(props)
</script>

<style scoped lang="less">
.skeleton-container {
  margin-top: 10px;

  .skeleton-block {
    background: linear-gradient(90deg, #f2f2f2 25%, #e6e6e6 50%, #f2f2f2 75%);
    background-size: 200% 100%;
    animation: skeleton-loading 1.5s infinite;
    border-radius: 4px;
    width: 100%;
    height: 100%;
  }

  @keyframes skeleton-loading {
    0% { background-position: 200% 0; }
    100% { background-position: -200% 0; }
  }

  .list-skeleton {
    .skeleton-item {
      display: flex;
      background-color: #fff;
      border-radius: 8px;
      overflow: hidden;
      margin-bottom: 12px;

      .skeleton-image {
        width: 85px;
        height: 85px;
        flex-shrink: 0;
      }

      .skeleton-info {
        flex: 1;
        margin-left: 8px;
        padding-top: 8px;
        display: flex;
        flex-direction: column;
        justify-content: space-between;

        .skeleton-info-main {
          .skeleton-title {
            height: 13px;
            margin-bottom: 6px;
            border-radius: 4px;
            width: 80%;
          }

          .skeleton-spec {
            height: 11px;
            margin: 4px 0 8px 0;
            border-radius: 4px;
            width: 60%;
          }
        }

        .skeleton-info-footer {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding-bottom: 8px;

          .skeleton-price {
            height: 12px;
            border-radius: 4px;
            width: 40%;
          }

          .skeleton-cart {
            width: 25px;
            height: 25px;
          }
        }
      }
    }
  }

  .waterfall-skeleton {
    .skeleton-grid {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 10px;
    }

    .skeleton-item {
      background-color: #fff;
      border-radius: 8px;
      overflow: hidden;

      .skeleton-image {
        width: 100%;
        height: 120px;
        border-radius: 8px 8px 0 0;
      }

      .skeleton-content {
        padding: 10px;

        .skeleton-title {
          height: 13px;
          margin-bottom: 6px;
          border-radius: 4px;
          width: 90%;
        }

        .skeleton-spec {
          height: 11px;
          margin: 4px 0;
          border-radius: 4px;
          width: 70%;
        }

        .skeleton-sales {
          height: 11px;
          margin: 4px 0;
          border-radius: 4px;
          width: 50%;
        }

        .skeleton-footer {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-top: 8px;

          .skeleton-price {
            height: 12px;
            border-radius: 4px;
            width: 50%;
          }

          .skeleton-cart {
            width: 25px;
            height: 25px;
          }
        }
      }
    }
  }

  .skeleton-title,
  .skeleton-spec,
  .skeleton-sales,
  .skeleton-price {
    background: linear-gradient(90deg, #f2f2f2 25%, #e6e6e6 50%, #f2f2f2 75%);
    background-size: 200% 100%;
    animation: skeleton-loading 1.5s infinite;
  }
}
</style>
