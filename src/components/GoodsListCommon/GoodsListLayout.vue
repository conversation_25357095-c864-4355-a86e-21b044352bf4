<!--
/**
 * 商品列表布局组件
 *
 * 主要功能：
 * 1. 提供商品列表的统一布局容器，支持列表模式和瀑布流模式两种展示方式
 * 2. 列表模式：使用van-list组件实现垂直滚动加载，适合标准的商品列表展示
 * 3. 瀑布流模式：使用WaterfallSection组件实现瀑布流布局，适合图片类商品展示
 * 4. 集成骨架屏加载状态，提供良好的加载体验
 * 5. 统一处理空状态展示，当无商品数据时显示空状态提示
 * 6. 支持无限滚动加载，自动触发数据加载和状态管理
 *
 * 技术特点：
 * - 根据isWaterfall属性自动切换展示模式
 * - 统一的事件处理机制，向上传递商品点击和加购事件
 * - 响应式设计，支持不同屏幕尺寸的适配
 * - 骨架屏数量根据展示模式自动调整
 *
 * 使用场景：
 * - 商品搜索结果页面
 * - 分类商品列表页面
 * - 推荐商品展示区域
 * - 需要支持多种展示模式的商品列表场景
 */
-->

<template>
  <!-- 商品列表布局主容器 -->
  <div class="goods-list-layout">
    <!-- 骨架屏加载状态 -->
    <!-- 在数据加载时显示骨架屏，根据展示模式调整骨架屏数量 -->
    <GoodsSkeletonLoader
      v-if="isLoading"
      :is-waterfall="isWaterfall"
      :skeleton-count="isWaterfall ? 4 : 3"
    />

    <!-- 列表模式布局 -->
    <!-- 当有商品数据且非瀑布流模式时显示标准列表布局 -->
    <section v-show="goodsList.length > 0 && !isWaterfall" class="list-layout">
      <!-- van-list组件提供无限滚动加载功能 -->
      <!-- 监听滚动事件，自动触发数据加载和状态更新 -->
      <van-list
        :loading="loading"
        :finished="finished"
        finished-text="没有更多了"
        @load="handleLoadMore"
        @update:loading="handleUpdateLoading"
      >
        <!-- 商品列表容器，使用无序列表结构 -->
        <ul class="goods-list-container">
          <!-- 遍历商品数据，为每个商品创建列表项 -->
          <!-- 使用skuId作为key确保列表项的唯一性和更新性能 -->
          <ProductListItem
            v-for="(item, index) in goodsList"
            :key="`goods-${item.skuId || index}`"
            :item="item"
            @item-click="handleItemClick"
            @add-cart="handleAddCart"
          />
        </ul>
      </van-list>
    </section>

    <!-- 瀑布流模式布局 -->
    <!-- 当有商品数据且为瀑布流模式时显示瀑布流布局 -->
    <section v-show="goodsList.length > 0 && isWaterfall" class="waterfall-layout">
      <!-- 瀑布流组件，提供瀑布流布局和滚动加载功能 -->
      <!-- 配置瀑布流的各种参数，包括加载状态、完成状态等 -->
      <WaterfallSection
        :waterfall-goods-list="goodsList"
        :waterfall-loading="loading"
        :waterfall-finished="finished"
        :waterfall-button-can-show="false"
        :waterfall-render-complete="true"
        :skeleton-states="{ waterfall: false }"
        load-mode="scroll"
        @load-more="handleWaterfallLoadMore"
      >
        <!-- 瀑布流商品项插槽 -->
        <!-- 为每个商品提供瀑布流样式的展示组件 -->
        <template #item="{ item }">
          <GoodsWaterfallItem
            :item="item"
            @item-click="handleItemClick"
            @add-cart="handleAddCart"
          />
        </template>
        
        <!-- 瀑布流空状态插槽 -->
        <!-- 在瀑布流模式下的空状态展示 -->
        <template #empty>
          <section v-if="goodsList.length <= 0 && !isLoading && !isWaterfall" class="empty-state">
            <WoEmpty :description="emptyDescription" />
          </section>
        </template>
      </WaterfallSection>
    </section>

    <!-- 通用空状态展示 -->
    <!-- 当没有商品数据且不在加载状态时显示空状态提示 -->
    <section v-if="goodsList.length <= 0 && !isLoading" class="empty-state">
      <WoEmpty :description="emptyDescription" />
    </section>
  </div>
</template>

<script setup>
import { toRefs } from 'vue'
import WoEmpty from '@/components/WoElementCom/WoEmpty.vue'
import GoodsSkeletonLoader from './GoodsSkeletonLoader.vue'
import ProductListItem from './ProductListItem.vue'
import GoodsWaterfallItem from './GoodsWaterfallItem.vue'
import WaterfallSection from './WaterfallSection.vue'
import { getDefaultBreakpoints } from '@/config/responsive.js'

// ==================== 组件属性定义 ====================
// 定义组件接收的props参数
const props = defineProps({
  // 商品列表数据数组，包含所有需要展示的商品信息
  goodsList: {
    type: Array,
    default: () => []
  },
  // 初始加载状态，控制骨架屏的显示
  isLoading: {
    type: Boolean,
    default: false
  },
  // 滚动加载状态，控制加载更多的显示
  loading: {
    type: Boolean,
    default: false
  },
  // 数据加载完成状态，控制是否还有更多数据
  finished: {
    type: Boolean,
    default: false
  },
  // 是否使用瀑布流布局模式
  isWaterfall: {
    type: Boolean,
    default: false
  },
  // 响应式断点配置，用于不同屏幕尺寸的适配
  breakpoints: {
    type: Object,
    default: () => getDefaultBreakpoints()
  },
  // 空状态时的描述文本
  emptyDescription: {
    type: String,
    default: '暂无商品'
  }
})

// 使用toRefs解构props，保持响应性
// 确保在组件内部使用这些值时能够正确响应props的变化
const {
  goodsList,
  isLoading,
  loading,
  finished,
  isWaterfall,
  emptyDescription
} = toRefs(props)

// 定义组件向父组件发射的事件
const emit = defineEmits(['load-more', 'item-click', 'add-cart', 'update:loading'])

// ==================== 列表模式事件处理 ====================
// 处理列表模式的加载更多事件
// 当用户滚动到底部时触发，通知父组件加载更多数据
const handleLoadMore = () => {
  emit('load-more')
}

// 处理列表模式的加载状态更新事件
// 同步van-list组件的loading状态到父组件
const handleUpdateLoading = (value) => {
  emit('update:loading', value)
}

// ==================== 瀑布流模式事件处理 ====================
// 处理瀑布流模式的加载更多事件
// 瀑布流滚动触底时触发，模拟van-list的update:loading以兼容父组件
const handleWaterfallLoadMore = () => {
  // 先设置loading状态为true，然后触发加载更多事件
  emit('update:loading', true)
  emit('load-more')
}

// ==================== 商品交互事件处理 ====================
// 处理商品点击事件
// 当用户点击商品时，将商品信息传递给父组件
const handleItemClick = (item) => {
  emit('item-click', item)
}

// 处理商品加购事件
// 当用户点击加购按钮时，将商品信息传递给父组件
const handleAddCart = (item) => {
  emit('add-cart', item)
}
</script>

<style scoped lang="less">
.goods-list-layout {
  .list-layout {
    margin-top: 2px;

    .goods-list-container {
      list-style: none;
      padding: 0;
      margin: 0;
      display: flex;
      flex-direction: column;
      gap: 10px;
    }
  }

  .waterfall-layout {
    :deep(.home-waterfall-container) {
      padding: 0;
    }
  }

  .empty-state {
    padding: 40px 0;
  }
}
</style>
