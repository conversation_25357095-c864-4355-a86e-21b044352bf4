<!--
/**
 * 商品基础信息展示组件
 *
 * 主要功能：
 * 1. 展示商品的价格信息，包括当前价格和原价对比
 * 2. 显示商品标题名称，支持多行文本显示和溢出处理
 * 3. 价格组件化显示，支持不同尺寸和颜色的价格展示
 * 4. 原价划线显示，突出价格优惠信息
 *
 * 技术特点：
 * - 使用PriceDisplay组件统一价格显示格式
 * - 响应式布局，适配不同屏幕尺寸
 * - CSS多行文本截断，优化长标题显示效果
 * - 条件渲染，仅在有原价且高于现价时显示划线价格
 *
 * 使用场景：
 * - 商品详情页基础信息展示区域
 * - 商品列表页商品信息卡片
 * - 需要展示商品价格和标题的任何页面组件
 */
-->

<template>
  <!-- 商品基础信息主容器 -->
  <section class="basic-info-section">
    <!-- 价格信息展示区域 -->
    <div class="price-wrapper">
      <!-- 当前价格组件，使用大尺寸橙色显示 -->
      <PriceDisplay :price="goodsInfo.price" size="large" color="orange"/>
      <!-- 原价显示，仅在有原价且高于现价时展示划线效果 -->
      <span v-if="goodsInfo.originalPrice && goodsInfo.originalPrice > goodsInfo.price" class="price-original">
        ¥{{ goodsInfo.originalPrice }}
      </span>
    </div>

    <!-- 商品标题展示区域 -->
    <!-- 支持最多两行显示，超出部分用省略号处理 -->
    <div class="title">
      {{ goodsInfo.name }}
    </div>
  </section>
</template>

<script setup>
import { toRefs } from 'vue'
import PriceDisplay from '@components/Common/PriceDisplay.vue'

// ===================== 组件属性定义 ======================
// 定义组件接收的props参数
const props = defineProps({
  // 商品信息对象，包含商品的基础展示数据
  goodsInfo: {
    type: Object,
    required: true,
    default: () => ({
      name: '',        // 商品名称标题
      price: 0,        // 商品当前价格
      originalPrice: 0, // 商品原价，用于显示划线价格
      imageUrl: ''     // 商品图片URL（虽然此组件未使用，但保留接口完整性）
    })
  }
})

// 使用toRefs解构props，保持响应性
// 解构后可以直接使用goodsInfo属性，同时保持响应式特性
const { goodsInfo } = toRefs(props)
</script>

<style scoped lang="less">
.basic-info-section {
  background-color: #FFFFFF;
  padding: 10px 17px;
  box-sizing: border-box;
}

.price-wrapper {
  display: flex;
  align-items: baseline;
  gap: 12px;
  margin-bottom: 10px;

  .price-original {
    font-size: 14px;
    color: #999999;
    text-decoration: line-through;
  }
}

.title {
  font-size: 16px;
  color: #171E24;
  font-weight: 500;
  line-height: 1.5;
  display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    word-break: break-all;
}
</style>
