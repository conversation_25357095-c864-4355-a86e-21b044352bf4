<!--
/**
 * 商品详情页骨架屏组件
 *
 * 主要功能：
 * 1. 在商品详情页数据加载期间显示占位内容，提升用户体验
 * 2. 模拟真实页面布局结构，包含图片轮播、商品信息、规格选择等区域
 * 3. 提供流畅的加载动画效果，减少用户等待时的焦虑感
 * 4. 保持与实际内容相似的视觉层次和布局比例
 *
 * 技术特点：
 * - 使用CSS渐变和动画实现流畅的加载效果
 * - 响应式布局，适配不同屏幕尺寸
 * - 模块化设计，各个区域独立且可复用
 * - 无JavaScript逻辑，纯CSS实现，性能优异
 *
 * 使用场景：
 * - 商品详情页初始加载时的占位显示
 * - 网络较慢时的友好加载提示
 * - 提升页面加载体验的过渡效果
 */
-->

<template>
  <!-- 商品详情骨架屏主容器 -->
  <div class="goods-detail-skeleton">
    <!-- 商品图片轮播区域骨架屏 -->
    <div class="skeleton-image-section">
      <!-- 轮播图容器骨架 -->
      <div class="skeleton-swiper">
        <!-- 主图片占位区域 -->
        <div class="skeleton-image"></div>
        <!-- 轮播指示点占位区域 -->
        <div class="skeleton-dots">
          <!-- 生成3个指示点占位 -->
          <div class="skeleton-dot" v-for="i in 3" :key="i"></div>
        </div>
      </div>
    </div>

    <!-- 商品基础信息区域骨架屏 -->
    <div class="skeleton-info-section">
      <!-- 价格信息骨架屏 -->
      <div class="skeleton-price-section">
        <!-- 当前价格占位 -->
        <div class="skeleton-price"></div>
        <!-- 原价占位 -->
        <div class="skeleton-original-price"></div>
      </div>

      <!-- 商品标题骨架屏 -->
      <div class="skeleton-title">
        <!-- 标题第一行占位 -->
        <div class="skeleton-title-line"></div>
        <!-- 标题第二行占位，较短 -->
        <div class="skeleton-title-line short"></div>
      </div>

      <!-- 规格选择区域骨架屏 -->
      <div class="skeleton-spec-section">
        <!-- 规格选择头部信息 -->
        <div class="skeleton-spec-header">
          <!-- 规格图标占位 -->
          <div class="skeleton-spec-icon"></div>
          <!-- 规格文本占位 -->
          <div class="skeleton-spec-text"></div>
          <!-- 箭头图标占位 -->
          <div class="skeleton-arrow"></div>
        </div>
        <!-- 规格选项列表 -->
        <div class="skeleton-spec-options">
          <!-- 生成4个规格选项占位 -->
          <div class="skeleton-spec-option" v-for="i in 4" :key="i"></div>
        </div>
      </div>

      <!-- 配送信息区域骨架屏 -->
      <div class="skeleton-delivery-section">
        <!-- 生成3个配送信息项占位 -->
        <div class="skeleton-delivery-item" v-for="i in 3" :key="i">
          <!-- 配送图标占位 -->
          <div class="skeleton-delivery-icon"></div>
          <!-- 配送文本占位 -->
          <div class="skeleton-delivery-text"></div>
        </div>
      </div>
    </div>

    <!-- 商品介绍区域骨架屏 -->
    <div class="skeleton-introduce-section">
      <!-- 介绍内容容器 -->
      <div class="skeleton-introduce-content">
        <!-- 生成5行介绍文本占位 -->
        <div class="skeleton-introduce-line" v-for="i in 5" :key="i"></div>
      </div>
    </div>

    <!-- 底部操作栏骨架屏 -->
    <div class="skeleton-action-bar">
      <!-- 购物车区域占位 -->
      <div class="skeleton-cart-section">
        <!-- 购物车图标占位 -->
        <div class="skeleton-cart-icon"></div>
        <!-- 购物车文本占位 -->
        <div class="skeleton-cart-text"></div>
      </div>
      <!-- 操作按钮区域占位 -->
      <div class="skeleton-button-section">
        <!-- 加入购物车按钮占位 -->
        <div class="skeleton-button"></div>
        <!-- 立即购买按钮占位 -->
        <div class="skeleton-button primary"></div>
      </div>
    </div>
  </div>
</template>

<script setup>
// ===================== 骨架屏组件说明 ======================
// 此组件为纯展示型骨架屏，不包含任何业务逻辑
// 主要通过CSS动画和布局实现加载占位效果
// 无需props传入，无需事件处理，无需状态管理
</script>

<style scoped lang="less">
.goods-detail-skeleton {
  min-height: 100vh;
  background-color: #FFFFFF;
  padding-bottom: 55px;
}

// 骨架屏动画
@keyframes skeleton-loading {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

.skeleton-base {
  background: linear-gradient(90deg, #f5f5f5 25%, #e8e8e8 50%, #f5f5f5 75%);
  background-size: 200px 100%;
  animation: skeleton-loading 1.2s ease-in-out infinite;
  border-radius: 4px;
}

// 图片区域骨架屏
.skeleton-image-section {
  background-color: #FFFFFF;

  .skeleton-swiper {
    position: relative;

    .skeleton-image {
      background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%); background-size: 200% 100%; animation: skeleton-loading 1.5s infinite;;
      width: 100%;
      height: 375px; // 与实际轮播图高度一致
      border-radius: 0;
    }

    .skeleton-dots {
      position: absolute;
      bottom: 12px;
      left: 50%;
      transform: translateX(-50%);
      display: flex;
      gap: 8px;

      .skeleton-dot {
        background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%); background-size: 200% 100%; animation: skeleton-loading 1.5s infinite;;
        width: 8px;
        height: 8px;
        border-radius: 50%;
      }
    }
  }
}

// 商品信息区域骨架屏
.skeleton-info-section {
  background-color: #FFFFFF;
  padding: 13px 17px;

  .skeleton-price-section {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 10px;

    .skeleton-price {
      background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%); background-size: 200% 100%; animation: skeleton-loading 1.5s infinite;;
      width: 120px;
      height: 32px;
    }

    .skeleton-original-price {
      background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%); background-size: 200% 100%; animation: skeleton-loading 1.5s infinite;;
      width: 80px;
      height: 16px;
    }
  }

  .skeleton-title {
    margin-bottom: 16px;

    .skeleton-title-line {
      background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%); background-size: 200% 100%; animation: skeleton-loading 1.5s infinite;;
      height: 20px;
      margin-bottom: 8px;

      &.short {
        width: 60%;
      }
    }
  }

  .skeleton-spec-section {
    margin-bottom: 20px;

    .skeleton-spec-header {
      display: flex;
      align-items: center;
      margin-bottom: 12px;

      .skeleton-spec-icon {
        background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%); background-size: 200% 100%; animation: skeleton-loading 1.5s infinite;;
        width: 16px;
        height: 16px;
        border-radius: 50%;
        margin-right: 8px;
      }

      .skeleton-spec-text {
        background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%); background-size: 200% 100%; animation: skeleton-loading 1.5s infinite;;
        flex: 1;
        height: 16px;
        margin-right: 8px;
      }

      .skeleton-arrow {
        background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%); background-size: 200% 100%; animation: skeleton-loading 1.5s infinite;;
        width: 8px;
        height: 8px;
      }
    }

    .skeleton-spec-options {
      display: flex;
      gap: 12px;

      .skeleton-spec-option {
        background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%); background-size: 200% 100%; animation: skeleton-loading 1.5s infinite;;
        width: 60px;
        height: 60px;
        border-radius: 8px;
      }
    }
  }

  .skeleton-delivery-section {
    .skeleton-delivery-item {
      display: flex;
      align-items: center;
      margin-bottom: 12px;

      .skeleton-delivery-icon {
        background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%); background-size: 200% 100%; animation: skeleton-loading 1.5s infinite;;
        width: 16px;
        height: 16px;
        border-radius: 50%;
        margin-right: 8px;
      }

      .skeleton-delivery-text {
        background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%); background-size: 200% 100%; animation: skeleton-loading 1.5s infinite;;
        flex: 1;
        height: 16px;
      }
    }
  }
}

// 商品介绍区域骨架屏
.skeleton-introduce-section {
  background-color: #FFFFFF;
  margin-top: 8px;
  padding: 16px;

  .skeleton-introduce-content {
    .skeleton-introduce-line {
      background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%); background-size: 200% 100%; animation: skeleton-loading 1.5s infinite;;
      height: 16px;
      margin-bottom: 12px;

      &:nth-child(odd) {
        width: 100%;
      }

      &:nth-child(even) {
        width: 80%;
      }

      &:last-child {
        margin-bottom: 0;
        width: 60%;
      }
    }
  }
}

// 底部操作栏骨架屏
.skeleton-action-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #FFFFFF;
  padding: 10px 17px;
  display: flex;
  align-items: center;
  gap: 12px;
  border-top: 1px solid #f0f0f0;

  .skeleton-cart-section {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 4px;

    .skeleton-cart-icon {
      background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%); background-size: 200% 100%; animation: skeleton-loading 1.5s infinite;;
      width: 24px;
      height: 24px;
      border-radius: 50%;
    }

    .skeleton-cart-text {
      background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%); background-size: 200% 100%; animation: skeleton-loading 1.5s infinite;;
      width: 32px;
      height: 12px;
    }
  }

  .skeleton-button-section {
    flex: 1;
    display: flex;
    gap: 8px;

    .skeleton-button {
      background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%); background-size: 200% 100%; animation: skeleton-loading 1.5s infinite;;
      flex: 1;
      height: 44px;
      border-radius: 22px;

      &.primary {
        background: linear-gradient(90deg, #ff9a4a 25%, #ff7a0a 50%, #ff9a4a 75%);
        background-size: 200px 100%;
        animation: skeleton-loading 1.5s infinite;
      }
    }
  }
}
</style>
