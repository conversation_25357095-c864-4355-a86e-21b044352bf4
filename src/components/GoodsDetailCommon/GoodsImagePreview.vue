<!--
/**
 * 商品图片预览组件
 *
 * 主要功能：
 * 1. 提供商品图片的全屏预览功能，支持手势滑动切换
 * 2. 过滤并显示图片类型的媒体内容，排除视频等其他类型
 * 3. 自定义图片索引显示，显示当前图片位置和总数
 * 4. 支持关闭按钮和手势关闭操作
 * 5. 根据初始索引定位到指定图片位置
 *
 * 技术特点：
 * - 基于Vant UI的ImagePreview组件封装
 * - 响应式数据管理，支持双向绑定
 * - 智能索引计算，处理混合媒体类型的索引映射
 * - 自定义样式覆盖，提供更好的视觉体验
 *
 * 使用场景：
 * - 商品详情页图片轮播点击预览
 * - 商品图片列表的大图查看
 * - 需要全屏查看商品图片的任何场景
 */
-->

<template>
  <!-- Vant图片预览组件 -->
  <!-- 支持双向绑定显示状态，配置预览参数和事件处理 -->
  <van-image-preview
    v-model:show="show"
    :images="imageList"
    :start-position="startPosition"
    :closeable="true"
    :show-index="true"
    :swipe-duration="300"
    @change="handleChange"
    @close="handleClose"
  >
    <!-- 自定义图片索引显示模板 -->
    <template #index>
      <!-- 显示当前图片位置和总图片数 -->
      <div class="custom-index">
        {{ currentIndex + 1 }} / {{ imageList.length }}
      </div>
    </template>
  </van-image-preview>
</template>

<script setup>
import { ref, computed, watch, toRefs } from 'vue'

// ===================== 组件属性定义 ======================
// 定义组件接收的props参数
const props = defineProps({
  // 预览窗口的显示状态，支持双向绑定
  visible: {
    type: Boolean,
    default: false
  },
  // 图片数组，包含不同类型的媒体项目
  images: {
    type: Array,
    default: () => []
  },
  // 初始显示的图片索引位置
  initialIndex: {
    type: Number,
    default: 0
  }
})

// 使用toRefs解构props，保持响应性
// 解构后可以直接使用各个属性，同时保持响应式特性
const { visible, images, initialIndex } = toRefs(props)

// 定义组件向父组件发射的事件
// update:visible: 预览状态变化时触发，用于双向绑定
// change: 图片切换时触发，传递当前图片索引
// close: 预览关闭时触发
const emit = defineEmits(['update:visible', 'change', 'close'])

// ===================== 响应式状态管理 ======================
// 内部预览显示状态，与外部visible属性同步
const show = ref(false)
// 当前显示的图片索引
const currentIndex = ref(0)
// 预览开始的图片位置
const startPosition = ref(0)

// ===================== 计算属性 ======================
// 过滤并提取纯图片列表，排除视频等其他媒体类型
// 只保留type为'image'的项目，并提取其url用于预览
const imageList = computed(() => {
  return images.value
    .filter(item => item.type === 'image')
    .map(item => item.url)
})

// ===================== 监听器 ======================
// 监听外部visible属性变化，同步内部显示状态
watch(visible, (newVal) => {
  show.value = newVal
  if (newVal) {
    // 当预览打开时，计算在纯图片列表中的正确位置
    // 因为原始列表可能包含视频等非图片项目，需要重新映射索引
    const imageItems = images.value.filter(item => item.type === 'image')
    const targetImageIndex = imageItems.findIndex((_, index) => {
      // 找到原始列表中对应的图片索引
      let imageCount = 0
      for (let i = 0; i <= initialIndex.value; i++) {
        if (images.value[i]?.type === 'image') {
          if (imageCount === index) {
            return i === initialIndex.value
          }
          imageCount++
        }
      }
      return false
    })

    // 设置预览开始位置，确保不小于0
    startPosition.value = Math.max(0, targetImageIndex)
    currentIndex.value = startPosition.value
  }
})

// 监听内部show状态变化，向父组件同步状态
watch(show, (newVal) => {
  emit('update:visible', newVal)
})

// ===================== 用户操作处理 ======================
// 处理图片切换事件
// 当用户滑动切换图片时触发，更新当前索引并通知父组件
const handleChange = (index) => {
  currentIndex.value = index
  emit('change', index)
}

// 处理预览关闭事件
// 当用户点击关闭按钮或手势关闭时触发
const handleClose = () => {
  show.value = false
  emit('close')
}
</script>

<style scoped lang="less">
.custom-index {
  position: absolute;
  top: 20px;
  right: 20px;
  background-color: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 8px 12px;
  border-radius: 4px;
  font-size: 14px;
  z-index: 1000;
}

:deep(.van-image-preview__image) {
  background-color: #000;
}

:deep(.van-image-preview__close) {
  top: 20px;
  right: 60px;
  color: white;
  font-size: 24px;
}
</style>
