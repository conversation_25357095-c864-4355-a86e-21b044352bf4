<!--
/**
 * 商品状态提示组件
 *
 * 主要功能：
 * 1. 显示商品各种状态的提示信息
 * 2. 支持多种状态类型（下架、无货、权限、区域、限购）
 * 3. 固定定位在页面底部，不影响用户操作
 * 4. 半透明背景，毛玻璃效果
 * 5. 滑入动画效果
 *
 * 技术特点：
 * - 条件渲染，根据状态动态显示
 * - CSS动画和过渡效果
 * - 响应式设计，适配不同屏幕
 * - 层级管理，确保提示可见
 * - 毛玻璃背景效果
 *
 * 使用场景：
 * - 商品详情页状态提示
 * - 购买流程中的限制提示
 * - 用户权限验证提示
 */
-->

<template>
  <!-- 状态提示遮罩层 -->
  <div v-if="isDataGet" class="status-tips-overlay">
    <!-- 状态提示容器 -->
    <div class="status-tips-container">
      <!-- 商品下架提示 -->
      <div class="status-tip tips-state" v-if="!onSaleState">
        <div class="tip-content">
          <div class="tip-title">商品已下架</div>
          <div class="tip-message">该商品已下架，请选购其他商品!</div>
        </div>
      </div>
      <!-- 库存不足提示 -->
      <div class="status-tip tips-stock" v-if="onSaleState && !stockState">
        <div class="tip-content">
          <div class="tip-title">暂时无货</div>
          <div class="tip-message">所选地区暂时无货，非常抱歉！</div>
        </div>
      </div>
      <!-- 用户权限提示 -->
      <div class="status-tip tips-permission" v-if="!userStatus">
        <div class="tip-content">
          <div class="tip-title">无购买资格</div>
          <div class="tip-message">您暂无购买资格，非常抱歉！</div>
        </div>
      </div>
      <!-- 区域限制提示 -->
      <div class="status-tip tips-region" v-if="!regionalSalesState">
        <div class="tip-content">
          <div class="tip-title">区域限制</div>
          <div class="tip-message">抱歉，此商品在所选区域暂不支持销售!</div>
        </div>
      </div>
      <!-- 限购提示 -->
      <div class="status-tip tips-limit" v-if="!limitState">
        <div class="tip-content">
          <div class="tip-title">限购商品</div>
          <div class="tip-message">该商品限购，请选购其他商品!</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { toRefs } from 'vue'

// ===================== 组件属性定义 ======================
// 定义组件接收的props参数
const props = defineProps({
  // 数据是否已获取，控制整个组件显示
  isDataGet: {
    type: Boolean,
    default: false
  },
  // 商品是否在售状态
  onSaleState: {
    type: Boolean,
    default: true
  },
  // 库存状态，true表示有库存
  stockState: {
    type: Boolean,
    default: true
  },
  // 用户购买资格状态
  userStatus: {
    type: Boolean,
    default: true
  },
  // 区域销售状态，true表示该区域可销售
  regionalSalesState: {
    type: Boolean,
    default: true
  },
  // 限购状态，true表示无限购限制
  limitState: {
    type: Boolean,
    default: true
  }
})

// 使用toRefs解构props，保持响应性
const {
  isDataGet,
  onSaleState,
  stockState,
  userStatus,
  regionalSalesState,
  limitState
} = toRefs(props)
</script>

<style scoped lang="less">
.status-tips-overlay {
  position: fixed;
  bottom: 49px;
  left: 0;
  right: 0;
  z-index: 999;
  pointer-events: none;

  .status-tips-container {
    pointer-events: auto;

    .status-tip {
      display: flex;
      align-items: flex-start;
      background: rgba(0, 0, 0, 0.85);
      backdrop-filter: blur(10px);
      border-radius: 12px;
      margin: 10px 5px;
      padding: 5px 10px;
      box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
      animation: slideInUp 0.3s ease-out;
      box-sizing: border-box;

      .tip-content {
        flex: 1;

        .tip-title {
          color: #FFFFFF;
          font-size: 16px;
          font-weight: 600;
          margin-bottom: 4px;
          line-height: 1.5;
        }

        .tip-message {
          color: rgba(255, 255, 255, 0.8);
          font-size: 14px;
          line-height: 1.5;
        }
      }
    }
  }
}

// 滑入动画
@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>
