<!--
/**
 * 促销活动展示组件
 *
 * 主要功能：
 * 1. 展示商品相关的促销活动列表
 * 2. 支持活动项的点击交互，跳转到活动详情
 * 3. 条件渲染，仅在自营商品且有活动数据时显示
 * 4. 渐变背景设计，提供视觉吸引力
 * 5. 响应式布局，适配不同屏幕尺寸
 *
 * 技术特点：
 * - 业务逻辑判断，根据bizCode控制显示
 * - 列表渲染优化，使用key确保更新性能
 * - 事件委托机制，支持父组件处理活动点击
 * - CSS渐变背景，提供现代化视觉效果
 * - 灵活的布局设计，支持多个活动项展示
 *
 * 使用场景：
 * - 自营商品的促销活动展示
 * - 需要突出显示优惠信息的场景
 * - 活动页面跳转的入口组件
 */
-->

<template>
  <!-- 促销活动展示区域 -->
  <!-- 仅在自营商品且有促销活动数据时显示 -->
  <section 
    class="promotion-section" 
    v-if="promotionList && promotionList.length > 0 && bizCode === 'ziying'"
  >
    <!-- 促销活动容器 -->
    <div class="promotion-activity">
      <!-- 活动标题 -->
      <div class="promotion-activity-title">
        优惠活动
      </div>
      
      <!-- 活动内容区域 -->
      <div class="promotion-activity-content">
        <!-- 促销活动项列表 -->
        <div
          class="promotion-item"
          v-for="(item, index) in promotionList"
          :key="index"
          @click="handlePromotionClick(item)"
        >
          <!-- 活动图标 -->
          <img 
            :src="item.activityImage" 
            alt="优惠活动" 
            class="promotion-heart-img"
          >
          
          <!-- 活动名称 -->
          <div class="promotion-content">
            {{ item.activityName }}
          </div>
          
          <!-- 活动描述和箭头 -->
          <div class="promotion-arrow">
            <span class="promotion-desc">{{ item.activityDec }}</span>
            <i class="arrow-icon"></i>
          </div>
        </div>
      </div>
    </div>
  </section>
</template>

<script setup>
import { toRefs } from 'vue'

// ===================== 组件属性定义 ======================
// 定义组件接收的props参数
const props = defineProps({
  // 促销活动列表，包含活动的详细信息
  promotionList: {
    type: Array,
    default: () => []
  },
  // 业务代码，用于判断是否为自营商品
  bizCode: {
    type: String,
    default: ''
  }
})

// 使用toRefs解构props，保持响应性
const { promotionList, bizCode } = toRefs(props)

// 定义组件事件
const emit = defineEmits(['promotion-click'])

// ===================== 事件处理函数 ======================
// 处理促销活动点击事件，向父组件传递活动数据
const handlePromotionClick = (item) => {
  emit('promotion-click', item)
}
</script>

<style scoped lang="less">
.promotion-section {
  background-color: #FFFFFF;
  padding: 5px 0;
  box-sizing: border-box;
}

.promotion-activity {
  background: linear-gradient(90deg, #FF6362 0%, #FFBE7A 100%);
  padding: 10px;

  .promotion-activity-title {
    color: #FFFFFF;
    font-size: 16px;
    font-weight: 500;
    margin-bottom: 8px;
  }

  .promotion-activity-content {
    display: flex;
    align-items: center;
    border-radius: 8px;
    padding: 13px;
    background: linear-gradient(90deg, #FFEEEB 0%, #FFFAF9 100%);

    .promotion-item {
      display: flex;
      align-items: center;
      width: 100%;
      margin-bottom: 15px;
      border-bottom: 1px solid #f5f5f5;
      cursor: pointer;
      transition: all 0.3s ease;

      &:last-child {
        border-bottom: none;
        margin-bottom: 0;
        padding-bottom: 0;
      }

      .promotion-heart-img {
        width: 25px;
        height: 20px;
        margin-right: 12px;
        flex-shrink: 0;
      }

      .promotion-content {
        flex: 1;
        font-size: 14px;
        color: #171E24;
        font-weight: 500;
        line-height: 1.4;
      }

      .promotion-arrow {
        display: flex;
        align-items: center;
        flex-shrink: 0;

        .promotion-desc {
          font-size: 14px;
          color: var(--wo-biz-theme-color);
          margin-right: 8px;
        }

        .arrow-icon {
          display: inline-block;
          width: 8px;
          height: 8px;
          border-top: 2px solid var(--wo-biz-theme-color);
          border-right: 2px solid var(--wo-biz-theme-color);
          transform: rotate(45deg);
        }
      }
    }
  }
}
</style>
