<!--
/**
 * 商品规格选择组件
 *
 * 主要功能：
 * 1. 显示当前已选择的商品规格信息
 * 2. 提供规格选项的横向滚动选择界面
 * 3. 支持规格选项的选中状态和禁用状态
 * 4. 根据业务代码条件显示规格选择区域
 * 5. 提供规格数量统计信息
 *
 * 技术特点：
 * - 响应式设计，支持横向滚动
 * - 多状态支持（选中、禁用、默认）
 * - 事件冒泡控制，精确处理点击事件
 * - 业务逻辑分离，支持不同业务场景
 * - DOM引用暴露，支持外部操作
 *
 * 使用场景：
 * - 商品详情页的规格选择
 * - 需要图片展示的规格选择
 * - 支持多种状态的选择组件
 */
-->

<template>
  <!-- 规格选择区域容器 -->
  <section class="spec-section">
    <!-- 规格选择主体包装器 -->
    <div class="spec-wrapper" @click="handleSpecClick">
      <!-- 规格选择图标 -->
      <img src="./assets/round_check.png" alt="" class="spec-icon" />
      
      <!-- 规格选择主要内容区域 -->
      <div class="spec-main">
        <!-- 已选规格显示区域 -->
        <div class="spec-selected">
          <span class="spec-label">已选：</span>
          <span class="spec-value">{{ selectedSpec }}</span>
          <img src="../../static/images/arrow-right-gray.png" alt="" class="arrow-icon" />
        </div>

        <!-- 规格选项列表，根据业务代码条件显示 -->
        <div class="spec-options-wrapper" v-if="bizCode !== 'zq'">
          <!-- 规格选项横向滚动容器 -->
          <div class="spec-options" ref="specOptionsRef">
            <!-- 单个规格选项 -->
            <div
              v-for="spec in specOptions"
              :key="spec.id"
              class="spec-option"
              :class="{ 'is-active': spec.selected, 'is-disabled': spec.disabled, 'active': spec.selected }"
              @click.stop="handleSelectSpec(spec)"
            >
              <!-- 规格图片 -->
              <img v-if="spec.image" :src="spec.image" alt="" class="spec-image" />
              <!-- 选中状态标识 -->
              <img v-if="spec.selected" src="./assets/diagonal-hook.png" alt="" class="spec-check" />
            </div>
          </div>
          <!-- 规格数量统计 -->
          <div class="spec-count">共{{ specOptions.length }}款可选</div>
        </div>
      </div>
    </div>
  </section>
</template>

<script setup>
import { getBizCode } from '@/utils/curEnv'
import { ref, toRefs } from 'vue'

// ===================== 组件属性定义 ======================
// 定义组件接收的props参数
const props = defineProps({
  // 当前已选择的规格描述文本
  selectedSpec: {
    type: String,
    default: ''
  },
  // 可选择的规格选项列表
  specOptions: {
    type: Array,
    default: () => []
  }
})

// 使用toRefs解构props，保持响应性
const { selectedSpec, specOptions } = toRefs(props)

// 定义组件事件
const emit = defineEmits(['spec-click', 'select-spec'])

// ===================== 业务逻辑和状态管理 ======================
// 获取当前业务代码，用于控制显示逻辑
const bizCode = getBizCode()

// 规格选项容器的DOM引用
const specOptionsRef = ref(null)

// ===================== 事件处理函数 ======================
// 处理规格区域点击事件，触发规格选择弹窗
const handleSpecClick = () => {
  emit('spec-click')
}

// 处理单个规格选项的选择事件
const handleSelectSpec = (spec) => {
  emit('select-spec', spec)
}

// ===================== 组件接口暴露 ======================
// 向父组件暴露DOM引用，支持外部操作
defineExpose({
  specOptionsRef
})
</script>

<style scoped lang="less">
.spec-section {
  background-color: #FFFFFF;
  padding: 5px 17px;
  box-sizing: border-box;
}

.spec-wrapper {
  display: flex;
  align-items: flex-start;
  cursor: pointer;

  .spec-icon {
    width: 16px;
    height: 16px;
    margin-right: 8px;
    margin-top: 2px;
  }

  .spec-main {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
  }

  .spec-selected {
    display: flex;
    align-items: center;
    justify-content: space-between;
    flex: 1;
    margin-bottom: 12px;

    .spec-label {
      font-size: 13px;
      color: #4A5568;
      margin-right: 4px;
    }

    .spec-value {
      font-size: 13px;
      color: #171E24;
      flex: 1;
    }

    .arrow-icon {
      width: 5px;
      height: 9px;
    }
  }
}

.spec-options-wrapper {
  overflow: hidden;
  display: flex;
  align-items: center;

  .spec-options {
    flex: 1;
    display: flex;
    gap: 12px;
    overflow-x: auto;
    overflow-y: hidden;

    &::-webkit-scrollbar {
      height: 0px;
    }

    &::-webkit-scrollbar-track {
      background: #f1f1f1;
      border-radius: 2px;
    }

    &::-webkit-scrollbar-thumb {
      background: #c1c1c1;
      border-radius: 2px;
    }

    &::-webkit-scrollbar-thumb:hover {
      background: #a8a8a8;
    }

    .spec-option {
      position: relative;
      width: 60px;
      height: 60px;
      border-radius: 8px;
      overflow: hidden;
      cursor: pointer;
      border: 2px solid transparent;
      flex-shrink: 0;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      background-color: #f5f5f5;

      &.is-active {
        border-color: var(--wo-biz-theme-color);
      }

      &.active {
        border-color: var(--wo-biz-theme-color);
      }

      &.is-disabled {
        opacity: 0.5;
        cursor: not-allowed;
      }

      .spec-image {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }

      .spec-name {
        font-size: 11px;
        color: #171E24;
        text-align: center;
        padding: 2px;
        overflow: hidden; text-overflow: ellipsis; white-space: nowrap;;
      }

      .spec-check {
        position: absolute;
        top: -1px;
        right: -1px;
        width: 16px;
        height: 16px;
      }
    }
  }

  .spec-count {
    flex-shrink: 0;
    font-size: 12px;
    color: #999999;
    margin-left: 5px;
  }
}
</style>
