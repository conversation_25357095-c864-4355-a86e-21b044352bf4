<!--
/**
 * 营销活动展示组件
 *
 * 主要功能：
 * 1. 展示商品相关的营销活动图片
 * 2. 处理营销图片的点击事件，支持跳转到活动页面
 * 3. 智能显示控制，只在有有效营销数据时渲染
 * 4. 响应式图片展示，适配不同屏幕尺寸
 *
 * 技术特点：
 * - 条件渲染，避免空数据时的无效展示
 * - 事件委托机制，支持父组件处理营销点击逻辑
 * - 全屏宽度图片展示，提供沉浸式营销体验
 * - 简洁的组件设计，专注于营销内容展示
 *
 * 使用场景：
 * - 商品详情页的营销活动展示
 * - 促销信息的图片化展示
 * - 需要跳转到营销页面的场景
 */
-->

<template>
  <!-- 营销活动展示区域 -->
  <!-- 只在有有效营销模板数据且包含图片时显示 -->
  <section 
    class="marketing-section" 
    v-if="marketTemplates && marketTemplates.length > 0 && marketTemplates[0].upImage"
  >
    <!-- 营销图片容器 -->
    <div class="goods-marketing">
      <!-- 营销活动图片，支持点击交互 -->
      <img 
        @click="handleMarketingClick" 
        :src="marketTemplates[0].upImage" 
        alt="营销活动" 
      />
    </div>
  </section>
</template>

<script setup>
import { toRefs } from 'vue'

// ===================== 组件属性定义 ======================
// 定义组件接收的props参数
const props = defineProps({
  // 营销模板数据数组，包含营销活动的图片和相关信息
  marketTemplates: {
    type: Array,
    default: () => []
  }
})

// 使用toRefs解构props，保持响应性
const { marketTemplates } = toRefs(props)

// 定义组件事件
const emit = defineEmits(['marketing-click'])

// ===================== 事件处理函数 ======================
// 处理营销图片点击事件，向父组件发送点击信号
const handleMarketingClick = () => {
  emit('marketing-click')
}
</script>

<style scoped lang="less">
.marketing-section {
  background-color: #FFFFFF;
  padding: 5px 0;
  box-sizing: border-box;
}

.goods-marketing {
  img {
    width: 100vw;
    height: auto;
    display: block;
    cursor: pointer;
  }
}
</style>
