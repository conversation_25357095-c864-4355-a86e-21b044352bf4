<!--
/**
 * 分享下拉菜单组件
 *
 * 主要功能：
 * 1. 提供固定位置的下拉菜单触发器
 * 2. 支持首页、分类、我的等页面快速导航
 * 3. 条件显示分享功能，根据系统环境判断
 * 4. 流畅的动画效果，提供良好的用户体验
 * 5. 完善的无障碍支持，符合可访问性标准
 *
 * 技术特点：
 * - Vue 3 Transition组件实现动画效果
 * - 事件冒泡控制，防止意外关闭
 * - 响应式设计，适配不同屏幕尺寸
 * - 系统环境检测，智能显示功能项
 * - 路由导航集成，支持页面跳转
 *
 * 使用场景：
 * - 商品详情页的快速导航菜单
 * - 需要固定位置菜单的页面
 * - 多功能入口的聚合组件
 */
-->

<template>
  <!-- 分享下拉菜单容器 -->
  <div
    class="share-dropdown-wrapper"
    v-show="visible"
  >
    <!-- 菜单触发按钮 -->
    <button
      class="share-dropdown__trigger"
      type="button"
      @click.stop="toggleMenu"
      :aria-expanded="show"
      aria-label="打开菜单"
      :class="{ 'share-dropdown__trigger--active': show }"
    >
      <!-- 默认的三点图标，支持插槽自定义 -->
      <slot>
        <div class="share-dropdown__dots">
          <span class="share-dropdown__dot" />
          <span class="share-dropdown__dot" />
          <span class="share-dropdown__dot" />
        </div>
      </slot>
    </button>

    <!-- 遮罩层，点击关闭菜单 -->
    <Transition name="mask-fade">
      <div
        v-if="show"
        class="share-dropdown__mask"
        @click="hidePop"
      />
    </Transition>

    <!-- 下拉菜单内容 -->
    <Transition name="menu-slide">
      <div
        v-if="show"
        class="share-dropdown__menu"
        role="menu"
        aria-label="功能菜单"
      >
        <!-- 菜单导航容器 -->
        <nav class="share-dropdown__nav" @click.stop>
          <!-- 首页导航按钮 -->
          <button
            class="share-dropdown__item"
            type="button"
            role="menuitem"
            @click="toHomeHandler"
          >
            <span class="share-dropdown__icon share-dropdown__icon--home" aria-hidden="true" />
            <span class="share-dropdown__text">首页</span>
          </button>

          <!-- 分类导航按钮 -->
          <button
            class="share-dropdown__item"
            type="button"
            role="menuitem"
            @click="toCategoryHandler"
          >
            <span class="share-dropdown__icon share-dropdown__icon--category" aria-hidden="true" />
            <span class="share-dropdown__text">分类</span>
          </button>

          <!-- 我的页面导航按钮 -->
          <button
            class="share-dropdown__item"
            type="button"
            role="menuitem"
            @click="toUserHandler"
          >
            <span class="share-dropdown__icon share-dropdown__icon--user" aria-hidden="true" />
            <span class="share-dropdown__text">我的</span>
          </button>

          <!-- 分享按钮，根据系统环境条件显示 -->
          <button
            v-if="!isShowShare"
            class="share-dropdown__item"
            type="button"
            role="menuitem"
            @click="shareHandler"
          >
            <span class="share-dropdown__icon share-dropdown__icon--share" aria-hidden="true" />
            <span class="share-dropdown__text">分享</span>
          </button>
        </nav>
      </div>
    </Transition>
  </div>
</template>

<script setup>
import { ref, computed, onActivated, toRefs } from 'vue'
import { useRouter } from 'vue-router'
import { isHarmonyOS } from 'commonkit'

// ===================== 组件属性定义 ======================
// 定义组件接收的props参数
const props = defineProps({
  // 控制组件是否可见的标识
  visible: {
    type: Boolean,
    default: true
  }
})

// 使用toRefs解构props，保持响应性
const { visible } = toRefs(props)

// 定义组件事件
const emit = defineEmits(['share'])

// ===================== 路由和状态管理 ======================
// Vue Router实例，用于页面导航
const router = useRouter()
// 控制下拉菜单显示状态
const show = ref(false)

// ===================== 计算属性 ======================
// 根据系统环境判断是否显示分享功能
const isShowShare = computed(() => {
  return isHarmonyOS
})

// ===================== 菜单控制函数 ======================
// 隐藏下拉菜单
const hidePop = () => {
  show.value = false
}

// 切换菜单显示状态
const toggleMenu = () => {
  show.value = !show.value
}

// ===================== 导航处理函数 ======================
// 跳转到首页并关闭菜单
const toHomeHandler = () => {
  router.push('/home')
  show.value = false
}

// 跳转到分类页面并关闭菜单，添加时间戳防止缓存
const toCategoryHandler = () => {
  router.push({ path: '/category', query: { _t: new Date().getTime().toString() } })
  show.value = false
}

// 跳转到用户页面并关闭菜单
const toUserHandler = () => {
  router.push('/user')
  show.value = false
}

// 处理分享事件，向父组件发送分享信号并关闭菜单
const shareHandler = (e) => {
  emit('share', e)
  show.value = false
}

// ===================== 生命周期 ======================
// 组件激活时自动关闭菜单，避免状态残留
onActivated(() => {
  show.value = false
})
</script>

<style lang="less" scoped>
.share-dropdown-wrapper {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 1000;
}

.share-dropdown__trigger {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 500;;
  padding: 0;
  background: transparent;
  -webkit-tap-highlight-color: transparent;
  transition: all 0.25s ease-out;
  transform-origin: center;

  &:active {
    transform: scale(0.92);
  }

  &--active {
    .share-dropdown__dots {
      background: #FFFFFF;
      box-shadow: 0 6px 24px rgba(0, 0, 0, 0.15), 0 0 0 1px rgba(255, 122, 10, 0.1);
      transform: scale(1.02);

      .share-dropdown__dot {
        background: #FF7A0A;
        transform: scale(1.2);
      }
    }
  }
}

.share-dropdown__dots {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
  padding: 3px;
  background: #FFFFFF;
  border-radius: 50px;
  width: 35px;
  height: 35px;
  justify-content: center;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.12), 0 2px 4px rgba(0, 0, 0, 0.08);
  border: 1px solid rgba(0, 0, 0, 0.06);
  transition: all 0.25s ease-out;

  &:hover {
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15), 0 2px 6px rgba(0, 0, 0, 0.1);
    transform: translateY(-1px);
  }
}

.share-dropdown__dot {
  width: 4px;
  height: 4px;
  background: #171E24;
  border-radius: 50px;
  transition: all 0.25s ease-out;
}

.share-dropdown__mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.4);
  z-index: -1;
}

.share-dropdown__menu {
  position: absolute;
  top: 60px;
  right: 0;
  z-index: 1;
}

.share-dropdown__nav {
  /* 箭头偏移量 */
  /* 箭头大小 */
  /* 菜单阴影 */

  position: relative;
  background: #FFFFFF;
  color: #171E24;
  border-radius: 12px;
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
  overflow: hidden;
  border: 1px solid rgba(0, 0, 0, 0.08);
  min-width: 140px;

  &::before {
    content: '';
    position: absolute;
    top: -10px;
    right: 16px;
    width: 0;
    height: 0;
    border-left: 10px solid transparent;
    border-right: 10px solid transparent;
    border-bottom: 10px solid #FFFFFF;
    filter: drop-shadow(0 -2px 4px rgba(0, 0, 0, 0.08));
  }
}

.share-dropdown__item {
  display: inline-flex; align-items: center; justify-content: center; border: none; border-radius: 4px; cursor: pointer; transition: all 0.3s; font-weight: 500;;
  display: flex;
  align-items: center;
  width: 100%;
  padding: 8px 10px;
  background: transparent;
  border: none;
  border-radius: 0;
  cursor: pointer;
  transition: all 0.3s ease-out;
  -webkit-tap-highlight-color: transparent;
  position: relative;
  min-height: 35px;

  &:first-child {
    border-radius: 12px 12px 0 0;
  }

  &:last-child {
    border-radius: 0 0 12px 12px;
  }

  &:hover {
    background: linear-gradient(90deg, rgba(255, 122, 10, 0.06) 0%, rgba(255, 122, 10, 0.02) 100%);

    .share-dropdown__icon {
      transform: scale(1.1);
    }

    .share-dropdown__text {
      color: #FF7A0A;
      font-weight: 600;
    }
  }

  &:active {
    background: linear-gradient(90deg, rgba(255, 122, 10, 0.12) 0%, rgba(255, 122, 10, 0.04) 100%);
    transform: scale(0.98);
  }

  &:not(:last-child)::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 18px;
    right: 18px;
    height: 1px;
    background: linear-gradient(90deg, transparent 0%, #E2E8EE 20%, #E2E8EE 80%, transparent 100%);
    opacity: 0.6;
  }
}

.share-dropdown__icon {
  display: inline-block;
  width: 22px;
  height: 22px;
  background-size: 22px;
  background-repeat: no-repeat;
  background-position: center center;
  flex-shrink: 0;
  transition: all 0.3s ease-out;
  border-radius: 6px;
  padding: 2px;

  &--home {
    background-image: url(assets/icon-home.png);
  }

  &--user {
    background-image: url(assets/icon-my.png);
  }

  &--share {
    background-image: url(assets/icon-share.png);
  }

  &--category {
    background-image: url(assets/icon-category.png);
  }
}

.share-dropdown__text {
  font-size: 16px; font-weight: 500; color: #171E24; line-height: 1.5;;
  margin-left: 14px;
  flex: 1;
  text-align: left;
  font-size: 16px;
  font-weight: 500;
  transition: all 0.3s ease-out;
  letter-spacing: 0.2px;
}

.mask-fade-enter-active,
.mask-fade-leave-active {
  transition: opacity 0.25s ease-out;
}

.mask-fade-enter-from,
.mask-fade-leave-to {
  opacity: 0;
}

.menu-slide-enter-active {
  transition: all 0.3s cubic-bezier(0.18, 0.89, 0.32, 1.28);
}

.menu-slide-leave-active {
  transition: all 0.3s cubic-bezier(0.4, 0, 1, 1);
}

.menu-slide-enter-from {
  opacity: 0;
  transform: translateY(-12px) scale(0.92);
}

.menu-slide-leave-to {
  opacity: 0;
  transform: translateY(-6px) scale(0.96);
}

.share-dropdown__item {
  &::before {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    bottom: 0;
    width: 3px;
    background: #FF7A0A;
    transform: scaleY(0);
    transition: transform 0.3s ease-out;
    border-radius: 0 2px 2px 0;
  }

  &:hover::before {
    transform: scaleY(1);
  }
}

@media (-webkit-min-device-pixel-ratio: 2) {
  .share-dropdown__icon {
    image-rendering: -webkit-optimize-contrast;
  }
}

</style>
