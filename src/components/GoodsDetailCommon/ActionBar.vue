<!--
/**
 * 商品详情页底部操作栏组件
 *
 * 主要功能：
 * 1. 提供购物车图标和数量显示，支持点击跳转到购物车页面
 * 2. 提供"加入购物车"和"立即购买/立即提交"两个主要操作按钮
 * 3. 根据业务代码(bizCode)动态显示按钮文案，支持不同业务场景
 * 4. 支持按钮禁用状态控制，防止重复操作或无效操作
 * 5. 采用固定定位在页面底部，提供便捷的商品操作入口
 * 6. 集成购物车数量徽章显示，实时反映购物车商品数量
 *
 * 技术特点：
 * - 使用WoActionBar组件作为容器，保持UI一致性
 * - 集成van-badge组件显示购物车数量徽章
 * - 支持事件向上传递，实现父子组件通信
 * - 响应式设计，适配不同屏幕尺寸
 *
 * 使用场景：
 * - 商品详情页底部操作区域
 * - 需要提供购物车和购买功能的商品展示页面
 */
-->

<template>
  <!-- 底部操作栏容器组件 -->
  <WoActionBar>
    <!-- 操作栏主容器，固定在页面底部 -->
    <div class="action-bar-container">
      <!-- 操作内容区域，包含购物车和按钮组 -->
      <div class="action-content">
        <!-- 购物车区域，包含图标、数量徽章和文字 -->
        <div class="cart-wrapper">
          <!-- 购物车图标容器，点击跳转到购物车页面 -->
          <div class="cart-icon-wrapper" @click="handleGoToCart">
            <!-- 购物车数量徽章，显示购物车中商品数量 -->
            <!-- content: 徽章显示的数量值 -->
            <!-- show-zero: 不显示数量为0的徽章 -->
            <!-- max: 最大显示数量，超过99显示99+ -->
            <van-badge :content="cartCount" :show-zero="false" :max="99" class="cart-badge">
              <!-- 购物车图标，使用本地图片资源 -->
              <img src="./assets/cart-icon.png" alt="购物车" class="cart-icon" />
            </van-badge>
          </div>
          <!-- 购物车文字标签 -->
          <span class="cart-text">购物车</span>
        </div>

        <!-- 操作按钮组容器 -->
        <div class="buttons-wrapper">
          <!-- 加入购物车按钮 -->
          <!-- type: secondary表示次要按钮样式 -->
          <!-- size: medium表示中等尺寸 -->
          <!-- disabled: 根据cartButtonDisabled控制按钮是否禁用 -->
          <WoButton
            type="secondary"
            size="medium"
            :disabled="cartButtonDisabled"
            @click="handleAddToCart"
          >
            加入购物车
          </WoButton>
          <!-- 立即购买/立即提交按钮 -->
          <!-- type: gradient表示渐变主要按钮样式 -->
          <!-- 根据bizCode动态显示按钮文案，zq业务显示"立即提交"，其他显示"立即购买" -->
          <WoButton
            type="gradient"
            size="medium"
            :disabled="cartButtonDisabled"
            @click="handleBuyNow"
          >
             {{ bizCode === 'zq' ? '立即提交' :'立即购买'}}
          </WoButton>
        </div>
      </div>
    </div>
  </WoActionBar>
</template>

<script setup>
import { toRefs } from 'vue'
import WoButton from '@components/WoElementCom/WoButton/WoButton.vue'
import WoActionBar from '@components/WoElementCom/WoActionBar.vue'
import { getBizCode } from '@utils/curEnv.js'

// ===================== 组件属性定义 ======================
// 定义组件接收的props参数
const props = defineProps({
  // 购物车中商品数量，用于显示徽章数字
  cartCount: {
    type: Number,
    default: 0
  },
  // 购物车相关按钮的禁用状态，true时禁用加入购物车和立即购买按钮
  cartButtonDisabled: {
    type: Boolean,
    default: false
  }
})

// 使用toRefs解构props，保持响应性
// 解构后可以直接使用cartCount和cartButtonDisabled，同时保持响应式特性
const { cartCount, cartButtonDisabled } = toRefs(props)

// 定义组件向父组件发射的事件
// go-to-cart: 跳转到购物车页面
// add-to-cart: 添加商品到购物车
// buy-now: 立即购买商品
const emit = defineEmits(['go-to-cart', 'add-to-cart', 'buy-now'])

// ===================== 业务逻辑配置 ======================
// 获取当前业务代码，用于区分不同业务场景
// zq业务显示"立即提交"，其他业务显示"立即购买"
const bizCode = getBizCode()

// ===================== 用户操作处理 ======================
// 处理跳转到购物车的操作
// 当用户点击购物车图标时触发
const handleGoToCart = () => {
  // 向父组件发射go-to-cart事件，由父组件处理具体的跳转逻辑
  emit('go-to-cart')
}

// 处理添加商品到购物车的操作
// 当用户点击"加入购物车"按钮时触发
const handleAddToCart = () => {
  // 向父组件发射add-to-cart事件，由父组件处理具体的添加逻辑
  emit('add-to-cart')
}

// 处理立即购买商品的操作
// 当用户点击"立即购买/立即提交"按钮时触发
const handleBuyNow = () => {
  // 向父组件发射buy-now事件，由父组件处理具体的购买逻辑
  emit('buy-now')
}
</script>

<style scoped lang="less">
.action-bar-container{
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #FFFFFF;
  padding: 10px;
  display: flex;
  flex-direction: column;
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1);
  border-top: 1px solid rgba(0, 0, 0, 0.05);
  box-sizing: border-box;

  .action-content {
    display: flex;
    align-items: center;
    gap: 12px;
  }

  .cart-wrapper {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 0;
    box-sizing: border-box;

    .cart-icon-wrapper {
      position: relative;

      .cart-icon {
        width: 24px;
        height: 24px;
      }

      .cart-badge {
        :deep(.van-badge) {
          background-color: var(--wo-biz-theme-color);
          border-color: var(--wo-biz-theme-color);
        }
      }
    }

    .cart-text {
      font-size: 11px;
      color: #4A5568;
    }
  }

  .buttons-wrapper {
    flex: 1;
    display: flex;
    justify-content: flex-end;
    gap: 8px;
  }
}
</style>
