/**
 * 将金额分转换为元并分离整数和小数部分
 * @param {number} amt - 金额，单位为分
 * @returns {string[]} 返回数组，[整数部分, 小数部分]，例如：123分 -> ['1', '23']
 */
export const splitAmt = amt => {
  const amtN = Number(amt) / 100
  return splitAmtFromYuan(amtN)
}

/**
 * 将金额元分离整数和小数部分
 * @param {number} amt - 金额，单位为元
 * @returns {string[]} 返回数组，[整数部分, 小数部分]，例如：1.23元 -> ['1', '23']
 */
export const splitAmtFromYuan = amt => {
  const amtN = Number(amt)
  const amtS = amtN.toFixed(2)
  return amtS.split('.')
}

/**
 * 将分转换为元
 * @param {number} amt - 金额，单位为分
 * @returns {string} 金额字符串，单位为元，保留两位小数
 */
export const fenToYuan = amt => {
  return (amt / 100).toFixed(2)
}

/**
 * 计算商品的售价和划线价
 * @param {Object} goods - 商品对象
 * @param {Array} goods.skuList - SKU列表
 * @returns {Array<number>} 价格数组，按优先级排序：[展示价格, 划线价格]
 */
export const priceCompute = goods => {
  const sku = goods && goods.skuList && goods.skuList[0]
    ? goods.skuList[0]
    : {}
  return priceComputeFromSku(sku)
}

/**
 * 从SKU计算售价和划线价
 * @param {Object} sku - SKU对象
 * @param {Array} sku.skuPromotionList - SKU促销列表
 * @param {number} sku.price - SKU价格
 * @param {number} sku.crossedPrice - 划线价格
 * @returns {Array<number>} 价格数组，可能的组合：[活动价, SKU价] 或 [SKU价, 划线价] 或 [SKU价]
 */
export const priceComputeFromSku = sku => {
  const priceData = []
  const skuPromotionList = sku.skuPromotionList

  const promotionPrice = (skuPromotionList && skuPromotionList.length > 0) ? skuPromotionList[0].promotionPrice : ''
  if (promotionPrice) priceData.push(promotionPrice)

  const skuPrice = sku.price
  if (skuPrice) priceData.push(skuPrice)

  const underlinePrice = sku.crossedPrice
  if (underlinePrice) priceData.push(underlinePrice)

  return priceData
}
