import { pullAll, cloneDeep, intersection } from 'lodash'
import { log } from 'commonkit'
import { getGoodsDetail, isWhiteUserLimitCheck } from '@api/interface/goods.js'
import { getBizCode } from '@/utils/curEnv'
import { useUserStore } from '@/store/modules/user.js'

/**
 * 根据给定规格返回当前规格行的所有数据
 * @param {Array<Array<string>>} specsList - 规格列表
 * @param {string} spec - 指定规格
 * @returns {Array<string>} 包含指定规格的规格组
 */
const getSpecsGroupFromSpec = (specsList, spec) => {
  return specsList.filter(group => group.indexOf(spec) >= 0)[0]
}

/**
 * 根据给定规格返回可能的SKU列表
 * @param {Array<Object>} skuList - SKU列表
 * @param {Array<string>} specs - 规格数组
 * @returns {Array<Object>} 匹配的SKU列表
 */
const getSkuListFromSpec = (skuList, specs) => {
  let list = skuList
  specs.forEach(spec => {
    list = list.filter(sku => {
      return ('_p0_' + sku.param === spec) || ('_p1_' + sku.param1 === spec) || ('_p2_' + sku.param2 === spec) || ('_p3_' + sku.param3 === spec)
    })
  })
  return list
}

/**
 * 根据参数名称获取对应的前缀
 * @param {string} name - 参数名称
 * @returns {string} 对应的前缀
 */
const paramPrefix = (name) => {
  switch (name) {
    case 'param':
      return '_p0_'
    case 'param1':
      return '_p1_'
    case 'param2':
      return '_p2_'
    case 'param3':
      return '_p3_'
    default:
      return ''
  }
}

/**
 * 商品详情管理类
 * 用于处理商品规格选择、SKU管理等功能
 */
class GoodsDetail {
  #goodsId = ''
  #spu = null
  #curSpecs = []
  #skuId = ''
  #specsList = null
  #userStore = null

  /**
   * 构造函数
   * @param {string} goodsId - 商品ID
   * @param {string} [skuId] - SKU ID，可选
   */
  constructor (goodsId, skuId) {
    this.#goodsId = goodsId
    this.#skuId = skuId || ''
    this.#userStore = useUserStore()
  }

  /**
   * 查询商品SPU信息
   * @returns {Promise<Object>} 商品详情数据
   */
  async querySpu () {
    // 需要先读取地址，再查询商品信息
    await this.#userStore.queryDefaultAddr()

    const info = this.#userStore.curAddressInfo
    const addressInfo = JSON.stringify({
      provinceId: info.provinceId,
      provinceName: info.provinceName,
      cityId: info.cityId,
      cityName: info.cityName,
      countyId: info.countyId,
      countyName: info.countyName,
      townId: info.townId,
      townName: info.townName
    })
    const [json, goodsDetail] = await getGoodsDetail({
      bizCode: getBizCode('GOODS'),
      goodsId: this.#goodsId,
      addressInfo: addressInfo
    })
    if (!goodsDetail) {
      return cloneDeep(json)
    }
    this.#spu = goodsDetail
    // 处理默认 sku，必须是存在可购买的商品
    let flag = false // 当前是否索引到有效 sku
    let skuCache = {}
    if (this.#skuId) {
      // 当前强行指定 skuId
      const sku = this.#spu.skuList.filter(sku => this.#skuId === sku.skuId)[0]
      if (sku) skuCache = sku
    }

    // 没有默认skuId，或者skuId是错误的，导致 skuCache 不存在，此时要走默认流程
    if (!skuCache.skuId) {
      // skuId 不存在，使用默认方案
      this.#spu.skuList.some(sku => {
        const { status } = this.checkSkuAvailable(sku)
        if (status === 0) {
          this.#skuId = sku.skuId
          skuCache = sku
          flag = true
          return true
        }
        return false
      })
      if (!flag) {
        // 当前商品下，所有规格均不可使用，默认使用第一个sku
        this.#skuId = this.#spu.skuList[0].skuId
        skuCache = this.#spu.skuList[0]
      }
    }

    const { param, param1, param2, param3 } = skuCache
    this.#curSpecs = ['_p0_' + param, '_p1_' + param1, '_p2_' + param2, '_p3_' + param3].filter(p => !!p)
    return cloneDeep(json)
  }

  /**
   * 查询当前对应的SKU
   * @returns {Object} 当前SKU对象的深拷贝
   */
  querySku () {
    const sku = this.#spu.skuList.filter(sku => sku.skuId === this.#skuId)[0]
    return cloneDeep(sku)
  }

  /**
   * 查询当前商品拥有的SKU数量
   * @returns {number} SKU数量
   */
  querySkuCount () {
    return this.#spu.skuList.length
  }

  /**
   * 查询规格列表
   * @returns {Array<Array<string>>} 四维规格数组
   */
  querySpecsList () {
    if (this.#specsList) return this.#specsList

    const specs = [[], [], [], []]

    this.#spu.skuList.forEach(sku => {
      const { param, param1, param2, param3 } = sku
      if (param) {
        const has0 = specs[0].indexOf('_p0_' + param) >= 0
        if (!has0) specs[0].push('_p0_' + param)
      }
      if (param1) {
        const has1 = specs[1].indexOf('_p1_' + param1) >= 0
        if (!has1) specs[1].push('_p1_' + param1)
      }
      if (param2) {
        const has2 = specs[2].indexOf('_p2_' + param2) >= 0
        if (!has2) specs[2].push('_p2_' + param2)
      }
      if (param3) {
        const has3 = specs[3].indexOf('_p3_' + param3) >= 0
        if (!has3) specs[3].push('_p3_' + param3)
      }
    })
    this.#specsList = cloneDeep(specs)
    return this.#specsList
  }

  /**
   * 查询当前选中的规格
   * @returns {Array<string>} 当前选中的规格数组，按标准顺序排序
   */
  queryCurSpecs () {
    const specsList = this.querySpecsList()
    const newList = []
    // 检查每个规格行，是否有当前选中规格，有则 push 到列表中
    // 这样可以保证 this.#curSpecs 无论什么顺序，最终输出顺序都是按照商品标准规格顺序排序
    specsList.forEach(item => {
      this.#curSpecs.forEach(curSpec => {
        if (item.indexOf(curSpec) >= 0) newList.push(curSpec)
      })
    })
    return newList
  }

  /**
   * 通过规格设置SKU ID
   * @param {string} spec - 要设置的规格
   * @returns {boolean} 是否成功修改规格：true-成功修改，false-没有修改
   */
  setSpecs (spec) {
    // 当前规格，不可点击
    // if (this.queryCurSpecs().indexOf(spec) >= 0) return false
    // disabled 规格，不可点击
    if (this.queryDisabledSpecs().indexOf(spec) >= 0) return false

    // 判断当前是否选中该规格
    let curSpecs = this.queryCurSpecs()
    const isSelected = curSpecs.filter(s => s === spec)[0]
    if (isSelected) {
      // 选中该规格，此时需要反选
      curSpecs = curSpecs.filter(s => s !== spec)
    } else {
      // 没有选中该规则，此时选中。需要移除同组规格数据，再进行添加
      // 筛选出当前行所有规格
      const curGroup = getSpecsGroupFromSpec(this.querySpecsList(), spec)
      // 筛选出除去当前行的规格
      pullAll(curSpecs, curGroup)
      // 将新规格推入
      curSpecs.push(spec)
    }
    this.#curSpecs = curSpecs

    // 查找当前规格，对应的 sku
    const list = getSkuListFromSpec(this.#spu.skuList, curSpecs)
    // list 可能是多个，因为用户可能没有把规格选择完全
    if (list.length === 1) {
      this.#skuId = list[0].skuId
    } else {
      // 多个商品 sku 情况，此时不改变当前展示 skuId
      return false
    }
    return true
  }

  /**
   * 查询当前不可选的规格
   * @returns {Array<string>} 不可选规格数组
   */
  queryDisabledSpecs () {
    const specsList = this.querySpecsList()
    const curSpecs = this.queryCurSpecs()
    let disabledSpecs = [...specsList[0], ...specsList[1], ...specsList[2], ...specsList[3]]

    const runner = (specsListGroup, paramName) => {
      console.group(paramName)
      if (specsListGroup.length > 0) {
        // 从当前规格中，排除一组规格
        const filteredSpecs = pullAll(cloneDeep(curSpecs), specsListGroup)
        log('[goodsDetail] queryDisabledSpecs', '排除：', specsListGroup, '后，当前剩余规格：', filteredSpecs)

        const skuList = this.#spu.skuList.filter(sku => {
          // 检查sku组合是否有效
          const ret = this.checkSkuAvailable(sku)
          return ret.status === 0
        }).filter(sku => {
          // 通过当前选中的规格，筛选出可能的sku组合
          // 如果当前规格为空，则认为所有规格均有效
          if (filteredSpecs.length === 0) return true
          // 拿到当前 sku 的所有规格
          const { param, param1, param2, param3 } = sku
          // 拿到当前 sku 的所有规格，并过滤为数组
          const specs = ['_p0_' + param, '_p1_' + param1, '_p2_' + param2, '_p3_' + param3].filter(p => !!p)
          // 计算规格交集
          const intersectionedSpecs = intersection(specs, filteredSpecs)
          // 如果规格交集长度和当前过滤后规格长度相同，此时就是当前有效规格
          return intersectionedSpecs.length === filteredSpecs.length
        })
        log('[goodsDetail] queryDisabledSpecs', '当前所有有效的skuList', skuList)

        // 当前有效的规格
        const validSpecs = skuList.map(sku => paramPrefix(paramName) + sku[paramName])
        log('[goodsDetail] queryDisabledSpecs', '当前有效的规格', validSpecs)

        // 当前规格有效，从 disabledSpecs 移除
        disabledSpecs = disabledSpecs.filter(spec => {
          return validSpecs.indexOf(spec) === -1
        })
        log('[goodsDetail] queryDisabledSpecs', '当前流程 disabledSpecs', disabledSpecs)
      }
      console.groupEnd(paramName)
    }

    // 检查第一组规格（剩余三组保持不动）
    runner(specsList[0], 'param')

    // 检查第二组规格（剩余三组保持不动）
    runner(specsList[1], 'param1')

    // 检查第三组规格（剩余三组保持不动）
    runner(specsList[2], 'param2')

    // 检查第四组规格（剩余三组保持不动）
    runner(specsList[3], 'param3')

    log('[goodsDetail] queryDisabledSpecs', '当前不可选规格', disabledSpecs)
    return disabledSpecs
  }

  /**
   * 检查当前SKU是否可用
   * @param {Object} [sku] - SKU对象，可选，默认使用当前SKU
   * @returns {{status: number}|{err: string, status: number}} 检查结果，status: 0-可用，1-无库存，2-已下架
   */
  checkSkuAvailable (sku) {
    const skuu = sku || this.querySku()
    // 商品状态 state： 0-不能购买，1-上架，2-下架，null-状态异常
    if (skuu.state !== '1') return { status: 2, err: '该商品已下架，请选购其他商品~' }
    // 检查库存
    if (Number(skuu.stock) <= 0) return { status: 1, err: '所选地区暂时无货，非常抱歉！' }
    return { status: 0 }
  }

  /**
   * 检查当前商品SPU是否可用
   * @returns {Promise<boolean>} 是否可用
   */
  async checkSpuAvailable () {
    // 检查限售区域
    if (this.#spu.isCheckWhiteUser === '1') {
      if (this.#userStore.isLogin) {
        const [err, json] = await isWhiteUserLimitCheck(this.#goodsId)
        if (!err && !json) return true
      }
    }
    return false
  }

  /**
   * 检查当前规格是否全部选择完整
   * @returns {boolean} 规格是否选择完整
   */
  isSpecsComplete () {
    const specsList = this.querySpecsList()
    const curSpecs = this.queryCurSpecs()
    // 有效的规格栏目数量
    const count = specsList.reduce((a, b) => {
      if (b.length > 0) a++
      return a
    }, 0)
    return count === curSpecs.length
  }
}

export default GoodsDetail

/**
 * 移除规格参数的前缀
 * @param {string|Array<string>} param - 规格参数，可以是字符串或字符串数组
 * @returns {string|Array<string>} 移除前缀后的规格参数
 */
export const removeSpecPrefix = (param) => {
  if (typeof param === 'string') {
    // 特殊处理默认规格
    if (param === '默认规格') {
      return param
    }

    if (param.indexOf(',') >= 0) {
      return param.split(',').map(item => {
        // 检查是否有前缀
        if (item.startsWith('_p0_') || item.startsWith('_p1_') || item.startsWith('_p2_') || item.startsWith('_p3_')) {
          return item.substring(4)
        }
        return item
      }).join(',')
    } else {
      // 检查是否有前缀
      if (param.startsWith('_p0_') || param.startsWith('_p1_') || param.startsWith('_p2_') || param.startsWith('_p3_')) {
        return param.substring(4)
      }
      return param
    }
  } else if (Array.isArray(param)) {
    return param.map(item => {
      if (typeof item === 'string') {
        // 特殊处理默认规格
        if (item === '默认规格') {
          return item
        }
        // 检查是否有前缀
        if (item.startsWith('_p0_') || item.startsWith('_p1_') || item.startsWith('_p2_') || item.startsWith('_p3_')) {
          return item.substring(4)
        }
      }
      return item
    })
  }
  return param
}
