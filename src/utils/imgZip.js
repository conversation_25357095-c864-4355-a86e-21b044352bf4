/**
 * 图片压缩状态枚举
 * @readonly
 * @enum {number}
 */
const STATUS = {
  NOT_REACH_COMPRESS_THRESHOLD: 0,
  COMPRESS_SUCCESS: 1,
  NOT_IMAGE_FILE: 2,
  NOT_SUPPORT_COMPRESS: 3,
  COMPRESS_ERROR: 4
}

/**
 * 状态码对应的消息映射
 * @type {Object<number, string>}
 */
const STATUS_MAPPING_MSG = {
  0: '文件大小未达到压缩阈值',
  1: '图片压缩成功',
  2: '非图片文件不进行压缩',
  3: '当前浏览器不支持图片压缩',
  4: '图片压缩过程出错'
}

/**
 * 压缩图片文件
 * @param {Object} props - 压缩配置参数
 * @param {File} props.file - 待压缩的文件对象
 * @param {number} [props.threshold=2] - 压缩阈值，单位MB
 * @param {number} [props.quality=0.5] - 压缩质量，范围0-1
 * @param {boolean} [props.shrink=false] - 是否需要缩放图片
 * @param {number} [props.shrinkMaxWidth=1920] - 最大宽度，单位px
 * @param {number} [props.shrinkMaxHeight=1080] - 最大高度，单位px
 * @param {boolean} [props.compressUntilSizeBelowThreshold=false] - 是否压缩直到文件大小低于阈值
 * @returns {Promise<{file: File, isCompressed: boolean, status: number, msg: string}>} 压缩结果
 */
const compressImageFile = (props) => {
  const {
    file,
    threshold = 2,
    quality = 0.5,
    shrink = false,
    shrinkMaxWidth = 1920,
    shrinkMaxHeight = 1080,
    compressUntilSizeBelowThreshold = false
  } = props

  const isAccept = [
    'image/png',
    'image/jpeg',
    'image/jpg',
    'image/gif',
    'image/bmp',
    'image/tiff',
    'image/webp',
    'image/svg+xml',
    'image/heic'
  ]

  const isFileTypeAccepted = isAccept.indexOf(file.type) !== -1
  if (!isFileTypeAccepted) {
    return Promise.resolve({
      file: file,
      isCompressed: false,
      status: STATUS.NOT_IMAGE_FILE,
      msg: STATUS_MAPPING_MSG[STATUS.NOT_IMAGE_FILE]
    })
  }

  const fileSize = file.size / 1024 / 1024

  if (fileSize < threshold) {
    return Promise.resolve({
      file: file,
      isCompressed: false,
      status: STATUS.NOT_REACH_COMPRESS_THRESHOLD,
      msg: STATUS_MAPPING_MSG[STATUS.NOT_REACH_COMPRESS_THRESHOLD]
    })
  }

  if (typeof FileReader === 'undefined') {
    return Promise.resolve({
      file: file,
      isCompressed: false,
      status: STATUS.NOT_SUPPORT_COMPRESS,
      msg: STATUS_MAPPING_MSG[STATUS.NOT_SUPPORT_COMPRESS]
    })
  }

  const compressErrorContent = {
    file: file,
    isCompressed: false,
    status: STATUS.COMPRESS_ERROR,
    msg: STATUS_MAPPING_MSG[STATUS.COMPRESS_ERROR]
  }

  return new Promise((resolve) => {
    try {
      const reader = new FileReader()
      reader.readAsDataURL(file)
      reader.onload = (event) => {
        const img = new Image()
        img.src = event.target?.result || reader?.result || ''
        img.onload = () => {
          const canvas = document.createElement('canvas')
          const ctx = canvas.getContext('2d')
          if (!ctx) {
            return resolve(compressErrorContent)
          }
          const originImageWidth = img.width
          const originImageHeight = img.height
          const ratio = (shrinkMaxWidth || 1920) / (shrinkMaxHeight || 1080)
          let targetWidth = originImageWidth
          let targetHeight = originImageHeight
          if (
            shrink &&
            (originImageWidth > shrinkMaxWidth || originImageHeight > shrinkMaxHeight)
          ) {
            if (originImageWidth / originImageHeight > ratio) {
              targetWidth = shrinkMaxWidth
              targetHeight = Math.round(shrinkMaxWidth * (originImageHeight / originImageWidth))
            } else {
              targetHeight = shrinkMaxHeight
              targetWidth = Math.round(shrinkMaxHeight * (originImageWidth / originImageHeight))
            }
          }
          canvas.width = targetWidth
          canvas.height = targetHeight
          ctx.clearRect(0, 0, targetWidth, targetHeight)
          ctx.drawImage(img, 0, 0, targetWidth, targetHeight)
          canvas.toBlob(
            (blob) => {
              if (blob) {
                const newFile = new File([blob], file.name, { type: file.type })
                if (
                  compressUntilSizeBelowThreshold &&
                  newFile.size / 1024 / 1024 >= threshold &&
                  quality - 0.2 >= 0.2
                ) {
                  compressImageFile({
                    file: newFile,
                    threshold: threshold,
                    quality: quality - 0.2,
                    shrink: shrink,
                    shrinkMaxWidth: shrinkMaxWidth,
                    shrinkMaxHeight: shrinkMaxHeight,
                    compressUntilSizeBelowThreshold: compressUntilSizeBelowThreshold
                  }).then((res) => {
                    resolve(res)
                  })
                } else {
                  return resolve({
                    file: newFile,
                    isCompressed: true,
                    status: STATUS.COMPRESS_SUCCESS,
                    msg: STATUS_MAPPING_MSG[STATUS.COMPRESS_SUCCESS]
                  })
                }
              } else {
                return resolve(compressErrorContent)
              }
            },
            file.type,
            quality
          )
        }
        img.onerror = () => {
          return resolve(compressErrorContent)
        }
      }
      reader.onerror = () => {
        return resolve(compressErrorContent)
      }
    } catch (error) {
      return resolve(compressErrorContent)
    }
  })
}

export default compressImageFile
