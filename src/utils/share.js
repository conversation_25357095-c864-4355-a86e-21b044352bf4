import { copy } from '@/utils/clipboard'
import { getFuLiHuiID } from '@api/interface/flh.js'
import { getBizCode } from '@/utils/curEnv'
import { curDeveloperId } from '@utils/storage.js'
import { showToast } from 'vant'

/**
 * 获取默认分享URL
 * @returns {Promise<string>} 分享URL
 */
export const getDefaultShareUrl = async () => {
  const urlSearch = window.location.search
  const urlQueryStr = urlSearch ? urlSearch.split('?')[1] : ''

  let url = window.location.origin + window.location.pathname + urlSearch
  if (!(urlQueryStr.indexOf('distri_biz_code') >= 0)) {
    url += url.indexOf('?') >= 0 ? '&' : '?'
    const bizCode = getBizCode()
    if (bizCode === 'fulihui') {
      let developerId = curDeveloperId.get()
      if (!developerId) {
        const [err, json] = await getFuLiHuiID({ bizCode })
        if (!err) {
          developerId = json || ''
        }
      }
      curDeveloperId.set(developerId)

      const shareUrl = url + 'distri_biz_code=' + bizCode + '&developerId=' + developerId
      return shareUrl
    }
    const shareUrl = url + 'distri_biz_code=' + bizCode
    return shareUrl
  }
  return url
}

/**
 * 分享数据配置对象
 * @type {Object}
 * @property {string} link - 分享链接
 * @property {string} title - 分享标题
 * @property {string} describe - 分享描述
 * @property {string} picUrl - 分享图片URL
 * @property {Function} next - 分享后续处理函数
 * @property {Function} callback - 分享回调函数
 */
export const shareData = {
  link: '',
  title: '',
  describe: '',
  picUrl: '',
  /**
   * 分享后续处理函数
   * @param {Object} options - 分享选项
   * @param {string} options.type - 分享类型，如 'h5', 'weixin'
   * @param {Object} userData - 用户数据
   */
  next: (options, userData) => {
    if (options.type === 'h5') {
      copy(shareData.link)
    } else if (options.type === 'weixin') {
      showToast('请点击屏幕右上方的按钮进行分享')
    }
  },
  /**
   * 分享回调函数
   */
  callback: () => {

  }
}
