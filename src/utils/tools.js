/**
 * 检查Android版本是否小于指定版本
 * @param {number} num - 要比较的版本号
 * @returns {boolean} 如果是Android且版本小于指定版本返回true，否则返回undefined
 */
export const heckPlatform = num => {
  const ua = navigator.userAgent.toLowerCase()
  if (/android/i.test(navigator.userAgent)) {
    const test = /android\s([\w.]+)/
    const match = test.exec(ua)
    const version = match[1].split('.')[0]
    if (version < num) {
      return true
    }
  }
}
/**
 * 批量请求处理器 - 性能优化版本
 * 支持并发控制、错误处理、超时控制和进度回调
 */
export class BatchRequest {
  /**
   * 构造函数
   * @param {Object} [options={}] - 配置选项
   * @param {number} [options.concurrency=10] - 并发数限制
   * @param {number} [options.timeout=30000] - 超时时间(ms)
   * @param {number} [options.retryCount=0] - 重试次数
   * @param {boolean} [options.failFast=false] - 是否快速失败
   */
  constructor(options = {}) {
    this.promises = []
    this.results = []
    this.errors = []
    this.onCompleteCallback = null
    this.onProgressCallback = null
    this.onErrorCallback = null

    // 配置选项
    this.options = {
      concurrency: options.concurrency || 10, // 并发数限制
      timeout: options.timeout || 30000, // 超时时间(ms)
      retryCount: options.retryCount || 0, // 重试次数
      failFast: options.failFast || false, // 是否快速失败
      ...options
    }

    this.completed = 0
    this.total = 0
    this.isExecuting = false
  }

  /**
   * 添加请求到批次
   * @param {Promise|Function} promiseOrFn - Promise或返回Promise的函数
   * @param {Object} metadata - 元数据
   */
  push(promiseOrFn, metadata = {}) {
    if (this.isExecuting) {
      throw new Error('Cannot add requests while batch is executing')
    }

    this.promises.push({
      executor: typeof promiseOrFn === 'function' ? promiseOrFn : () => promiseOrFn,
      metadata,
      retries: 0
    })
    this.total = this.promises.length
    return this
  }

  /**
   * 设置完成回调并执行所有请求
   * @param {Function} callback - 完成回调函数
   */
  set onComplete(callback) {
    this.onCompleteCallback = callback
    this.executeAll()
  }

  /**
   * 设置进度回调
   * @param {Function} callback - 进度回调函数
   * @returns {BatchRequest} 返回当前实例以支持链式调用
   */
  onProgress(callback) {
    this.onProgressCallback = callback
    return this
  }

  /**
   * 设置错误回调
   * @param {Function} callback - 错误回调函数
   * @returns {BatchRequest} 返回当前实例以支持链式调用
   */
  onError(callback) {
    this.onErrorCallback = callback
    return this
  }

  /**
   * 创建带超时的Promise
   * @param {Promise} promise - 原始Promise
   * @param {number} timeout - 超时时间(ms)
   * @returns {Promise} 带超时控制的Promise
   */
  _createTimeoutPromise(promise, timeout) {
    return Promise.race([
      promise,
      new Promise((_, reject) =>
        setTimeout(() => reject(new Error('Request timeout')), timeout)
      )
    ])
  }

  /**
   * 执行单个请求（带重试机制）
   * @param {Object} requestItem - 请求项
   * @returns {Promise<Object>} 执行结果
   */
  async _executeWithRetry(requestItem) {
    const { executor, metadata, retries } = requestItem

    try {
      const promise = executor()
      const result = await this._createTimeoutPromise(promise, this.options.timeout)
      return { success: true, result, metadata }
    } catch (error) {
      if (retries < this.options.retryCount) {
        requestItem.retries++
        await new Promise(resolve => setTimeout(resolve, Math.pow(2, retries) * 1000)) // 指数退避
        return this._executeWithRetry(requestItem)
      }
      return { success: false, error, metadata }
    }
  }

  /**
   * 并发控制执行所有请求
   * @returns {Promise<Array>} 执行结果数组
   */
  async _executeConcurrently() {
    const { concurrency } = this.options
    const executing = []
    const results = []

    for (let i = 0; i < this.promises.length; i++) {
      const requestPromise = this._executeWithRetry(this.promises[i])
        .then(result => {
          this.completed++

          if (result.success) {
            results[i] = result.result
          } else {
            this.errors.push({ index: i, error: result.error, metadata: result.metadata })
            if (this.onErrorCallback) {
              this.onErrorCallback(result.error, result.metadata, i)
            }
            if (this.options.failFast) {
              throw result.error
            }
          }

          // 进度回调
          if (this.onProgressCallback) {
            this.onProgressCallback({
              completed: this.completed,
              total: this.total,
              progress: this.completed / this.total,
              errors: this.errors.length
            })
          }

          return result
        })

      executing.push(requestPromise)

      // 控制并发数
      if (executing.length >= concurrency) {
        await Promise.race(executing)
        executing.splice(executing.findIndex(p => p.isFulfilled), 1)
      }
    }

    // 等待所有剩余请求完成
    await Promise.allSettled(executing)
    return results
  }

  /**
   * 执行所有请求
   * @returns {Promise<Object>} 包含结果和错误的对象
   */
  async executeAll() {
    if (this.isExecuting) {
      throw new Error('Batch is already executing')
    }

    if (this.promises.length === 0) {
      if (this.onCompleteCallback) {
        this.onCompleteCallback([], [])
      }
      return { results: [], errors: [] }
    }

    this.isExecuting = true
    this.completed = 0
    this.results = []
    this.errors = []

    try {
      this.results = await this._executeConcurrently()

      if (this.onCompleteCallback) {
        this.onCompleteCallback(this.results, this.errors)
      }

      return {
        results: this.results,
        errors: this.errors,
        success: this.errors.length === 0
      }
    } catch (error) {
      console.error('批量请求执行失败:', error)

      if (this.onCompleteCallback) {
        this.onCompleteCallback(this.results, this.errors)
      }

      throw error
    } finally {
      this.isExecuting = false
    }
  }

  /**
   * 清空批次中的所有请求
   * @returns {BatchRequest} 返回当前实例以支持链式调用
   */
  clear() {
    if (this.isExecuting) {
      throw new Error('Cannot clear while batch is executing')
    }
    this.promises = []
    this.results = []
    this.errors = []
    this.completed = 0
    this.total = 0
    return this
  }

  /**
   * 获取当前执行状态
   * @returns {Object} 状态信息对象
   */
  getStatus() {
    return {
      total: this.total,
      completed: this.completed,
      pending: this.total - this.completed,
      errors: this.errors.length,
      isExecuting: this.isExecuting,
      progress: this.total > 0 ? this.completed / this.total : 0
    }
  }
}

/**
 * 创建批量请求实例的工厂函数
 * @param {Object} options - 配置选项
 * @returns {BatchRequest}
 */
export const createBatchRequest = (options = {}) => {
  return new BatchRequest(options)
}
