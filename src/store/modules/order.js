/**
 * @fileoverview 订单状态管理
 * @description 管理订单数量统计、缓存机制和加载状态
 * <AUTHOR> Coding
 * @since 2024
 */

import { defineStore } from 'pinia'
import { getOrderCount } from '@/api/interface/order'
import { debounce } from 'lodash-es'

/**
 * 订单状态管理
 * @description 用于管理订单数量统计，包含缓存机制和防抖功能
 * @returns {Object} Pinia store实例
 */
export const useOrderStore = defineStore('order', {
  /**
   * 状态定义
   * @returns {Object} 状态对象
   */
  state: () => ({
    /**
     * 未支付订单数量
     * @type {number}
     * @default 0
     */
    unpaidCount: 0,
    
    /**
     * 待发货订单数量
     * @type {number}
     * @default 0
     */
    processedCount: 0,
    
    /**
     * 待收货订单数量
     * @type {number}
     * @default 0
     */
    dispatchedCount: 0,
    
    /**
     * 加载状态
     * @type {boolean}
     * @default false
     */
    loading: false,
    
    /**
     * 上次获取时间戳
     * @type {number}
     * @default 0
     * @description 用于实现缓存机制，避免频繁请求
     */
    lastFetchTime: 0
  }),

  getters: {
    /**
     * 根据类型获取订单数量
     * @description 根据订单类型返回对应的订单数量
     * @param {Object} state - 状态对象
     * @returns {Function} 返回一个函数，接收订单类型参数
     * 
     * @example
     * // 获取未支付订单数量
     * const unpaidCount = orderStore.getCountByType('0')
     * 
     * // 获取待发货订单数量
     * const processedCount = orderStore.getCountByType('3')
     */
    getCountByType: (state) => (type) => {
      switch (type) {
        case '0': return state.unpaidCount    // 未支付
        case '3': return state.processedCount // 待发货
        case '5': return state.dispatchedCount // 待收货
        default: return 0
      }
    },
    
    /**
     * 是否有未处理订单
     * @description 检查是否存在任何需要处理的订单（未支付、待发货、待收货）
     * @param {Object} state - 状态对象
     * @returns {boolean} 如果有未处理订单返回true，否则返回false
     * 
     * @example
     * // 检查是否有未处理订单
     * if (orderStore.hasUnprocessedOrders) {
     *   console.log('有订单需要处理')
     * }
     */
    hasUnprocessedOrders: (state) => {
      return state.unpaidCount > 0 || state.processedCount > 0 || state.dispatchedCount > 0
    }
  },

  actions: {
    /**
     * 设置订单数量
     * @description 批量更新各类型订单的数量
     * @param {Object} counts - 订单数量对象
     * @param {number} [counts.unpayCount=0] - 未支付订单数量
     * @param {number} [counts.payedCount=0] - 待发货订单数量
     * @param {number} [counts.deliverCount=0] - 待收货订单数量
     * 
     * @example
     * // 设置订单数量
     * orderStore.setOrderCounts({
     *   unpayCount: 5,
     *   payedCount: 3,
     *   deliverCount: 2
     * })
     */
    setOrderCounts({ unpayCount = 0, payedCount = 0, deliverCount = 0 }) {
      this.unpaidCount = unpayCount
      this.processedCount = payedCount
      this.dispatchedCount = deliverCount
    },

    /**
     * 防抖获取订单数量
     * @description 使用防抖机制获取订单数量，避免频繁调用
     * @type {Function}
     * @debounce 300ms
     * 
     * @example
     * // 防抖获取订单数量
     * orderStore.fetchOrderCountDebounced()
     */
    fetchOrderCountDebounced: debounce(async function() {
      await this.fetchOrderCount()
    }, 300),

    /**
     * 获取订单数量
     * @description 从服务器获取订单数量，支持缓存机制（5分钟）和强制刷新
     * @param {boolean} [force=false] - 是否强制刷新，忽略缓存
     * @returns {Promise<void>} 无返回值的Promise
     * 
     * @example
     * // 正常获取（使用缓存）
     * await orderStore.fetchOrderCount()
     * 
     * // 强制刷新
     * await orderStore.fetchOrderCount(true)
     */
    async fetchOrderCount(force = false) {
      // 缓存机制：5分钟内不重复请求
      const now = Date.now()
      const cacheTime = 5 * 60 * 1000 // 5分钟
      
      if (!force && this.lastFetchTime && (now - this.lastFetchTime) < cacheTime) {
        return
      }

      // 防止重复请求
      if (this.loading) return
      
      this.loading = true
      try {
        const [err, data] = await getOrderCount()
        if (!err && data) {
          this.setOrderCounts(data)
          this.lastFetchTime = now
        }
      } catch (error) {
        console.error('获取订单数量失败:', error)
      } finally {
        this.loading = false
      }
    },

    /**
     * 清除缓存
     * @description 清除获取时间缓存，下次调用fetchOrderCount时将重新请求
     * 
     * @example
     * // 清除缓存
     * orderStore.clearCache()
     */
    clearCache() {
      this.lastFetchTime = 0
    },

    /**
     * 重置状态
     * @description 重置所有状态到初始值，包括订单数量、加载状态和缓存时间
     * 
     * @example
     * // 重置所有状态
     * orderStore.reset()
     */
    reset() {
      this.unpaidCount = 0
      this.processedCount = 0
      this.dispatchedCount = 0
      this.loading = false
      this.lastFetchTime = 0
    }
  }
})