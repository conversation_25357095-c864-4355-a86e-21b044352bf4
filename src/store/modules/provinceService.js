import { defineStore } from 'pinia'
import { storage } from 'commonkit'
import { zqAllSupplierList } from '@/api/interface/zq'

// 创建sessionStorage缓存（保留其他缓存，只移除serviceList缓存）
const selectedAreaIdCache = storage('PS_CCMS_SELECTED_AREA_ID', false)
const selectedIsvIdCache = storage('PS_CCMS_SELECTED_ISV_ID', false)

export const useProvinceServiceStore = defineStore('provinceService', {
  state: () => ({
    // 从缓存中恢复数据，如果没有缓存则使用默认值
    selectedAreaId: selectedAreaIdCache.get() || '',
    selectedIsvId: selectedIsvIdCache.get() || '',
    provinceList: [
      { areaId: '1', areaName: '北京' },
      { areaId: '2', areaName: '上海' },
      { areaId: '3', areaName: '天津' },
      { areaId: '4', areaName: '重庆' },
      { areaId: '5', areaName: '河北' },
      { areaId: '6', areaName: '山西' },
      { areaId: '7', areaName: '河南' },
      { areaId: '8', areaName: '辽宁' },
      { areaId: '9', areaName: '吉林' },
      { areaId: '10', areaName: '黑龙江' },
      { areaId: '11', areaName: '内蒙古' },
      { areaId: '12', areaName: '江苏' },
      { areaId: '13', areaName: '山东' },
      { areaId: '14', areaName: '安徽' },
      { areaId: '15', areaName: '浙江' },
      { areaId: '16', areaName: '福建' },
      { areaId: '17', areaName: '湖北' },
      { areaId: '18', areaName: '湖南' },
      { areaId: '19', areaName: '广东' },
      { areaId: '20', areaName: '广西' },
      { areaId: '21', areaName: '江西' },
      { areaId: '22', areaName: '四川' },
      { areaId: '23', areaName: '海南' },
      { areaId: '24', areaName: '贵州' },
      { areaId: '25', areaName: '云南' },
      { areaId: '26', areaName: '西藏' },
      { areaId: '27', areaName: '陕西' },
      { areaId: '28', areaName: '甘肃' },
      { areaId: '29', areaName: '青海' },
      { areaId: '30', areaName: '宁夏' },
      { areaId: '31', areaName: '新疆' }
    ],
    // 移除缓存，每次都从接口获取
    serviceList: [],
    loading: false
  }),
  getters: {
    selectedProvinceName: (state) => {
      if (!state.selectedAreaId) return '全部'
      const province = state.provinceList.find(p => p.areaId === state.selectedAreaId)
      return province ? province.areaName : '全部'
    },
    selectedServiceName: (state) => {
      if (!state.selectedIsvId) return '全部'
      const service = state.serviceList.find(s => s.isvId === state.selectedIsvId)
      return service ? service.isvName : '全部'
    }
  },
  actions: {
    selectProvince (areaId) {
      this.selectedAreaId = areaId
      // 同步更新缓存
      selectedAreaIdCache.set(areaId)
    },
    selectService (isvId) {
      this.selectedIsvId = isvId
      // 同步更新缓存
      selectedIsvIdCache.set(isvId)
    },
    resetSelection () {
      this.selectedAreaId = ''
      this.selectedIsvId = ''
      // 同步清空缓存
      selectedAreaIdCache.set('')
      selectedIsvIdCache.set('')
    },
    // 获取服务商列表 - 每次都从接口获取
    async fetchServiceList (params = {}) {
      this.loading = true
      try {
        const [error, data] = await zqAllSupplierList(params)
        if (!error && data) {
          this.serviceList = data
          return data
        } else {
          console.error('获取服务商列表失败:', error)
          return []
        }
      } catch (error) {
        console.error('获取服务商列表异常:', error)
        return []
      } finally {
        this.loading = false
      }
    }
  }
})
