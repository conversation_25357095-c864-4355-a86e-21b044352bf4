/**
 * 购物车模块
 *
 * 查询购物车
 * const err = await useNewCartStore().query()
 * 有效商品列表 useNewCartStore().validList 数据结构：[{ groupName, goodsList, selected }, ... ]
 * 失效商品列表 useNewCartStore().invalidList 数据结构：[{ groupName, goodsList }]
 * 所有有效商品种类数 useNewCartStore().countByGoods
 * 所有有效商品个数 useNewCartStore().countAll
 * 选中有效商品种类数 useNewCartStore().selectCountByGoods
 * 选中有效所有商品个数 useNewCartStore().selectCountAll
 * 选中有效商品总金额 useNewCartStore().selectTotalPrice
 * 当前所有有效商品是否全选 useNewCartStore().isSelectAll
 *
 * 增加商品（goodsNum 为 Number 类型），本接口完成后自动刷新购物车数据
 * const err = await useNewCartStore().add({ goodsId, skuId, goodsNum, addressInfo })
 *
 * 更新商品数量（goodsNum 为 Number 类型），本接口已判断库存数量
 * const err = await useNewCartStore().updateGoodsNum({ goodsId, skuId, goodsNum })
 *
 * 更新商品选中状态（多个）（select 为 Boolean 类型）
 * const err = await useNewCartStore().updateGoodsSelectMuti([{ goodsId, skuId, select }, ... ])
 *
 * 移除商品
 * const err = useNewCartStore().removeMuti([{ goodsId, skuId }, ... ])
 *
 * 移除失效商品
 * const err = useNewCartStore().removeInvalidGoods()
 *
 * 【封装方法】移除有效区选中的商品
 * const err = useNewCartStore().removeSelectValidGoods()
 *
 * 【封装方法】有效商品全选/反选
 * const err = useNewCartStore().checkedAllValid()
 *
 * 【封装方法】指定分组下商品全选/反选
 * const err = useNewCartStore().checkedAllByGroupName(groupName)
 */
import { defineStore } from 'pinia'
import { cart } from '@/api'
import { getBizCode } from '@/utils/curEnv'
import { useUserStore } from './user'
import { woReport } from 'commonkit'
import dayjs from 'dayjs'
import { queryZqInfo } from '@/utils/zqInfo'
import { cloneDeep } from 'lodash-es'

/**
 * 数据保护装饰器 - 通过深拷贝隔离原始数据
 * @param data
 */
const protectData = (data) => {
  // 深拷贝确保数据隔离
  return cloneDeep(data)
}

/**
 * 数据完整性验证
 * @param group
 */
const validateGroupData = (group) => {
  if (!group || typeof group !== 'object') {
    console.warn('无效的分组数据:', group)
    return false
  }
  if (!Array.isArray(group.goodsList)) {
    console.warn('分组商品列表格式错误:', group)
    return false
  }
  return true
}

/**
 * 创建商品索引Map，提高查找性能
 * @param state
 * @returns {Map}
 */
const createGoodsMap = (state) => {
  const goodsMap = new Map()

  // 索引有效商品
  state.validList.forEach(group => {
    group.goodsList?.forEach(goods => {
      const key = `${goods.cartGoodsId}_${goods.cartSkuId}`
      goodsMap.set(key, { goods, type: 'valid', group })
    })
  })

  // 索引无效商品
  state.invalidList.forEach(group => {
    group.goodsList?.forEach(goods => {
      const key = `${goods.cartGoodsId}_${goods.cartSkuId}`
      goodsMap.set(key, { goods, type: 'invalid', group })
    })
  })

  return goodsMap
}

/**
 * 通过 goodsId skuId 获取商品信息 - 优化版本
 * @param state
 * @param goodsId
 * @param skuId
 * @returns {null|object}
 */
const getGoods = (state, goodsId, skuId) => {
  const goodsMap = createGoodsMap(state)
  const key = `${goodsId}_${skuId}`
  const result = goodsMap.get(key)
  return result?.goods || null
}

/**
 * 获取已经选中（未选中）的商品列表 - 优化版本
 * @param state
 * @param select
 * @param groupName
 * @returns {Array}
 */
const getSelectGoods = (state, select, groupName = '') => {
  const selected = select ? 'true' : 'false'
  const result = []

  state.validList.forEach(group => {
    if (groupName && group.groupName !== groupName) return

    group.goodsList?.forEach(goods => {
      if (goods.selected === selected) {
        result.push(goods)
      }
    })
  })

  return result
}



export const CART_QUERY_STATUS = {
  SUCCESS: 'success',
  FAILED: 'failed',
  LOADING: 'loading'
}

export const useNewCartStore = defineStore('newCart', {
  state: () => ({
    validList: [], // 正常商品组
    invalidList: [], // 失效商品组
    cartLoadingStatus: CART_QUERY_STATUS.LOADING // 购物车加载状态
  }),
  getters: {
    // 所有商品数量 - 优化版本
    countAll() {
      return this.validList.reduce((count, group) => {
        return count + (group.goodsList?.reduce((sum, goods) => sum + (goods.skuNum || 0), 0) || 0)
      }, 0)
    },
    getCartLoadingStatus() {
      return this.cartLoadingStatus
    },
    getCartValidList() {
      return this.validList
    },
    getCartInvalidList() {
      return this.invalidList
    },
    // 所有商品的种类数 - 优化版本
    countByGoods() {
      const count = this.validList.reduce((count, group) => {
        return count + (group.goodsList?.length || 0)
      }, 0)

      // 如果登录态且数量为0，重新拉取购物车数据
      if (count === 0) {
        const userStore = useUserStore()
        if (userStore.isLogin === true) {
          this.query()
        }
      }

      return count
    },
    // 选中的商品数量 - 优化版本
    selectCountAll() {
      return this.validList.reduce((count, group) => {
        return count + (group.goodsList?.reduce((sum, goods) => {
          return sum + (goods.selected === 'true' ? (goods.skuNum || 0) : 0)
        }, 0) || 0)
      }, 0)
    },
    // 选中的商品的种类数 - 优化版本
    selectCountByGoods() {
      return this.validList.reduce((count, group) => {
        return count + (group.goodsList?.filter(goods => goods.selected === 'true').length || 0)
      }, 0)
    },
    // 选中商品总金额 - 优化版本，添加数值安全检查
    selectTotalPrice() {
      return this.validList.reduce((total, group) => {
        return total + (group.goodsList?.reduce((sum, goods) => {
          if (goods.selected === 'true') {
            const price = Number(goods.nowPrice) || 0
            const num = Number(goods.skuNum) || 0
            return sum + (price * num)
          }
          return sum
        }, 0) || 0)
      }, 0)
    },
    // 是否所有商品都选中 - 优化版本
    isSelectAll() {
      if (this.validList.length === 0) return false
      return this.validList.every(group => group.selected === true)
    },
    // 新增：是否有商品
    hasGoods() {
      return this.countByGoods > 0
    },
    // 新增：是否有选中商品
    hasSelectedGoods() {
      return this.selectCountByGoods > 0
    },
    // 新增：是否有失效商品
    hasInvalidGoods() {
      return this.invalidList.some(group => group.goodsList?.length > 0)
    },
    // 新增：是否有有效商品
    hasValidGoods() {
      return this.validList.some(group => group.goodsList?.length > 0)
    }
  },
  actions: {
    // 设置有效商品列表
    setValidList(payload) {
      // 数据验证 + 深拷贝保护
      const validatedPayload = payload.filter(validateGroupData)
      this.validList = protectData(validatedPayload)
      this.selectCheck()
    },
    // 设置失效商品列表
    setInvalidList(payload) {
      // 数据验证 + 深拷贝保护
      const validatedPayload = payload.filter(validateGroupData)
      this.invalidList = protectData(validatedPayload)
    },
    setCartLoadingStatus(payload) {
      this.cartLoadingStatus = payload
    },
    // 更新商品细节数据
    updateGoodsMuti(payload) {
      const newState = cloneDeep(this.$state)
      payload.forEach(load => {
        const { goodsId, skuId, goodsNum, select } = load
        const goods = getGoods(newState, goodsId, skuId)
        if (!goods) return
        if (typeof goodsNum === 'number') goods.skuNum = goodsNum
        if (typeof select === 'boolean') goods.selected = select ? 'true' : 'false'
      })
      this.setValidList(newState.validList)
      this.selectCheck()
    },
    // 移除商品数据
    removeGoodsMuti(payload) {
      const newState = cloneDeep(this.$state)
      payload.forEach(load => {
        const { goodsId, skuId } = load
        newState.validList.forEach(group => {
          group.goodsList = group.goodsList.filter(goods => {
            return !(goods.cartGoodsId === goodsId && goods.cartSkuId === skuId)
          })
        })
        newState.invalidList.forEach(group => {
          group.goodsList = group.goodsList.filter(goods => {
            return !(goods.cartGoodsId === goodsId && goods.cartSkuId === skuId)
          })
        })
      })
      this.setValidList(newState.validList)
      this.setInvalidList(newState.invalidList)
      this.hasGoodsCheck()
      this.selectCheck()
    },
    // 处理所有分组的选中状态展示
    selectCheck() {
      const newState = cloneDeep(this.$state)
      newState.validList.forEach(group => {
        if (group.goodsList && group.goodsList.length > 0) {
          const selectedList = group.goodsList.filter(goods => goods.selected === 'true')
          group.selected = selectedList.length === group.goodsList.length
        } else {
          group.selected = false
        }
      })

      newState.validList.forEach(group => {
        protectData(group)
      })

      this.validList = newState.validList
    },
    // 商品分组是否还有商品检查
    // 本方法只是过滤空 group 数据，不改变存在数据的 group
    hasGoodsCheck() {
      const newValidList = this.validList.filter(group => {
        return group.goodsList && group.goodsList.length > 0
      })
      this.validList = newValidList
    },
    // 处理登录
    async login() {
      const userStore = useUserStore()
      await userStore.login({ reload: false })
    },
    // 读取购物车内容
    async query() {
      // 需要先读取地址，再查询购物车
      const userStore = useUserStore()
      await userStore.queryDefaultAddr()
      const addr = userStore.curAddressInfo
      const addressInfo = JSON.stringify({
        provinceId: addr.provinceId,
        provinceName: addr.provinceName,
        cityId: addr.cityId,
        cityName: addr.cityName,
        countyId: addr.countyId,
        countyName: addr.countyName,
        townId: addr.townId,
        townName: addr.townName
      })
      this.setCartLoadingStatus(CART_QUERY_STATUS.LOADING)

      const bizCode = getBizCode('ORDER')
      const zqInfo = queryZqInfo()
      let params = {
        bizCode,
        addressInfo
      }

      if (bizCode === 'zq') {
        params = {
          bizCode,
          addressInfo,
          enterpriseCode: zqInfo.ciCode
        }
      }

      const [err, json] = await cart.query(params)
      if (!err) {
        this.setCartLoadingStatus(CART_QUERY_STATUS.SUCCESS)
        if (json && !json.goodsGroupList) {
          const timestamp = dayjs().format('YYYY-MM-DD hh:mm:ss')
          const params = {
            cartId: json.cartId,
            distriBizCode: json.distriBizCode
          }
          woReport(`购物车监控，分组数据出现丢失。 出现问题时间：${timestamp}`, params)
        }

        // 现要求所有分组合并成一组，特殊处理
        const list1 = json.goodsGroupList.map(group => {
          group.selected = false
          return group
        })

        const list2 = json.invalidGoodsList.filter(goods => {
          return goods.goods
        })
        const validList = list1.length > 0 ? list1 : []
        const invalidList = list2.length > 0 ? [{ groupName: '失效商品', goodsList: list2 }] : []

        this.setValidList(validList)
        this.setInvalidList(invalidList)
        return null
      } else {
        this.setCartLoadingStatus(CART_QUERY_STATUS.FAILED)
        return err
      }
    },
    // 读取购物车内容
    async queryQuick() {
      // 需要先读取地址，再查询购物车
      const userStore = useUserStore()
      await userStore.queryDefaultAddr()
      const addr = userStore.curAddressInfo
      const addressInfo = JSON.stringify({
        provinceId: addr.provinceId,
        provinceName: addr.provinceName,
        cityId: addr.cityId,
        cityName: addr.cityName,
        countyId: addr.countyId,
        countyName: addr.countyName,
        townId: addr.townId,
        townName: addr.townName
      })
      this.setCartLoadingStatus(CART_QUERY_STATUS.LOADING)
      const [err, json] = await cart.baseView({ bizCode: getBizCode('ORDER'), addressInfo })
      if (!err) {
        this.setCartLoadingStatus(CART_QUERY_STATUS.SUCCESS)
        if (json && !json.goodsGroupList) {
          const timestamp = dayjs().format('YYYY-MM-DD hh:mm:ss')
          const params = {
            cartId: json.cartId,
            distriBizCode: json.distriBizCode
          }
          woReport(`购物车监控，分组数据出现丢失。 出现问题时间：${timestamp}`, params)
        }

        const oldCartValidList = this.validList
        let newCartValidList = json.goodsGroupList
        const oldCartInvalidList = this.invalidList
        // 1. 先看看分组是否勾选选了
        const oldItemsMap = new Map(oldCartValidList.map(item => [item.groupName, item.selected]))
        newCartValidList.forEach(newItem => {
          const selected = oldItemsMap.get(newItem.groupName)
          if (selected !== undefined) {
            newItem.selected = selected
          }
        })

        console.warn('oldCartInvalidList', oldCartInvalidList)

        // 创建一个包含所有无效商品ID的集合
        const invalidIds = new Set()
        oldCartInvalidList.forEach(oldItem => {
          oldItem.goodsList.forEach(oldCartInvalidItem => {
            invalidIds.add(oldCartInvalidItem.cartSkuId)
          })
        })

        // 使用这个集合来过滤newCartValidList中的有效商品
        newCartValidList = newCartValidList.map(newItem => {
          return {
            ...newItem,
            goodsList: newItem.goodsList.filter(item => !invalidIds.has(item.cartSkuId))
          }
        })

        console.warn('newCartValidList', newCartValidList)

        const validList = newCartValidList.length > 0 ? newCartValidList : []
        this.setValidList(validList)
        return null
      } else {
        this.setCartLoadingStatus(CART_QUERY_STATUS.FAILED)
        return err
      }
    },
    // 添加商品到购物车
    // payload = { goodsId, skuId, goodsNum }
    async add(payload) {
      await this.login()

      const { goodsId, skuId, goodsNum, addressInfo } = payload
      const bizCode = getBizCode('ORDER')
      const [err] = await cart.add({ goodsId, skuId, goodsNum, bizCode, addressInfo })
      if (!err) {
        this.query()
        return null
      } else {
        return err
      }
    },
    // 更新购物车商品数量
    // payload = { goodsId, skuId, goodsNum } goodsNum 要求 Number 类型
    async updateGoodsNum(payload) {
      await this.login()

      const { goodsId, skuId, goodsNum } = payload
      const goods = getGoods(this.$state, goodsId, skuId)
      if (!goods) return { code: 'FE2002', msg: '当前商品不存在', goodsId, skuId }
      const stock = Number(goods.goods.skuList[0].stock)
      if (goodsNum > stock) {
        return { code: 'FE2001', msg: '最多可购买' + stock + '件', stock }
      }
      if (goodsNum < 1) {
        return { code: 'FE2002', msg: '最少购买 1 件商品', stock }
      }
      const bizCode = getBizCode('ORDER')
      const [err] = await cart.update({ goodsId, skuId, goodsNum, bizCode })
      if (!err) {
        this.updateGoodsMuti([payload])
        return null
      } else {
        return err
      }
    },
    // 更新购物车商品选中状态（多个）
    // payload = [{ goodsId, skuId, select }, ...] selected 要求 Boolean 类型
    async updateGoodsSelectMuti(payload) {
      await this.login()

      const bizCode = getBizCode('ORDER')
      const [err] = await cart.select({ cartGoodsList: JSON.stringify(payload), bizCode })
      if (!err) {
        this.updateGoodsMuti(payload)
        return null
      } else {
        return err
      }
    },
    // 移除商品（多个）
    // payload = [{ goodsId, skuId }, ... ]
    async removeMuti(payload) {
      await this.login()

      const bizCode = getBizCode('ORDER')
      const [err] = await cart.remove({ goodsSkuList: JSON.stringify(payload), bizCode })
      if (!err) {
        this.removeGoodsMuti(payload)
        return null
      } else {
        return err
      }
    },
    // 移除失效商品
    async removeInvalidGoods(payload) {
      await this.login()
      const { deleteGoodsList } = payload
      const bizCode = getBizCode('ORDER')
      const [err] = await cart.removeInvalidGoods({ bizCode, deleteGoodsList })
      if (!err) {
        this.setInvalidList([])
        return null
      } else {
        return err
      }
    },
    // 【封装方法】删除有效区选中的商品
    // payload = true/false 要求 Boolean 类型
    async removeSelectValidGoods() {
      await this.login()
      const removedList = getSelectGoods(this.$state, true).map(goods => {
        return { goodsId: goods.cartGoodsId, skuId: goods.cartSkuId }
      })
      if (removedList.length > 0) {
        return this.removeMuti(removedList)
      }
    },
    // 【封装方法】购物车有效商品全选反选
    async checkedAllValid() {
      await this.login()

      // 查询未选中商品个数
      const unselectCount = getSelectGoods(this.$state, false).length
      // 如果商品存在未选中情况，则执行全选，否则执行反选
      const selectType = unselectCount > 0
      // 需要改变状态的商品列表
      const newPayload = getSelectGoods(this.$state, !selectType).map(goods => {
        return { goodsId: goods.cartGoodsId, skuId: goods.cartSkuId, select: selectType }
      })

      if (newPayload.length === 0) return null
      return this.updateGoodsSelectMuti(newPayload)
    },
    // 【封装方法】购物车有效商品全选反选
    // payload = groupName （传递分组名称）
    async checkedAllByGroupName(payload) {
      await this.login()

      // 查询未选中商品个数
      const unselectCount = getSelectGoods(this.$state, false, payload).length
      // 如果商品存在未选中情况，则执行全选，否则执行反选
      const selectType = unselectCount > 0
      // 需要改变状态的商品列表
      const newPayload = getSelectGoods(this.$state, !selectType, payload).map(goods => {
        return { goodsId: goods.cartGoodsId, skuId: goods.cartSkuId, select: selectType }
      })

      if (newPayload.length === 0) return null
      return this.updateGoodsSelectMuti(newPayload)
    },
    // （无需关注登录状态，通过sku自己计算）检查库存：{ flag:boolean, num:null|number }
    // flag: true 表示库存充足，false 表示库存不足，null 表示库存未知（查询失败）
    // num: 表示具体库存数量，null 表示具体数量未知（如果 flag 为 true，则可认为库存充裕）
    async checkStock(payload) {
      const { sku, skuNum } = payload
      const result = { flag: true, num: null }

      // sku.stock=0 表示无货（业管配置）
      if (String(sku.stock) === '0') {
        result.flag = false
        result.num = 0
        return result
      }
      // 无供货商skuId则认为无货（20220523，后端郝孝虎说这个字段不再使用了，by LIJINGCHEN）
      // if (!sku.supplierSkuId) {
      //   result.flag = false
      //   result.num = 0
      //   return result
      // }
      // 非京东供货商返回业管配置的商品库存
      if (sku.supplierCode !== 'jd') {
        if (!sku.stock) {
          // 库存未知（可能是存量数据），认为库存充足
          result.flag = true
          result.num = null
        } else {
          // 有具体库存数量
          result.flag = Number(sku.stock) >= skuNum
          result.num = Number(sku.stock)
        }
        return result
      }

      // 走到此处，是京东逻辑，由于目前没有京东商品了，故直接算作库存为0
      result.flag = false
      result.num = 0
      return result
    }
  }
})
