import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { closeToast, showLoadingToast, showToast } from 'vant'
import { get } from 'lodash-es'
import dayjs from 'dayjs'
import { useAlert } from '@/composables/index.js'
import { useAfterSalesStore } from '@store/modules/afterSales.js'
import { afterSalesProduct } from '@/utils/storage.js'
import { getBizCode } from '@/utils/curEnv.js'
import {
  applyOrderAfterSalesJD
} from '@/api/interface/order.js'
import {
  applyOrderCancel,
  jdPrompt
} from '@/api/interface/afterSales.js'
import {JD_GOODS_CODE} from "@utils/types.js";

export function useOrderAfterSalesActions() {
  const router = useRouter()
  const $alert = useAlert()
  const afterSalesStore = useAfterSalesStore()
  const expirationPopupVisible = ref(false)

  // 订单状态常量已移除，直接使用数字判断：
  // '0': 待付款, '1': 待发货, '2': 已取消, '3': 待发货, '4': 部分发货, '5': 配送中,
  // '6': 部分撤销, '7': 拒收, '8': 已撤销, '9': 已签收, '10': 已退款, '11': 部分退款, '12': 部分退款中, '-1': 已删除

  const isExpires = (item, options = {}) => {
    const { useSubOrderData = false } = options

    let orderDate
    if (useSubOrderData) {
      orderDate = get(item, 'subOrderRawData.orderDate') || get(item, 'orderDate')
    } else {
      orderDate = get(item, 'subOrderRawData.orderDate') || get(item, 'expireTime') || get(item, 'orderDate')
    }

    if (!orderDate) return false

    const expDate = dayjs(orderDate).add(15, 'day')
    const now = dayjs()
    return expDate > now
  }

  const generateActionButtons = (item, options = {}) => {
    const {
      showAddToCart = false,
      handleAddToCart = null,
      useSubOrderData = false,
      afterSalesInfo = null,
      // 新增售后相关按钮的显示控制
      showRefundButton = true,
      showAfterSalesButton = true,
      showViewDetailsButton = true
    } = options

    const actions = []
    const orderState = item.orderState

    let afterSaleApplyList, isApplyAfterSales, supplierCode

    if (useSubOrderData) {
      const subOrder = item.subOrderRawData
      afterSaleApplyList = get(subOrder, 'afterSaleApplyList', [])
      isApplyAfterSales = afterSaleApplyList.length > 0
      supplierCode = get(subOrder, 'skuNumInfoList[0].sku.supplierCode', '')
    } else {
      isApplyAfterSales = get(item, 'afterSaleId', '')
      supplierCode = get(item, 'skuNumInfoList[0].sku.supplierCode', '')
    }

    const isExpired = !isExpires(item, { useSubOrderData })
    const isJDSupplier = supplierCode.indexOf(JD_GOODS_CODE) > -1

    if (showAddToCart && handleAddToCart) {
      actions.push({
        key: 'addToCart',
        label: '加购物车',
        type: 'default',
        handler: handleAddToCart
      })
    }

    // 当 bizCode === 'zq' 时，跳过售后逻辑
    const bizCode = getBizCode()
    if (bizCode === 'zq') {
      return actions
    }

    const applyType = useSubOrderData ? afterSalesInfo?.applyType : item.applyType

    if (applyType) {
      if (applyType === '1') {
        // 京东供应商：待发货(1,3)或配送中(5)状态且已申请售后
        if (showRefundButton && isJDSupplier && (orderState === '1' || orderState === '3' || orderState === '5') && isApplyAfterSales) {
          actions.push({
            key: 'applyRefund',
            label: '申请退款',
            type: isExpired && !isApplyAfterSales ? 'default' : 'gradient',
            disabled: isExpired,
            handler: useSubOrderData ? () => processAfterSalesRequest(item, 0, options) : () => processAfterSalesRequest(item, 0, options)
          })
        // 非京东供应商：待发货(1,3)状态且已申请售后
        } else if (showRefundButton && !isJDSupplier && (orderState === '1' || orderState === '3') && isApplyAfterSales) {
          actions.push({
            key: 'applyRefund',
            label: '申请退款',
            type: isExpired && !isApplyAfterSales ? 'default' : 'gradient',
            disabled: isExpired,
            handler: useSubOrderData ? () => processAfterSalesRequest(item, 0, options) : () => processAfterSalesRequest(item, 0, options)
          })
        }
      } else if (applyType === '2') {
        // 京东供应商：已签收(9)状态且已申请售后
        if (showAfterSalesButton && isJDSupplier && orderState === '9' && isApplyAfterSales) {
          actions.push({
            key: 'applyAfterSale',
            label: '申请售后',
            type: isExpired && !isApplyAfterSales ? 'default' : 'gradient',
            disabled: isExpired,
            handler: useSubOrderData ? () => processAfterSalesRequest(item, 1, options) : () => processAfterSalesRequest(item, 1, options)
          })
        // 非京东供应商：配送中(5)或已签收(9)状态且已申请售后
        } else if (showAfterSalesButton && !isJDSupplier && (orderState === '5' || orderState === '9') && isApplyAfterSales) {
          actions.push({
            key: 'applyAfterSale',
            label: '申请售后',
            type: isExpired && !isApplyAfterSales ? 'default' : 'gradient',
            disabled: isExpired,
            handler: useSubOrderData ? () => processAfterSalesRequest(item, 1, options) : () => processAfterSalesRequest(item, 1, options)
          })
        }
      }
    } else {
      // 京东供应商：待发货(1,3)或配送中(5)状态且未申请售后
      if (showRefundButton && isJDSupplier && (orderState === '1' || orderState === '3' || orderState === '5') && !isApplyAfterSales) {
        actions.push({
          key: 'applyRefund',
          label: '申请退款',
          type: isExpired && isApplyAfterSales ? 'default' : 'gradient',
          disabled: isExpired,
          handler: () => processAfterSalesRequest(item, 0, options)
        })
      // 非京东供应商：待发货(1,3)状态且未申请售后
      } else if (showRefundButton && !isJDSupplier && (orderState === '1' || orderState === '3') && !isApplyAfterSales) {
        actions.push({
          key: 'applyRefund',
          label: '申请退款',
          type: isExpired && !isApplyAfterSales ? 'default' : 'gradient',
          disabled: isExpired,
          handler: () => processAfterSalesRequest(item, 0, options)
        })
      }

      // 京东供应商：已签收(9)状态且未申请售后
      if (showAfterSalesButton && isJDSupplier && orderState === '9' && !isApplyAfterSales) {
        actions.push({
          key: 'applyAfterSale',
          label: '申请售后',
          type: isExpired && !isApplyAfterSales ? 'default' : 'gradient',
          disabled: isExpired,
          handler: () => processAfterSalesRequest(item, 1, options)
        })
      // 非京东供应商：配送中(5)或已签收(9)状态且未申请售后
      } else if (showAfterSalesButton && !isJDSupplier && (orderState === '5' || orderState === '9') && !isApplyAfterSales) {
        actions.push({
          key: 'applyAfterSale',
          label: '申请售后',
          type: isExpired && !isApplyAfterSales ? 'default' : 'gradient',
          disabled: isExpired,
          handler: () => processAfterSalesRequest(item, 1, options)
        })
      }
    }

    if (showViewDetailsButton && isApplyAfterSales) {
      actions.push({
        key: 'viewDetails',
        label: '查看详情',
        type: 'gradient',
        disabled: false,
        handler: () => processAfterSalesRequest(item, 2, options)
      })
    }

    return actions
  }

  const processAfterSalesRequest = async (item, type, options = {}) => {
    const { useSubOrderData = false, currentOrderStatus = null } = options

    const isExpired = !isExpires(item, { useSubOrderData })

    let hasAfterSaleId, supplierSubOrderId, orderPrice, orderState, supplierSubOutOrderId, skuNum, sku, supplierCode

    if (useSubOrderData) {
      const subOrder = item.subOrderRawData
      const afterSaleApplyList = get(subOrder, 'afterSaleApplyList', [])
      hasAfterSaleId = afterSaleApplyList.length > 0

      supplierSubOrderId = get(subOrder, 'id')
      orderPrice = subOrder.orderPrice
      orderState = subOrder.orderState
      supplierSubOutOrderId = subOrder.supplierSubOutOrderId
      const skuInfo = get(subOrder, 'skuNumInfoList[0]', {})
      skuNum = skuInfo.skuNum
      sku = skuInfo.sku
      supplierCode = sku?.supplierCode
    } else {
      hasAfterSaleId = get(item, 'afterSaleId', '')

      supplierSubOrderId = item.supplierSubOrderId
      orderPrice = item.orderPrice
      orderState = item.orderState
      supplierSubOutOrderId = item.supplierSubOutOrderId
      const skuInfo = get(item, 'skuNumInfoList[0]', {})
      skuNum = skuInfo.skuNum
      sku = skuInfo.sku
      supplierCode = sku?.supplierCode
    }

    if (isExpired && !hasAfterSaleId) {
      expirationPopupVisible.value = true
      return
    }

    const afterSalesProductInfo = {
      supplierSubOrderId,
      orderState,
      orderPrice,
      skuNum,
      sku,
      supplierCode,
      supplierOutSubOrderId: supplierSubOutOrderId || ''
    }
    afterSalesProduct.set(afterSalesProductInfo)

    const applyType = useSubOrderData ? options.afterSalesInfo?.applyType : item.applyType
    if (applyType) {
      afterSalesStore.updateAfterSalesInfo({
        applyType: '',
        afterSaleState: '',
        bizOrderId: '',
        bizCode: '',
        orderState: ''
      })
    }

    const isJDSupplier = supplierCode && supplierCode.indexOf(JD_GOODS_CODE) > -1

    let giftInfo, afterSaleId, afterSaleApplyType
    if (useSubOrderData) {
      const subOrder = item.subOrderRawData
      giftInfo = get(subOrder, 'giftInfoList', [])
      const afterSaleApplyList = get(subOrder, 'afterSaleApplyList', [])
      afterSaleId = afterSaleApplyList[0]?.id
      afterSaleApplyType = afterSaleApplyList[0]?.applyType
    } else {
      giftInfo = +get(item, 'isHaveGift', '0')
      afterSaleId = item.afterSaleId
      afterSaleApplyType = item.applyType
    }

    if (isJDSupplier) {
      await handleJDAfterSales(supplierSubOrderId, supplierCode, type, giftInfo, afterSaleId, afterSaleApplyType, orderState, currentOrderStatus)
    } else {
      await handleNonJDAfterSales(supplierSubOrderId, type, afterSaleId, afterSaleApplyType, orderState)
    }
  }

  const handleJDAfterSales = async (supplierSubOrderId, supplierCode, type, giftInfo, afterSaleId, applyType, orderState, currentOrderStatus) => {
    try {
      const [err, res] = await jdPrompt({ supplierSubOrderId, supplierCode })
      if (err) {
        showToast(err.msg)
        return
      }

      const prompt = res
      const finalOrderState = currentOrderStatus || orderState

      // 已签收状态(9)
      if (finalOrderState === '9') {
        if (type === 1) {
          const hasGift = Array.isArray(giftInfo) ? giftInfo.length > 0 : giftInfo > 0

          if (hasGift) {
            $alert({
              title: '',
              message: '该商品有赠品，如申请售后，请将赠品一同寄回。',
              confirmButtonText: '确定申请',
              cancelButtonText: '暂不申请',
              showCancelButton: true,
              onConfirmCallback: () => {
                applyJDAfterSales(supplierSubOrderId, 1)
              }
            })
            return
          }
          await applyJDAfterSales(supplierSubOrderId, 1)
        } else if (type === 2) {
          await applyJDAfterSales(supplierSubOrderId, 2)
        }
      } else {
        if (type === 0) {
          $alert({
            title: '',
            message: prompt,
            confirmButtonText: '确定申请',
            cancelButtonText: '暂不申请',
            showCancelButton: true,
            onConfirmCallback: () => {
              applyJDRefund(supplierSubOrderId)
            }
          })
        } else if (type === 1) {
          await applyJDAfterSales(supplierSubOrderId, 1)
        } else if (type === 2) {
          if (applyType === '1') {
            router.push({
              path: '/wo-after-sales-detail',
              query: {
                afterSaleId,
                type: +applyType
              }
            })
          } else {
            await applyJDAfterSales(supplierSubOrderId, 2)
          }
        }
      }
    } catch (error) {
      showToast('操作失败，请重试')
      console.error('京东售后处理错误:', error)
    }
  }

  const handleNonJDAfterSales = async (supplierSubOrderId, type, afterSaleId, applyType, orderState) => {
    try {
      if (type === 0) {
        $alert({
          title: '',
          message: '您确定申请退款吗？',
          confirmButtonText: '确定申请',
          cancelButtonText: '暂不申请',
          showCancelButton: true,
          onConfirmCallback: () => {
            applyRefund(supplierSubOrderId)
          }
        })
      } else if (type === 1) {
        router.push({
          path: '/wo-after-sales-entry',
          query: {
            orderState: orderState
          }
        })
      } else if (type === 2) {
        router.push({
          path: '/wo-after-sales-detail',
          query: {
            afterSaleId,
            type: +applyType
          }
        })
      }
    } catch (error) {
      showToast('操作失败，请重试')
      console.error('非京东售后处理错误:', error)
    }
  }

  const applyJDRefund = async (supplierSubOrderId) => {
    try {
      showLoadingToast()
      const [err, res] = await applyOrderCancel({ supplierSubOrderId })
      closeToast()

      if (!err) {
        const afterSaleId = res
        router.push({
          path: '/wo-after-sales-detail',
          query: {
            afterSaleId,
            type: 1
          }
        })
      } else {
        showToast(err.msg)
      }
    } catch (error) {
      closeToast()
      showToast('申请退款失败')
      console.error('京东退款申请错误:', error)
    }
  }

  const applyJDAfterSales = async (supplierSubOrderId, firstUrl) => {
    try {
      showLoadingToast()
      const [err, json] = await applyOrderAfterSalesJD({
        supplierSubOrderId,
        firstUrl
      })
      closeToast()

      if (!err) {
        window.location.href = json
      } else {
        showToast(err.msg)
      }
    } catch (error) {
      closeToast()
      showToast('申请售后失败')
      console.error('京东售后申请错误:', error)
    }
  }

  const applyRefund = async (supplierSubOrderId) => {
    try {
      showLoadingToast()
      const [err, res] = await applyOrderCancel({ supplierSubOrderId })
      closeToast()

      if (!err) {
        const afterSaleId = res
        router.push({
          path: '/wo-after-sales-detail',
          query: {
            afterSaleId,
            type: 1
          }
        })
      } else {
        showToast(err.msg)
      }
    } catch (error) {
      closeToast()
      showToast('申请退款失败')
      console.error('退款申请错误:', error)
    }
  }

  return {
    expirationPopupVisible,
    isExpires,
    generateActionButtons,
    processAfterSalesRequest,
    handleJDAfterSales,
    handleNonJDAfterSales,
    applyJDRefund,
    applyJDAfterSales,
    applyRefund
  }
}
