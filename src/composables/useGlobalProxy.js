import { getCurrentInstance } from 'vue'

/**
 * 获取全局代理对象 Composable
 * 用于在组合式函数中获取 Vue 实例的全局代理对象
 * 
 * @returns {Object|undefined} Vue 实例的代理对象，如果不在组件上下文中则返回 undefined
 * 
 * @example
 * ```javascript
 * import { useGlobalProxy } from '@/composables'
 * 
 * // 在 setup 函数或其他组合式函数中使用
 * const proxy = useGlobalProxy()
 * if (proxy) {
 *   // 可以访问全局属性和方法
 *   proxy.$router.push('/home')
 * }
 * ```
 */
export function useGlobalProxy() {
  const { proxy } = getCurrentInstance() || {}
  return proxy
}

/**
 * 获取全局提示方法 Composable
 * 用于获取全局注册的 $alert 方法
 * 
 * @returns {Function|undefined} 全局 $alert 方法，如果未注册或不在组件上下文中则返回 undefined
 * 
 * @example
 * ```javascript
 * import { useAlert } from '@/composables'
 * 
 * // 在 setup 函数中使用
 * const $alert = useAlert()
 * if ($alert) {
 *   $alert({
 *     title: '提示',
 *     message: '操作成功',
 *     confirmButtonText: '确定'
 *   })
 * }
 * ```
 */
export function useAlert() {
  const proxy = useGlobalProxy()
  return proxy?.$alert
}
