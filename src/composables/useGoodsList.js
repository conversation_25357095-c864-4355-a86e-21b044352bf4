import { ref, computed } from 'vue'
import { useRouter } from 'vue-router'
import { useUserStore } from '@store/modules/user.js'
import { useNewCartStore } from '@store/modules/newCart.js'
import { getBizCode } from '@utils/curEnv.js'
import { addOneClick } from '@api/index.js'
import { closeToast, showLoadingToast, showToast } from 'vant'
import { pick, compact, debounce, isNil, get, defaultTo } from 'lodash-es'

/**
 * 商品列表管理 Composable
 * 用于管理商品列表页面的状态和操作，包括分页、筛选、购物车操作等
 * 
 * @returns {Object} 商品列表管理对象
 * @returns {Ref<Array>} returns.goodsList - 商品列表数据
 * @returns {Ref<boolean>} returns.loading - 加载状态
 * @returns {Ref<boolean>} returns.finished - 是否加载完成（无更多数据）
 * @returns {Ref<boolean>} returns.isLoading - 初始加载状态
 * @returns {Ref<number>} returns.pageNo - 当前页码
 * @returns {Ref<number>} returns.pageSize - 每页数量
 * @returns {Ref<Object>} returns.filterCriteria - 筛选条件
 * @returns {ComputedRef<boolean>} returns.hasFilterConditions - 是否有筛选条件
 * @returns {ComputedRef<Object>} returns.curAddrInfo - 当前地址信息
 * @returns {ComputedRef<string>} returns.locationText - 地址文本
 * @returns {ComputedRef<string>} returns.addressInfo - 地址信息JSON字符串
 * @returns {Function} returns.resetList - 重置列表状态
 * @returns {Function} returns.processGoodsData - 处理商品数据
 * @returns {Function} returns.applyStockFilter - 应用库存筛选
 * @returns {Function} returns.goToDetail - 跳转到商品详情
 * @returns {Function} returns.goToCart - 跳转到购物车
 * @returns {Function} returns.addOneCart - 一键加入购物车
 * @returns {Function} returns.handleFilterConfirm - 处理筛选确认
 * @returns {Function} returns.handleFilterReset - 处理筛选重置
 * @returns {Function} returns.handleAddressChanged - 处理地址变更
 * 
 * @example
 * ```javascript
 * import { useGoodsList } from '@/composables'
 * 
 * // 在组件中使用
 * const {
 *   goodsList,
 *   loading,
 *   finished,
 *   pageNo,
 *   filterCriteria,
 *   hasFilterConditions,
 *   resetList,
 *   processGoodsData,
 *   goToDetail,
 *   addOneCart
 * } = useGoodsList()
 * 
 * // 处理商品数据
 * const processedGoods = processGoodsData(rawGoodsData)
 * 
 * // 跳转到商品详情
 * goToDetail(goodsItem)
 * 
 * // 添加到购物车
 * addOneCart(goodsItem)
 * ```
 */
export function useGoodsList() {
  const userStore = useUserStore()
  const cartStore = useNewCartStore()
  const router = useRouter()

  /**
   * 基础状态管理
   */
  const goodsList = ref([])
  const loading = ref(false)
  const finished = ref(false)
  const isLoading = ref(true)
  const pageNo = ref(1)
  const pageSize = ref(10)

  /**
   * 筛选条件状态
   */
  const filterCriteria = ref({
    isStock: false,
    minPrice: '',
    maxPrice: '',
    brandsList: []
  })

  /**
   * 检查是否有筛选条件
   * 
   * @returns {boolean} 是否存在任何筛选条件
   */
  const hasFilterConditions = computed(() => {
    const { isStock, minPrice, maxPrice, brandsList } = filterCriteria.value
    return isStock ||
      minPrice !== '' ||
      maxPrice !== '' ||
      brandsList.some(brand => brand.isSelected)
  })

  /**
   * 当前地址信息
   * 
   * @returns {Object} 用户当前选择的地址信息
   */
  const curAddrInfo = computed(() => userStore.curAddressInfo)

  /**
   * 地址显示文本
   * 
   * @returns {string} 格式化的地址文本
   */
  const locationText = computed(() => {
    return curAddrInfo.value?.addrDetail || ''
  })

  /**
   * 地址信息JSON字符串
   * 用于API请求参数
   * 
   * @returns {string} 包含地址信息的JSON字符串
   */
  const addressInfo = computed(() => {
    const addressFields = ['provinceId', 'provinceName', 'cityId', 'cityName', 'countyId', 'countyName', 'townId', 'townName']
    return JSON.stringify(pick(curAddrInfo.value, addressFields))
  })

  /**
   * 重置列表状态
   * 将分页和列表数据重置为初始状态
   */
  const resetList = () => {
    pageNo.value = 1
    finished.value = false
    goodsList.value = []
  }

  /**
   * 处理商品数据
   * 对原始商品数据进行格式化和处理，包括图片、参数、价格等
   * 
   * @param {Array<Object>} items - 原始商品数据数组
   * @returns {Array<Object>} 处理后的商品数据数组
   */
  const processGoodsData = (items) => {
    const bizCode = getBizCode()
    const isZqBiz = bizCode === 'zq'

    return items.map(item => {
      // 处理商品展示图片URL
      item.showImageUrl = item.imageUrl?.split(',')[0] || item.listImageUrl || ''

      // 根据业务类型处理商品参数
      const paramSource = isZqBiz ? item.skuList?.[0] : item
      item.params = compact([
        paramSource?.param,
        paramSource?.param1,
        paramSource?.param2,
        paramSource?.param3,
        paramSource?.param4
      ])

      // 处理销量数据
      item.realSaleVolume = item.sales ?? item.realSaleVolume ?? 0

      // 针对zq业务的特殊价格处理
      if (isZqBiz) {
        const hasNoPrice = isNil(item.highPrice) || item.highPrice === '' ||
          isNil(item.lowPrice) || item.lowPrice === ''

        if (hasNoPrice) {
          console.warn('商品价格缺失，使用SKU价格:', item)
          const firstSku = get(item, 'skuList[0]')

          // 从SKU获取价格并格式化
          item.highPrice = !isNil(firstSku?.highPrice) ?
            (firstSku.highPrice / 100).toFixed(2) :
            defaultTo(item.highPrice, '')

          item.lowPrice = !isNil(firstSku?.lowPrice) ?
            (firstSku.lowPrice / 100).toFixed(2) :
            defaultTo(item.lowPrice, '')
        } else {
          console.warn('使用商品原价格:', item)
          
          /**
           * 统一的价格格式化函数
           * 
           * @param {number|string} price - 原始价格（分为单位）
           * @returns {string} 格式化后的价格（元为单位，保留2位小数）
           */
          const formatPrice = (price) => !isNil(price) ? (price / 100).toFixed(2) : ''

          item.highPrice = formatPrice(item.highPrice)
          item.lowPrice = formatPrice(item.lowPrice)
        }
      }
      
      return item
    })
  }

  /**
   * 应用库存筛选
   * 根据库存筛选条件过滤商品列表
   * 
   * @param {Array<Object>} items - 商品数据数组
   * @returns {Array<Object>} 筛选后的商品数据数组
   */
  const applyStockFilter = (items) => {
    if (filterCriteria.value.isStock) {
      return items.filter(item => item.stock > 0)
    }
    return items
  }

  /**
   * 跳转到商品详情页
   * 
   * @param {Object} item - 商品对象
   * @param {string} item.goodsId - 商品ID
   * @param {string} item.skuId - SKU ID
   */
  const goToDetail = (item) => {
    router.push(`/goodsdetail/${item.goodsId}/${item.skuId}`)
  }

  /**
   * 跳转到购物车页面
   */
  const goToCart = () => {
    router.push('/cart')
  }

  /**
   * 一键加入购物车
   * 使用防抖处理，避免重复点击
   * 
   * @param {Object} item - 商品对象
   * @param {string} item.skuId - SKU ID
   * @param {string} item.goodsId - 商品ID
   */
  const addOneCart = debounce(async (item) => {
    try {
      showLoadingToast()

      const [err] = await addOneClick({
        bizCode: getBizCode('ORDER'),
        skuId: item.skuId,
        goodsId: item.goodsId,
        addressInfo: addressInfo.value
      })

      closeToast()

      if (err) {
        showToast(err.msg)
        return
      }

      showToast('加入购物车成功！')
      // 更新购物车数据
      await cartStore.query()
    } catch (error) {
      closeToast()
      console.error('加入购物车失败:', error)
      showToast('加入购物车失败，请重试')
    }
  }, 300)

  /**
   * 处理筛选确认
   * 应用筛选条件并重新获取数据
   * 
   * @param {Object} [criteria] - 筛选条件对象
   * @param {Function} fetchFunction - 获取数据的函数
   */
  const handleFilterConfirm = (criteria, fetchFunction) => {
    if (criteria) {
      console.log('应用筛选条件:', criteria)
    }

    resetList()
    if (typeof fetchFunction === 'function') {
      fetchFunction()
    } else {
      console.warn('handleFilterConfirm: fetchFunction is not provided or not a function')
    }
  }

  /**
   * 处理筛选重置
   * 清空所有筛选条件
   */
  const handleFilterReset = () => {
    console.log('重置筛选条件')
  }

  /**
   * 处理地址变更
   * 当用户切换地址时重新获取数据
   * 
   * @param {Function} fetchFunction - 获取数据的函数
   */
  const handleAddressChanged = (fetchFunction) => {
    resetList()
    if (typeof fetchFunction === 'function') {
      fetchFunction()
    } else {
      console.warn('handleAddressChanged: fetchFunction is not provided or not a function')
    }
  }

  return {
    // 状态
    goodsList,
    loading,
    finished,
    isLoading,
    pageNo,
    pageSize,
    filterCriteria,

    // 计算属性
    hasFilterConditions,
    curAddrInfo,
    locationText,
    addressInfo,

    // 方法
    resetList,
    processGoodsData,
    applyStockFilter,
    goToDetail,
    goToCart,
    addOneCart,
    handleFilterConfirm,
    handleFilterReset,
    handleAddressChanged
  }
}
