import { ref } from 'vue'
import { pullAll, cloneDeep, intersection } from 'lodash-es'
import { getGoodsDetail, isWhiteUserLimitCheck } from '@api/interface/goods.js'
import { getBizCode } from '@utils/curEnv.js'
import { useUserStore } from '@/store/modules/user.js'

/**
 * 商品详情管理 Composable
 * 用于管理商品详情页面的状态和操作，包括SKU选择、规格管理、库存检查等
 * 
 * @param {string} goodsId - 商品ID
 * @param {string} [skuId] - 可选的SKU ID，用于初始化选中的SKU
 * 
 * @returns {Object} 商品详情管理对象
 * @returns {Ref<Object|null>} returns.spu - SPU商品信息
 * @returns {Ref<Array>} returns.curSpecs - 当前选中的规格数组
 * @returns {Ref<string>} returns.curSkuId - 当前选中的SKU ID
 * @returns {Ref<boolean>} returns.loading - 加载状态
 * @returns {Function} returns.querySpu - 查询SPU商品详情
 * @returns {Function} returns.querySku - 查询当前选中的SKU信息
 * @returns {Function} returns.querySkuCount - 查询SKU总数
 * @returns {Function} returns.querySpecsList - 查询规格列表
 * @returns {Function} returns.queryCurSpecs - 查询当前有效的规格选择
 * @returns {Function} returns.setSpecs - 设置规格选择
 * @returns {Function} returns.queryDisabledSpecs - 查询不可选的规格
 * @returns {Function} returns.checkSkuAvailable - 检查SKU可用性
 * @returns {Function} returns.checkSpuAvailable - 检查SPU可用性（白名单用户限制）
 * @returns {Function} returns.isSpecsComplete - 检查规格选择是否完整
 * 
 * @example
 * ```javascript
 * import { useGoodsDetail } from '@/composables'
 * 
 * // 在组件中使用
 * const {
 *   spu,
 *   curSpecs,
 *   curSkuId,
 *   loading,
 *   querySpu,
 *   querySku,
 *   setSpecs,
 *   checkSkuAvailable
 * } = useGoodsDetail('12345', 'sku001')
 * 
 * // 获取商品详情
 * const result = await querySpu()
 * 
 * // 选择规格
 * const success = setSpecs('_p0_红色')
 * 
 * // 检查当前SKU可用性
 * const { status, err } = checkSkuAvailable()
 * ```
 */
export function useGoodsDetail(goodsId, skuId) {
  const spu = ref(null)
  const curSpecs = ref([])
  const curSkuId = ref(skuId || '')
  const specsList = ref(null)
  const loading = ref(false)
  const userStore = useUserStore()

  /**
   * 查询SPU商品详情
   * 获取商品的完整信息，包括SKU列表、规格等，并初始化当前选中的SKU
   * 
   * @returns {Promise<Object>} API响应结果
   */
  const querySpu = async () => {
    loading.value = true
    try {
      // 获取用户默认地址信息
      await userStore.queryDefaultAddr()
      const info = userStore.curAddressInfo
      const addressInfo = JSON.stringify({
        provinceId: info.provinceId,
        provinceName: info.provinceName,
        cityId: info.cityId,
        cityName: info.cityName,
        countyId: info.countyId,
        countyName: info.countyName,
        townId: info.townId,
        townName: info.townName
      })

      // 调用API获取商品详情
      const [json, goodsDetail] = await getGoodsDetail({
        bizCode: getBizCode('GOODS'),
        goodsId,
        skuId,
        addressInfo
      })

      // 验证返回数据
      if (!goodsDetail || !goodsDetail.skuList || goodsDetail.skuList.length === 0) {
        loading.value = false
        return json
      }

      spu.value = goodsDetail
      let skuCache = {}

      // 如果指定了SKU ID，尝试找到对应的SKU
      if (curSkuId.value) {
        const sku = spu.value.skuList.find(sku => curSkuId.value === sku.skuId)
        if (sku) skuCache = sku
      }

      // 如果没有找到指定的SKU，选择默认SKU
      if (!skuCache.skuId) {
        // 优先选择第一个可用的SKU
        const availableSku = spu.value.skuList.find(sku => {
          const { status } = checkSkuAvailable(sku)
          return status === 0
        })

        if (availableSku) {
          curSkuId.value = availableSku.skuId
          skuCache = availableSku
        } else {
          // 如果没有可用SKU，使用第一个
          curSkuId.value = spu.value.skuList[0].skuId
          skuCache = spu.value.skuList[0]
        }
      }

      // 初始化当前选中的规格
      const { param, param1, param2, param3 } = skuCache
      curSpecs.value = ['_p0_' + param, '_p1_' + param1, '_p2_' + param2, '_p3_' + param3].filter(p => !!p)
      loading.value = false
      return cloneDeep(json)
    } catch (error) {
      loading.value = false
      console.error('querySpu error:', error)
      return { code: '9999', msg: error.message || '获取商品详情失败' }
    }
  }

  /**
   * 查询当前选中的SKU信息
   * 
   * @returns {Object|null} 当前SKU的详细信息，如果未找到则返回null
   */
  const querySku = () => {
    if (!spu.value || !spu.value.skuList || !curSkuId.value) {
      return null
    }
    return cloneDeep(spu.value.skuList.find(sku => sku.skuId === curSkuId.value))
  }

  /**
   * 查询SKU总数
   * 
   * @returns {number} SKU的总数量
   */
  const querySkuCount = () => {
    if (!spu.value || !spu.value.skuList) {
      return 0
    }
    return spu.value.skuList.length
  }

  /**
   * 查询规格列表
   * 从所有SKU中提取出所有可能的规格选项，按param、param1、param2、param3分组
   * 
   * @returns {Array<Array<string>>} 四个规格组的数组，每个组包含该规格的所有可选值
   */
  const querySpecsList = () => {
    if (specsList.value) return specsList.value
    if (!spu.value || !spu.value.skuList) {
      return [[], [], [], []]
    }

    const specs = [[], [], [], []]
    spu.value.skuList.forEach(sku => {
      const { param, param1, param2, param3 } = sku
      
      // 收集第一个规格参数
      if (param) {
        const has0 = specs[0].indexOf('_p0_' + param) >= 0
        if (!has0) specs[0].push('_p0_' + param)
      }
      
      // 收集第二个规格参数
      if (param1) {
        const has1 = specs[1].indexOf('_p1_' + param1) >= 0
        if (!has1) specs[1].push('_p1_' + param1)
      }
      
      // 收集第三个规格参数
      if (param2) {
        const has2 = specs[2].indexOf('_p2_' + param2) >= 0
        if (!has2) specs[2].push('_p2_' + param2)
      }
      
      // 收集第四个规格参数
      if (param3) {
        const has3 = specs[3].indexOf('_p3_' + param3) >= 0
        if (!has3) specs[3].push('_p3_' + param3)
      }
    })
    
    specsList.value = cloneDeep(specs)
    return specsList.value
  }

  /**
   * 查询当前有效的规格选择
   * 从当前选中的规格中筛选出在规格列表中存在的有效规格
   * 
   * @returns {Array<string>} 当前有效的规格选择数组
   */
  const queryCurSpecs = () => {
    const specsListArr = querySpecsList()
    const newList = []
    specsListArr.forEach(item => {
      curSpecs.value.forEach(curSpec => {
        if (item.indexOf(curSpec) >= 0) newList.push(curSpec)
      })
    })
    return newList
  }

  /**
   * 设置规格选择
   * 根据用户选择的规格更新当前规格状态，并尝试确定唯一的SKU
   * 
   * @param {string} spec - 要设置的规格值（格式如：'_p0_红色'）
   * @returns {boolean} 是否成功确定了唯一的SKU
   */
  const setSpecs = (spec) => {
    if (!spu.value || !spu.value.skuList) {
      return false
    }

    // 检查规格是否被禁用
    if (queryDisabledSpecs().indexOf(spec) >= 0) return false
    
    let cur = queryCurSpecs()
    const isSelected = cur.find(s => s === spec)
    
    if (isSelected) {
      // 如果已选中，则取消选择
      cur = cur.filter(s => s !== spec)
    } else {
      // 如果未选中，则添加选择（同组规格互斥）
      const curGroup = getSpecsGroupFromSpec(querySpecsList(), spec)
      pullAll(cur, curGroup)
      cur.push(spec)
    }
    
    curSpecs.value = cur
    
    // 根据当前规格选择查找匹配的SKU
    const list = getSkuListFromSpec(spu.value.skuList, cur)
    if (list.length === 1) {
      curSkuId.value = list[0].skuId
      return true
    } else {
      return false
    }
  }

  /**
   * 查询不可选的规格
   * 基于当前规格选择和库存状态，计算出哪些规格选项应该被禁用
   * 
   * @returns {Array<string>} 不可选的规格数组
   */
  const queryDisabledSpecs = () => {
    if (!spu.value || !spu.value.skuList) {
      return []
    }

    const specsListArr = querySpecsList()
    const cur = queryCurSpecs()
    let disabledSpecs = [...specsListArr[0], ...specsListArr[1], ...specsListArr[2], ...specsListArr[3]]

    /**
     * 规格可用性检查器
     * 对指定规格组进行可用性检查，移除不可选的规格
     */
    const runner = (specsListGroup, paramName) => {
      if (specsListGroup.length > 0) {
        const filteredSpecs = pullAll(cloneDeep(cur), specsListGroup)
        
        // 筛选出可用且匹配当前规格选择的SKU
        const skuList = spu.value.skuList.filter(sku => {
          const ret = checkSkuAvailable(sku)
          return ret.status === 0
        }).filter(sku => {
          if (filteredSpecs.length === 0) return true
          const { param, param1, param2, param3 } = sku
          const specs = ['_p0_' + param, '_p1_' + param1, '_p2_' + param2, '_p3_' + param3].filter(p => !!p)
          const intersectionedSpecs = intersection(specs, filteredSpecs)
          return intersectionedSpecs.length === filteredSpecs.length
        })
        
        // 提取有效的规格选项
        const validSpecs = skuList.map(sku => paramPrefix(paramName) + sku[paramName]).filter(spec => spec !== paramPrefix(paramName) + 'undefined')
        
        // 从禁用列表中移除有效的规格
        disabledSpecs = disabledSpecs.filter(spec => {
          return validSpecs.indexOf(spec) === -1
        })
      }
    }
    
    // 对每个规格组进行检查
    runner(specsListArr[0], 'param')
    runner(specsListArr[1], 'param1')
    runner(specsListArr[2], 'param2')
    runner(specsListArr[3], 'param3')
    
    return disabledSpecs
  }

  /**
   * 检查SKU可用性
   * 检查指定SKU的状态和库存情况
   * 
   * @param {Object} [sku] - 要检查的SKU对象，如果不提供则检查当前选中的SKU
   * @returns {Object} 检查结果
   * @returns {number} returns.status - 状态码：0-可用，1-无库存，2-已下架
   * @returns {string} [returns.err] - 错误信息（当status不为0时）
   */
  const checkSkuAvailable = (sku) => {
    const skuu = sku || querySku()
    if (skuu.state !== '1') return { status: 2, err: '该商品已下架，请选购其他商品~' }
    if (Number(skuu.stock) <= 0) return { status: 1, err: '所选地区暂时无货，非常抱歉！' }
    return { status: 0 }
  }

  /**
   * 检查SPU可用性（白名单用户限制）
   * 检查当前用户是否有权限购买该商品（针对白名单限制商品）
   * 
   * @returns {Promise<boolean>} 是否受到白名单限制
   */
  const checkSpuAvailable = async () => {
    if (spu.value.isCheckWhiteUser === '1') {
      if (userStore.isLogin) {
        const [err, json] = await isWhiteUserLimitCheck(goodsId)
        if (!err && !json) return true
      }
    }
    return false
  }

  /**
   * 检查规格选择是否完整
   * 判断用户是否已经选择了所有必需的规格
   * 
   * @returns {boolean} 规格选择是否完整
   */
  const isSpecsComplete = () => {
    const specsListArr = querySpecsList()
    const cur = queryCurSpecs()
    
    // 计算有规格选项的组数
    const count = specsListArr.reduce((a, b) => {
      if (b.length > 0) a++
      return a
    }, 0)
    
    return count === cur.length
  }

  /**
   * 工具函数：从规格列表中获取包含指定规格的组
   * 
   * @param {Array<Array<string>>} specsList - 规格列表
   * @param {string} spec - 指定的规格
   * @returns {Array<string>} 包含该规格的组
   */
  const getSpecsGroupFromSpec = (specsList, spec) => {
    return specsList.filter(group => group.indexOf(spec) >= 0)[0]
  }

  /**
   * 工具函数：根据规格选择筛选SKU列表
   * 
   * @param {Array<Object>} skuList - SKU列表
   * @param {Array<string>} specs - 规格选择数组
   * @returns {Array<Object>} 匹配的SKU列表
   */
  const getSkuListFromSpec = (skuList, specs) => {
    let list = skuList
    specs.forEach(spec => {
      list = list.filter(sku => {
        return ('_p0_' + sku.param === spec) || 
               ('_p1_' + sku.param1 === spec) || 
               ('_p2_' + sku.param2 === spec) || 
               ('_p3_' + sku.param3 === spec)
      })
    })
    return list
  }

  /**
   * 工具函数：获取参数名对应的前缀
   * 
   * @param {string} name - 参数名（param、param1、param2、param3）
   * @returns {string} 对应的前缀
   */
  const paramPrefix = (name) => {
    switch (name) {
      case 'param': return '_p0_'
      case 'param1': return '_p1_'
      case 'param2': return '_p2_'
      case 'param3': return '_p3_'
      default: return ''
    }
  }

  return {
    spu,
    curSpecs,
    curSkuId,
    loading,
    querySpu,
    querySku,
    querySkuCount,
    querySpecsList,
    queryCurSpecs,
    setSpecs,
    queryDisabledSpecs,
    checkSkuAvailable,
    checkSpuAvailable,
    isSpecsComplete
  }
}
