import { ref } from 'vue'
import { createGlobalState } from '@vueuse/core'

/**
 * 全局弹窗状态管理 Composable
 * 用于管理全局弹窗的显示状态，确保同一时间只有一个弹窗处于激活状态
 * 
 * @returns {Object} 返回弹窗管理相关的状态和方法
 * @returns {import('vue').Ref<string|null>} returns.activePopoverId - 当前激活的弹窗ID
 * @returns {Function} returns.setActivePopover - 设置激活弹窗的方法
 * @returns {Function} returns.closeAllPopovers - 关闭所有弹窗的方法
 * @returns {Function} returns.isPopoverActive - 检查指定弹窗是否激活的方法
 * 
 * @example
 * ```javascript
 * import { useGlobalPopover } from '@/composables'
 * 
 * const { activePopoverId, setActivePopover, closeAllPopovers, isPopoverActive } = useGlobalPopover()
 * 
 * // 激活指定弹窗
 * setActivePopover('popup-1')
 * 
 * // 检查弹窗是否激活
 * const isActive = isPopoverActive('popup-1')
 * 
 * // 关闭所有弹窗
 * closeAllPopovers()
 * ```
 */
export const useGlobalPopover = createGlobalState(() => {
  /** @type {import('vue').Ref<string|null>} 当前激活的弹窗ID */
  const activePopoverId = ref(null)

  /**
   * 设置当前激活的弹窗
   * @param {string|null} id - 弹窗ID，传入null表示关闭当前弹窗
   * @returns {void}
   */
  const setActivePopover = (id) => {
    activePopoverId.value = id
  }

  /**
   * 关闭所有弹窗
   * @returns {void}
   */
  const closeAllPopovers = () => {
    activePopoverId.value = null
  }

  /**
   * 检查指定弹窗是否处于激活状态
   * @param {string} id - 要检查的弹窗ID
   * @returns {boolean} 如果指定弹窗处于激活状态返回true，否则返回false
   */
  const isPopoverActive = (id) => {
    return activePopoverId.value === id
  }

  return {
    activePopoverId,
    setActivePopover,
    closeAllPopovers,
    isPopoverActive
  }
})
