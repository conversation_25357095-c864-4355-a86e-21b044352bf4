import compressImageFile from '@utils/imgZip.js'
import { ref } from 'vue'

/**
 * 图片压缩 Composable
 * 提供图片压缩功能，包括文件压缩、文件验证和上传前处理
 * 
 * @returns {Object} 图片压缩管理对象
 * @returns {Ref<boolean>} returns.loading - 压缩加载状态
 * @returns {Ref<Object|null>} returns.result - 压缩结果
 * @returns {Ref<Error|null>} returns.error - 错误信息
 * @returns {Function} returns.compress - 压缩图片文件
 * @returns {Function} returns.beforeRead - 文件上传前处理（包含压缩和验证）
 * 
 * @example
 * ```javascript
 * import { useImageCompressor } from '@/composables'
 * import { showToast, showLoadingToast, closeToast } from 'vant'
 * 
 * // 在组件中使用
 * const { loading, result, error, compress, beforeRead } = useImageCompressor()
 * 
 * // 直接压缩文件
 * const compressedFile = await compress(file, { quality: 0.8 })
 * 
 * // 在文件上传组件中使用
 * const handleBeforeRead = (file) => {
 *   return beforeRead(file, {
 *     maxSize: 5,
 *     showToast,
 *     showLoadingToast,
 *     closeToast
 *   })
 * }
 * ```
 */
export function useImageCompressor() {
  /**
   * 压缩状态管理
   */
  const loading = ref(false)
  const result = ref(null)
  const error = ref(null)

  /**
   * 压缩图片文件
   * 
   * @param {File} file - 要压缩的图片文件
   * @param {Object} [options={}] - 压缩选项
   * @param {number} [options.quality] - 压缩质量 (0-1)
   * @param {number} [options.maxWidth] - 最大宽度
   * @param {number} [options.maxHeight] - 最大高度
   * @returns {Promise<Object|null>} 压缩后的文件对象，失败时返回null
   */
  const compress = async (file, options = {}) => {
    loading.value = true
    error.value = null
    
    try {
      const res = await compressImageFile({ file, ...options })
      result.value = res
      loading.value = false
      return res
    } catch (err) {
      error.value = err
      loading.value = false
      return null
    }
  }

  /**
   * 文件上传前处理函数
   * 包含文件压缩、大小验证和格式验证
   * 
   * @param {File} file - 要处理的文件
   * @param {Object} [options={}] - 处理选项
   * @param {number} [options.maxSize=10] - 最大文件大小（MB）
   * @param {boolean} [options.showLoadingMessage=true] - 是否显示加载提示信息
   * @param {string} [options.loadingMessage='处理中...'] - 加载提示文本
   * @param {Function} options.showToast - 显示提示消息的函数
   * @param {Function} options.showLoadingToast - 显示加载提示的函数
   * @param {Function} options.closeToast - 关闭提示的函数
   * @returns {Promise<File|false>} 处理后的文件对象，验证失败时返回false
   * @throws {Error} 当必需的toast函数未提供时抛出错误
   */
  const beforeRead = async (file, options = {}) => {
    const {
      maxSize = 10,
      showLoadingMessage = true,
      loadingMessage = '处理中...',
      showToast,
      showLoadingToast,
      closeToast
    } = options

    // 验证必需的toast函数
    if (!showToast || !showLoadingToast || !closeToast) {
      throw new Error('showToast, showLoadingToast, and closeToast are required')
    }

    // 记录压缩前文件大小
    const fileSizeBefore = file.size / 1024 / 1024
    console.warn('压缩前文件大小:', fileSizeBefore.toFixed(2) + 'M')

    // 显示加载提示
    if (showLoadingMessage) {
      showLoadingToast({
        message: loadingMessage,
        forbidClick: true
      })
    } else {
      showLoadingToast()
    }

    // 执行图片压缩
    const startTime = new Date()
    const newFileObj = await compressImageFile({ file })
    const endTime = new Date()
    const executionTime = (endTime - startTime) / 1000
    console.log(`图片压缩执行时间：${executionTime}秒`)
    closeToast()

    const { file: newFile } = newFileObj
    
    // 记录压缩后文件大小
    const fileSizeAfter = newFile.size / 1024 / 1024
    console.warn('压缩后文件大小:', fileSizeAfter.toFixed(2) + 'M')

    // 验证压缩后文件大小
    if (fileSizeAfter > maxSize) {
      showToast(`图片大小不能超过 ${maxSize}M`)
      return false
    }

    // 验证文件类型
    const allowedTypes = ['image/jpeg', 'image/png', 'image/jpg']
    
    /**
     * 验证单个文件的类型
     * 
     * @param {File} fileToValidate - 要验证的文件
     * @returns {boolean} 文件类型是否有效
     */
    const validateFileType = (fileToValidate) => {
      if (!allowedTypes.includes(fileToValidate.type)) {
        showToast('请选择正确图片格式上传')
        return false
      }
      return true
    }

    // 处理文件数组或单个文件
    if (newFile instanceof Array && newFile.length) {
      if (!validateFileType(newFile[0])) {
        return false
      }
      return newFile
    } else {
      if (!validateFileType(newFile)) {
        return false
      }
      return newFile
    }
  }

  return {
    // 状态
    loading,
    result,
    error,
    
    // 方法
    compress,
    beforeRead
  }
}
