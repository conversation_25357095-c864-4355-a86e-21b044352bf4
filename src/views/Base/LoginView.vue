<!--
  登录页面组件
  功能：提供用户登录入口，集成通用登录组件
  特性：支持本地开发环境和生产环境的区分，可配置登录类型
-->
<template>
  <!-- 登录页面容器 -->
  <LoginComponent
    :isLocal="isLocal"
    :options="loginOptions"
  />
</template>

<script setup>
import { ref, computed } from 'vue'
import { LoginComponent } from 'commonkit-login-vue3'
import { loginType } from '@utils/storage.js'

// 环境判断：检测是否为本地开发环境
const isLocal = ref(import.meta.env.VITE_ENV === 'local')

// 登录配置选项：动态生成登录组件所需的配置参数
const loginOptions = computed(() => ({
  // 启用 history.replaceState 导航模式，防止登录后用户通过浏览器回退按钮返回登录页
  useHistoryReplace: true,
  // 登录类型：从本地存储获取，默认为 '0'
  loginType: loginType.get() || '0'
}))
</script>
