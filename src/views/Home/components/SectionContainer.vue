<!--
  区域容器组件
  功能：为首页内容区域提供统一的标题头部和内容容器
  特性：支持标题显示、更多按钮、点击事件、插槽内容
  用途：包装首页各个功能区域，提供一致的视觉样式
-->
<template>
  <!-- 首页区域容器 -->
  <section class="home-block">
    <!-- 区域头部 -->
    <div class="header">
      <!-- 区域标题 -->
      <h2 class="title">{{ title }}</h2>
      <!-- 更多按钮 -->
      <p
        class="more"
        v-if="more"
        @click="handleMoreClick"
      >
        <span>{{ more }}</span>
        <span class="icon-arrow" />
      </p>
    </div>
    <!-- 区域内容 -->
    <div class="content">
      <slot />
    </div>
  </section>
</template>

<script setup>
import { toRefs } from 'vue'

// 组件属性定义
const props = defineProps({
  // 区域标题文本
  title: {
    type: String,
    required: true
  },
  // 更多按钮文本
  more: {
    type: String,
    default: ''
  }
})

// 事件定义
const emit = defineEmits(['click'])

// Props 解构：使用 toRefs 保持响应性
const { title, more } = toRefs(props)

// 更多按钮点击处理
const handleMoreClick = () => {
  emit('click')
}
</script>

<style lang="less" scoped>
.home-block {
    margin: 10px 0;
    //background: #FFFFFF;
    overflow: hidden;
    transition: box-shadow 0.3s ease;

    .header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 10px 20px 10px;

        .title {
            font-size: 18px;
            font-weight: 600;
            color: #171E24;
            line-height: 1.4;
            margin: 0;
            position: relative;

            &::before {
                content: '';
                position: absolute;
                left: -12px;
                top: 50%;
                transform: translateY(-50%);
                width: 4px;
                height: 16px;
                background: linear-gradient(135deg, #EF4444, #F97316);
                border-radius: 2px;
            }
        }

        .more {
            display: flex;
            align-items: center;
            font-size: 14px;
            font-weight: 400;
            color: #4A5568;
            cursor: pointer;
            padding: 6px 12px;
            border-radius: 20px;
            background: #F8F9FA;
            border: 1px solid transparent;
            transition: all 0.3s ease;

            &:hover {
                color: #EF4444;
                background: #FFFFFF;
                border-color: #EF4444;
                transform: translateY(-1px);
            }

            .icon-arrow {
                margin-left: 6px;
                width: 12px;
                height: 12px;
                background-image: url('assets/arrow.png');
                background-repeat: no-repeat;
                background-size: contain;
                background-position: center;
                transition: transform 0.3s ease;
                opacity: 0.7;
            }

            &:hover .icon-arrow {
                transform: translateX(3px);
                opacity: 1;
            }
        }
    }

    .content {
        //padding: 10px;
        //background: #FFFFFF;
    }
}
</style>
