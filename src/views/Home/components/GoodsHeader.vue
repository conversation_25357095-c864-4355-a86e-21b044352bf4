<!--
  商品分类头部组件
  功能：展示商品分类标签页，支持横向滚动和分类切换
  特性：支持横向滚动、自定义滚动条、活动状态指示、分类切换
  用途：商品列表页面的分类导航，支持多个分类的快速切换
-->
<template>
  <!-- 商品分类头部容器 -->
  <div
    class="goods-header"
    :style="{ paddingBottom: showScrollbar ? '10px' : '0' }"
  >
    <!-- 分类标签容器 -->
    <div
      ref="containerRef"
      class="goods-header__container"
      @scroll="handleScroll"
    >
      <!-- 分类标签项 -->
      <div
        ref="typeRefs"
        v-for="(item, index) in typeList"
        :key="index"
        class="goods-header__item"
        :class="{ 'goods-header__item--active': activeIndex === index }"
        @click="handleCategorySelect(index)"
      >
        {{ item.name }}
      </div>
    </div>
    <!-- 自定义滚动条 -->
    <div
      v-show="showScrollbar"
      class="goods-header__scrollbar"
    >
      <div
        class="goods-header__scrollbar-thumb"
        :style="{ transform: `translateX(${scrollbarPosition}px)` }"
      ></div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, nextTick } from 'vue'

// 组件属性定义
const props = defineProps({
  // 分类类型列表
  typeList: {
    type: Array,
    default: () => []
  }
})

// 事件定义
const emit = defineEmits(['switchTabs'])

// 组件状态管理
const typeRefs = ref([])
const activeIndex = ref(0)
const containerRef = ref(null)
const scrollbarPosition = ref(0)
const showScrollbar = ref(false)

// 分类选择处理：切换活动分类并触发事件
const handleCategorySelect = (index) => {
  activeIndex.value = index
  emit('switchTabs', props.typeList[index].id)
}

// 滚动事件处理：计算自定义滚动条位置
const handleScroll = () => {
  if (!containerRef.value) return

  const container = containerRef.value
  const scrollLeft = container.scrollLeft
  const scrollWidth = container.scrollWidth
  const clientWidth = container.clientWidth

  // 内容宽度小于等于容器宽度时，隐藏滚动条
  if (scrollWidth <= clientWidth) {
    showScrollbar.value = false
    scrollbarPosition.value = 0
    return
  }

  showScrollbar.value = true

  // 滚动条配置参数
  const trackWidth = 60      // 滚动条轨道宽度
  const thumbWidth = 20      // 滚动条滑块宽度
  const scrollableThumbDistance = trackWidth - thumbWidth  // 可滚动距离

  // 计算滚动比例
  const scrollRatio = scrollLeft / (scrollWidth - clientWidth)

  // 计算滚动条滑块位置
  scrollbarPosition.value = scrollRatio * scrollableThumbDistance
}

// 组件挂载时初始化：设置默认活动分类并检查滚动条显示
onMounted(() => {
  nextTick(() => {
    if (props.typeList.length > 0) {
      activeIndex.value = 0
    }
    // 初始化时检查是否需要显示滚动条
    handleScroll()
  })
})
</script>

<style lang="less" scoped>
.goods-header {
  width: 100vw;
  position: relative;
  overflow-x: hidden;
  z-index: 98;

  &__container {
    font-size: 14px;
    height: 25px;
    margin-top: 10px;
    display: flex;
    overflow-x: scroll;
    -webkit-overflow-scrolling: touch; scrollbar-width: none; -ms-overflow-style: none; &::-webkit-scrollbar { display: none; };
  }

  &__item {
    white-space: nowrap;
    height: 14px;
    line-height: 14px;
    padding: 0 17px;
    transition: 0.3s;
    color: #4A5568;
    cursor: pointer;

    &:not(:last-child) {
      border-right: 1px solid #E2E8EE;
    }

    &--active {
      position: relative;
      z-index: 2;
      color: #171E24;

      &::after {
        content: "";
        display: block;
        position: absolute;
        left: 50%;
        bottom: -1px;
        width: 56px;
        height: 6px;
        border-radius: 4px;
        z-index: -1;
        transform: translateX(-50%);
        background: var(--wo-biz-theme-gradient-1);
        transition: 0.3s;
      }
    }
  }

  // 自定义滚动条样式
  &__scrollbar {
    position: absolute;
    bottom: 5px;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 3px;
    background-color: rgba(255, 122, 10, 0.2);
    border-radius: 2px;
    overflow: hidden;
  }

  &__scrollbar-thumb {
    width: 20px;
    height: 3px;
    background-color: var(--wo-biz-theme-color);
    border-radius: 2px;
    transition: transform 0.1s ease;
  }
}
</style>
