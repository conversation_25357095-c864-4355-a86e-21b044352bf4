<template>
  <div class="special-goods-skeleton">
    <!-- 热区骨架屏 -->
    <div class="skeleton-hot-zone">
      <div class="skeleton-hot-zone-content"></div>
    </div>

    <!-- 商品滚动区域骨架屏 -->
    <div class="skeleton-scroll-wrapper">
      <div v-for="i in skeletonCount" :key="i" class="skeleton-item">
        <div class="skeleton-image"></div>
        <div class="skeleton-content">
          <div class="skeleton-title"></div>
          <div class="skeleton-title short"></div>
          <div class="skeleton-details">
            <div class="skeleton-price"></div>
            <div class="skeleton-sales"></div>
          </div>
          <div class="skeleton-spec"></div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
const props = defineProps({
  skeletonCount: {
    type: Number,
    default: 5
  }
})
</script>

<style scoped lang="less">

@keyframes skeleton-loading {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

.skeleton-base {
  background: linear-gradient(90deg, #f5f5f5 25%, #e8e8e8 50%, #f5f5f5 75%);
  background-size: 200px 100%;
  animation: skeleton-loading 1.2s ease-in-out infinite;
  border-radius: 4px;
}

.special-goods-skeleton {

  position: relative;
  border-radius: 12px;
  margin: 8px 12px;
  overflow: hidden;


  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200px 100%;
  animation: skeleton-loading 1.5s ease-in-out infinite;


  min-height: 230px;
  display: flex;
  flex-direction: column;

  .skeleton-hot-zone {
    flex: 1;
    min-height: 40px;
    padding: 12px 12px 0 12px;

    .skeleton-hot-zone-content {
      background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%); background-size: 200% 100%; animation: skeleton-loading 1.5s infinite;;
      width: 100%;
      height: 100%;
      min-height: 40px;
      border-radius: 8px;
      background: linear-gradient(90deg, rgba(255, 255, 255, 0.3) 25%, rgba(255, 255, 255, 0.5) 50%, rgba(255, 255, 255, 0.3) 75%);
    }
  }

  .skeleton-scroll-wrapper {
    display: flex;
    gap: 12px;
    overflow-x: auto;
    padding: 12px;
    scroll-behavior: smooth;
    width: 100%;
    -webkit-overflow-scrolling: touch; scrollbar-width: none; -ms-overflow-style: none; &::-webkit-scrollbar { display: none; };

    .skeleton-item {
      flex: 0 0 130px;
      background: #FFFFFF;
      border-radius: 6px;
      overflow: hidden;
      cursor: pointer;

      height: 200px;


      &:last-child {
        margin-right: 4px12;
      }

      .skeleton-image {
        background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%); background-size: 200% 100%; animation: skeleton-loading 1.5s infinite;;
        width: 100%;
        height: 120px;
        border-radius: 6px 6px 0 0;
        background-color: #F8F9FA;
      }

      .skeleton-content {

        padding: 8px;

        .skeleton-title {
          background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%); background-size: 200% 100%; animation: skeleton-loading 1.5s infinite;;

          height: 12px;
          margin-bottom: 6px;
          width: 100%;

          &.short {
            width: 70%;
            margin-bottom: 4px;
          }
        }

        .skeleton-details {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 4px;
          gap: 8px;

          .skeleton-price {
            background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%); background-size: 200% 100%; animation: skeleton-loading 1.5s infinite;;

            height: 14px;
            width: 50px;
            flex-shrink: 0;
          }

          .skeleton-sales {
            background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%); background-size: 200% 100%; animation: skeleton-loading 1.5s infinite;;

            height: 11px;
            width: 40px;
            border-radius: 2px;
          }
        }

        .skeleton-spec {
          background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%); background-size: 200% 100%; animation: skeleton-loading 1.5s infinite;;

          height: 11px;
          width: 75%;
          border-radius: 2px;
        }
      }
    }
  }
}


@media (max-width: 375px) {
  .special-goods-skeleton {
    .skeleton-hot-zone {
      padding: 8px 8px 0 8px;

      .skeleton-hot-zone-content {
        min-height: 30px;
      }
    }

    .skeleton-scroll-wrapper {
      gap: 8px;
      padding: 8px;

      .skeleton-item {
        flex: 0 0 120px;
        height: 190px;

        &:last-child {
          margin-right: 8px;
        }

        .skeleton-image {
          height: 110px;
        }

        .skeleton-content {
          padding: 6px;

          .skeleton-title {
            height: 11px;
            margin-bottom: 4px;

            &.short {
              margin-bottom: 2px;
            }
          }

          .skeleton-details {
            margin-bottom: 2px;
            gap: 6px;

            .skeleton-price {
              height: 13px;
              width: 45px;
            }

            .skeleton-sales {
              height: 10px;
              width: 35px;
            }
          }

          .skeleton-spec {
            height: 10px;
          }
        }
      }
    }
  }
}
</style>
