<template>
  <div class="third-banner-skeleton">
    <div class="skeleton-third-banner">
      <div class="skeleton-left">
        <div class="skeleton-image"></div>
      </div>
      <div class="skeleton-right">
        <div class="skeleton-image"></div>
      </div>
    </div>
  </div>
</template>

<script setup>
</script>

<style scoped lang="less">

@keyframes skeleton-loading {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

.skeleton-base {
  background: linear-gradient(90deg, #f5f5f5 25%, #e8e8e8 50%, #f5f5f5 75%);
  background-size: 200px 100%;
  animation: skeleton-loading 1.2s ease-in-out infinite;
  border-radius: 4px;
}

.third-banner-skeleton {
  margin: 8px 0;
  .skeleton-third-banner {
    display: flex;
    height: 210px;
    gap: 8px;

    .skeleton-left,
    .skeleton-right {
      display: flex;
      width: 50%;
      background: #ffffff;
      border-radius: 12px;
      overflow: hidden;

      .skeleton-image {
        background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%); background-size: 200% 100%; animation: skeleton-loading 1.5s infinite;;
        width: 100%;
        height: 100%;
        border-radius: 12px;
        background-color: #f8f9fa;
      }
    }
  }
}


@media (max-width: 375px) {
  .third-banner-skeleton {
    margin: 8px 0;
    .skeleton-third-banner {
      height: 180px;
      gap: 6px;
    }
  }
}
</style>
