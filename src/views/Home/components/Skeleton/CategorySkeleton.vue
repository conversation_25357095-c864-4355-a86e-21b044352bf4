<template>
  <div class="category-skeleton">
    <div v-for="i in 3" :key="i" class="skeleton-category-item">
      <div class="skeleton-category-header">
        <div class="skeleton-title"></div>
      </div>

      <div class="skeleton-category-content">
        <div class="skeleton-subcategory-grid">
          <div v-for="j in 6" :key="j" class="skeleton-subcategory-item">
            <div class="skeleton-image"></div>
            <div class="skeleton-name"></div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
</script>

<style scoped lang="less">

@keyframes skeleton-loading {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

.skeleton-base {
  background: linear-gradient(90deg, #f5f5f5 25%, #e8e8e8 50%, #f5f5f5 75%);
  background-size: 200px 100%;
  animation: skeleton-loading 1.2s ease-in-out infinite;
  border-radius: 4px;
}

.category-skeleton {
  display: flex;
  flex-direction: column;
  gap: 8px;
  padding: 8px 5px;
}

.skeleton-category-item {
  background-color: #FFFFFF;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

.skeleton-category-header {
  padding: 15px 20px 16px;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(255, 255, 255, 0.7) 100%);

  .skeleton-title {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%); background-size: 200% 100%; animation: skeleton-loading 1.5s infinite;;
    width: 120px;
    height: 18px;
    border-radius: 4px;
  }
}

.skeleton-category-content {
  padding: 0 20px 20px;
}

.skeleton-subcategory-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16px 20px;

  @media (max-width: 375px) {
    gap: 12px 16px;
    padding: 0 8px;
  }
}

.skeleton-subcategory-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  padding: 8px;

  .skeleton-image {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%); background-size: 200% 100%; animation: skeleton-loading 1.5s infinite;;
    width: 68px;
    height: 68px;
    border-radius: 8px;
    margin-bottom: 8px;
  }

  .skeleton-name {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%); background-size: 200% 100%; animation: skeleton-loading 1.5s infinite;;
    width: 60px;
    height: 14px;
    border-radius: 2px;
  }
}


@media (max-width: 375px) {
  .skeleton-category-header {
    padding: 12px 16px;

    .skeleton-title {
      width: 100px;
      height: 16px;
    }
  }

  .skeleton-category-content {
    padding: 0 16px 16px;
  }

  .skeleton-subcategory-item {
    padding: 6px;

    .skeleton-image {
      width: 56px;
      height: 56px;
      margin-bottom: 6px;
    }

    .skeleton-name {
      width: 50px;
      height: 12px;
    }
  }
}
</style>
