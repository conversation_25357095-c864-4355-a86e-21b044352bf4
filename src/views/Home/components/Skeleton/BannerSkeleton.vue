<!--
  轮播图骨架屏组件
  功能：在轮播图数据加载时显示占位符动画
  特性：流光动画效果、响应式布局、圆角设计
  用途：提升用户体验，避免内容加载时的空白状态
-->
<template>
  <!-- 轮播图骨架屏容器 -->
  <div class="banner-skeleton">
    <!-- 骨架屏轮播图 -->
    <div class="skeleton-banner">
      <!-- 骨架屏图片区域 -->
      <div class="skeleton-image"></div>
    </div>
  </div>
</template>

<script setup>
// 轮播图骨架屏组件：无需额外逻辑，纯展示组件
</script>

<style scoped lang="less">

@keyframes skeleton-loading {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

.skeleton-base {
  background: linear-gradient(90deg, #f5f5f5 25%, #e8e8e8 50%, #f5f5f5 75%);
  background-size: 200px 100%;
  animation: skeleton-loading 1.2s ease-in-out infinite;
  border-radius: 4px;
}

.banner-skeleton {
  border-radius: 12px;
  overflow: hidden;

  .skeleton-banner {
    position: relative;
    width: 100%;
    height: 120px;
    background: #ffffff;
    border-radius: 12px;

    .skeleton-image {
      background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%); background-size: 200% 100%; animation: skeleton-loading 1.5s infinite;;
      width: 100%;
      height: 100%;
      border-radius: 12px;
      background-color: #f8f9fa;
    }


    .skeleton-pagination {
      position: absolute;
      bottom: 12px;
      right: 12px;
      background: rgba(0, 0, 0, 0.6);
      padding: 6px 12px;
      border-radius: 16px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
      backdrop-filter: blur(8px);
      border: 1px solid rgba(255, 255, 255, 0.1);

      .skeleton-fraction {
        background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%); background-size: 200% 100%; animation: skeleton-loading 1.5s infinite;;
        width: 24px;
        height: 13px;
        border-radius: 2px;
        background: rgba(255, 255, 255, 0.8);
        animation: none;
      }
    }
  }
}
</style>
