<!--
  商品卡片组件
  功能：展示商品基本信息的卡片，支持不同业务类型的价格显示
  特性：支持图片懒加载、价格区间显示、销量展示、规格信息、响应式布局
  用途：商品列表、推荐区域等场景的商品展示
-->
<template>
  <!-- 商品卡片容器 -->
  <div class="goods-card">
    <!-- 商品图片区域 -->
    <div class="goods-image">
      <img
        :src="goodsInfo.image"
        :alt="goodsInfo.name"
        loading="lazy"
        decoding="async"
        @load="handleImageLoad"
      />
    </div>
    <!-- 商品信息区域 -->
    <div class="goods-info">
      <!-- 商品名称 -->
      <div class="goods-name">{{ goodsInfo.name }}</div>
      <!-- 商品规格 -->
      <div
        class="goods-spec"
        v-if="goodsInfo.spec"
      >
        {{ goodsInfo.spec }}
      </div>
      <!-- 商品详情（价格和销量） -->
      <div class="goods-details">
        <!-- 政企商城价格区间显示 -->
        <template v-if="isZQBusiness && (goodsInfo.highPrice || goodsInfo.lowPrice)">
          <PriceDisplay
            :high-price="goodsInfo.highPrice"
            :low-price="goodsInfo.lowPrice"
            range-label=""
            size="small"
            color="orange"
          />
        </template>
        <!-- 普通商城单价显示 -->
        <template v-else>
          <PriceDisplay
            :price="goodsInfo.price"
            size="small"
            color="orange"
          />
        </template>
        <!-- 销量信息（非政企商城显示） -->
        <span
          class="goods-sales"
          v-if="goodsInfo.sales > 0 && !isZQBusiness"
        >
          销量: {{ goodsInfo.sales }}
        </span>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed, toRefs } from 'vue'

// 组件导入
import PriceDisplay from '@components/Common/PriceDisplay.vue'

// 工具函数导入
import { getBizCode } from '@/utils/curEnv'

// 组件属性定义
const props = defineProps({
  // 商品信息对象
  goodsInfo: {
    type: Object,
    required: true,
    default: () => ({
      image: '',        // 商品图片URL
      name: '',         // 商品名称
      price: 0,         // 商品价格
      sales: 0,         // 销量
      spec: '',         // 商品规格
      lowPrice: '',     // 最低价格（政企商城）
      highPrice: ''     // 最高价格（政企商城）
    })
  }
})

// 事件定义
const emit = defineEmits(['image-loaded'])

// Props 解构：使用 toRefs 保持响应性
const { goodsInfo } = toRefs(props)

// 业务类型判断：检测是否为政企商城业务
const isZQBusiness = computed(() => getBizCode() === 'zq')

// 图片加载完成处理：通知父组件图片已加载
const handleImageLoad = () => {
  emit('image-loaded')
}
</script>

<style scoped lang="less">
.goods-card {
  background: #FFFFFF;
  border-radius: 8px;
  overflow: hidden;
  transition: all 0.2s ease;
  cursor: pointer;
  position: relative;
  width: 100%;
  max-width: 220px;
  min-width: 135px;
  min-height: 160px;
}

.goods-image {
  position: relative;
  width: 100%;
  overflow: hidden;
  background: #FFFFFF;
  flex-shrink: 0;

  img {
    width: 100%;
    height: auto;
    display: block;
    transition: transform 0.3s ease;
    background-color: #F8F9FA;
  }

  &:hover img {
    transform: scale(1.05);
  }
}

.goods-info {
  padding: 10px;
  background: #FFFFFF;

  .goods-name {
    font-size: 12px;
    font-weight: 400;
    color: #171E24;
    margin: 0 0 8px 0;
    line-height: 1.4;
    display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      overflow: hidden;
      word-break: break-all;
    text-decoration: none;
  }

  .goods-details {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 2px 6px;
    margin-bottom: 6px;

    .goods-price {
      color: var(--wo-biz-theme-color);
      font-size: 14px;
      font-weight: 700;
      line-height: 1;

      &::before {
        content: '¥';
        font-size: 12px;
        font-weight: 400;
        margin-right: 1px;
      }
    }

    .goods-price-range {
      display: flex;
      align-items: baseline;
      color: var(--wo-biz-theme-color);
      font-weight: 700;
      flex: 1 1 auto;
      min-width: 0;

      .goods-price-number {
        font-size: 13px;
        position: relative;
        padding-left: 8px;

        &::before {
          content: '¥';
          position: absolute;
          left: 0;
          top: 0;
          font-size: 11px;
          font-weight: 400;
        }
      }

      .goods-price-separator {
        margin: 0 2px;
        color: #718096;
        font-weight: 400;
      }
    }

    .goods-sales {
      color: #718096;
      font-size: 11px;
      line-height: 1;
      white-space: nowrap;
      flex: 0 0 auto;
    }
  }

  .goods-spec {
    color: #4A5568;
    font-size: 12px;
    line-height: 1.3;
    background: #F8F9FA;
    padding: 4px 8px;
    border-radius: 4px;
    margin-top: 4px;
    display: inline-block;
    max-width: 100%;
    overflow: hidden; text-overflow: ellipsis; white-space: nowrap;;
  }
}
</style>
