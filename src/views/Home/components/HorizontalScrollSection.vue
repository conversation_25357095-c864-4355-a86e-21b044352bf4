<!--
  横向滚动商品区域组件
  功能：展示横向滚动的商品列表，支持背景图片、热区点击、不同卡片类型
  特性：支持骨架屏加载、背景图片设置、热区交互、卡片类型切换、平滑滚动
  用途：首页特殊商品推荐、横向商品展示区域
-->
<template>
  <!-- 横向滚动容器 -->
  <div
    v-if="skeletonStates.horizontal || goodsList.length > 0"
    class="home-horizontal-scroll-container"
    :style="containerBackgroundStyle"
  >
    <transition name="skeleton-fade" mode="out-in">
      <!-- 横向滚动骨架屏 -->
      <HorizontalScrollSkeleton
        v-if="skeletonStates.horizontal"
        :skeleton-count="5"
        key="horizontal-skeleton"
      />
      <!-- 横向滚动内容 -->
      <div
        v-else-if="goodsList.length > 0"
        key="horizontal-content"
        class="home-horizontal-scroll-content"
      >
        <!-- 热区点击区域 -->
        <div
          v-if="showHotZone"
          class="home-hot-zone"
          @click="handleHotZoneClick"
        >
          <!-- 热区内容可以根据需要添加 -->
        </div>

        <!-- 商品横向滚动区域 -->
        <div class="home-horizontal-scroll-wrapper">
          <!-- 商品项 -->
          <div
            class="home-goods-item"
            v-for="item in goodsList"
            :key="item.goodsId"
            @click="handleGoodsClick(item)"
          >
            <!-- 动态商品卡片组件 -->
            <component
              :is="dynamicCardComponent"
              :goods-info="item"
              @click="handleGoodsClick(item)"
            />
          </div>
        </div>
      </div>
    </transition>
  </div>
</template>

<script setup>
import { computed, toRefs } from 'vue'

// 组件导入
import HorizontalScrollSkeleton from '@views/Home/components/Skeleton/HorizontalScrollSkeleton.vue'
import ProductCard from '@views/Home/components/ProductCard.vue'
import ProductCardMini from '@views/Home/components/ProductCardMini.vue'

// 组件属性定义
const props = defineProps({
  // 商品列表数据
  goodsList: {
    type: Array,
    default: () => []
  },
  // 背景图片URL
  backgroundImage: {
    type: String,
    default: ''
  },
  // 是否显示热区点击区域
  showHotZone: {
    type: Boolean,
    default: false
  },
  // 商品卡片类型：normal（普通）、mini（迷你）
  cardType: {
    type: String,
    default: 'normal',
    validator: (value) => ['normal', 'mini'].includes(value)
  },
  // 骨架屏状态配置
  skeletonStates: {
    type: Object,
    default: () => ({
      horizontal: true
    })
  }
})

// 事件定义
const emit = defineEmits(['goods-click', 'hot-zone-click'])

// Props 解构：使用 toRefs 保持响应性
const { goodsList, backgroundImage, showHotZone, cardType, skeletonStates } = toRefs(props)

// 容器背景样式：根据背景图片动态设置样式
const containerBackgroundStyle = computed(() => {
  return backgroundImage.value
    ? {
        backgroundImage: `url(${backgroundImage.value})`,
        backgroundSize: '100% 100%',
        backgroundPosition: 'center',
        backgroundRepeat: 'no-repeat'
      }
    : {}
})

// 动态卡片组件：根据卡片类型选择对应的组件
const dynamicCardComponent = computed(() => {
  return cardType.value === 'mini' ? ProductCardMini : ProductCard
})

// 商品点击处理：触发商品点击事件
const handleGoodsClick = (goodsInfo) => {
  emit('goods-click', goodsInfo)
}

// 热区点击处理：触发热区点击事件
const handleHotZoneClick = () => {
  emit('hot-zone-click')
}
</script>

<style scoped lang="less">
.home-horizontal-scroll-container {
  position: relative;
  min-height: 180px;
  display: flex;
  align-items: center;
  border-radius: 12px;
  margin: 8px 12px;
  overflow: hidden;
  box-sizing: border-box;

  .home-horizontal-scroll-content {
    height: 100%;
    display: flex;
    flex-direction: column;
    width: 100%;
    box-sizing: border-box;
  }

  .home-hot-zone {
    flex: 1;
    min-height: 40px;
    cursor: pointer;
    box-sizing: border-box;

    &:hover {
      opacity: 0.9;
    }
  }

  .home-horizontal-scroll-wrapper {
    display: flex;
    gap: 12px;
    overflow-x: auto;
    scroll-behavior: smooth;
    width: 100%;
    box-sizing: border-box;
    -webkit-overflow-scrolling: touch; scrollbar-width: none; -ms-overflow-style: none; &::-webkit-scrollbar { display: none; };

    .home-goods-item {
      flex: 0 0 130px;
      cursor: pointer;
      box-sizing: border-box;

      &:last-child {
        margin-right: 12px;
      }
    }
  }
}

.skeleton-fade-enter-active,
.skeleton-fade-leave-active {
  transition: opacity 0.3s ease;
}

.skeleton-fade-enter-from,
.skeleton-fade-leave-to {
  opacity: 0;
}

.skeleton-fade-enter-to,
.skeleton-fade-leave-from {
  opacity: 1;
}
</style>
