<!--
  首页基础布局组件
  功能：提供首页通用布局结构，包括搜索头部、轮播图、菜单网格等
  特性：支持模块化显示控制、骨架屏加载、过渡动画
  插槽：additional-content（额外内容）、main-content（主要内容）
-->
<template>
  <!-- 首页布局容器 -->
  <div class="home-layout" :class="homeClass">
    <!-- 搜索头部 -->
    <SearchHeader
      v-if="showModules.search !== false"
      v-model="searchKeyword"
      :placeholder="searchPlaceholder"
      :redirect-to-search="true"
      redirect-url="/search"
      @search="handleSearch"
    />

    <!-- 轮播图区域 -->
    <div
      v-if="showModules.banner !== false && (skeletonStates.banner || headerBannerList.length > 0)"
      class="home-banner-container"
    >
      <transition name="skeleton-fade" mode="out-in">
        <!-- 轮播图骨架屏 -->
        <BannerSkeleton
          v-if="skeletonStates.banner"
          key="banner-skeleton"
        />
        <!-- 轮播图内容 -->
        <GoodsSwiper
          v-else-if="headerBannerList.length > 0"
          key="banner-content"
          :imageList="headerBannerList"
          mode="landscape"
          paginationType="fraction"
          :autoplay="true"
          :loop="true"
          @image-click="handleBannerClick"
        />
      </transition>
    </div>

    <!-- 菜单网格区域 -->
    <div
      v-if="showModules.gridMenu !== false && (skeletonStates.gridMenu || gridMenuItems.length > 0)"
      class="home-grid-menu-container"
    >
      <transition name="skeleton-fade" mode="out-in">
        <!-- 菜单网格骨架屏 -->
        <GridMenuSkeleton
          v-if="skeletonStates.gridMenu"
          key="grid-skeleton"
        />
        <!-- 菜单网格内容 -->
        <IconGrid
          v-else-if="gridMenuItems.length > 0"
          key="grid-content"
          :items="gridMenuItems"
          :columns="gridColumns"
          :display-mode="gridDisplayMode"
          :show-more="true"
          :max-items="10"
          @item-click="handleGridItemClick"
          @more-click="handleMoreClick"
        />
      </transition>
    </div>

    <!-- 额外内容插槽 -->
    <slot name="additional-content" />

    <!-- 主要内容插槽 -->
    <slot name="main-content" />
  </div>
</template>

<script setup>
import { ref, toRefs } from 'vue'
import { debounce } from 'lodash-es'

// 组件导入
import SearchHeader from '@components/Common/SearchHeader.vue'
import IconGrid from '@views/Home/components/IconGrid.vue'
import BannerSkeleton from '@views/Home/components/Skeleton/BannerSkeleton.vue'
import GridMenuSkeleton from '@views/Home/components/Skeleton/GridMenuSkeleton.vue'
import GoodsSwiper from "@components/Common/GoodsSwiper.vue"

// 组件属性定义
const props = defineProps({
  // 首页样式类名
  homeClass: {
    type: String,
    default: ''
  },
  // 搜索框占位符文本
  searchPlaceholder: {
    type: String,
    default: '搜索商品'
  },
  // 头部轮播图数据列表
  headerBannerList: {
    type: Array,
    default: () => []
  },
  // 菜单网格项数据列表
  gridMenuItems: {
    type: Array,
    default: () => []
  },
  // 菜单网格列数
  gridColumns: {
    type: Number,
    default: 5
  },
  // 菜单网格显示模式
  gridDisplayMode: {
    type: String,
    default: 'grid'
  },
  // 模块显示控制配置
  showModules: {
    type: Object,
    default: () => ({
      search: true,
      banner: true,
      gridMenu: true
    })
  },
  // 骨架屏状态配置
  skeletonStates: {
    type: Object,
    default: () => ({
      banner: true,
      gridMenu: true
    })
  }
})

// 事件定义
const emit = defineEmits(['search', 'banner-click', 'grid-item-click', 'more-click'])

// Props 解构：使用 toRefs 保持响应性
const {
  homeClass,
  searchPlaceholder,
  headerBannerList,
  gridMenuItems,
  gridColumns,
  gridDisplayMode,
  showModules,
  skeletonStates
} = toRefs(props)

// 搜索关键词状态
const searchKeyword = ref('')

// 搜索处理：使用防抖避免频繁触发搜索
const handleSearch = debounce(() => {
  emit('search', searchKeyword.value)
}, 300)

// 轮播图点击处理
const handleBannerClick = ({ item }) => {
  emit('banner-click', { item })
}

// 菜单网格项点击处理
const handleGridItemClick = ({ item }) => {
  emit('grid-item-click', { item })
}

// 更多按钮点击处理
const handleMoreClick = () => {
  emit('more-click')
}
</script>

<style scoped lang="less">
.home-layout {
  width: 100vw;
  height: 100%;
  overflow: auto;
  background: #F8F9FA;
  box-sizing: border-box;

  .home-banner-container {
    margin: 8px 0;
    padding: 0 10px;
    border-radius: 12px;
    overflow: hidden;
    box-sizing: border-box;
  }

  .home-grid-menu-container {
    border-radius: 12px;
    margin: 8px 12px;
    box-sizing: border-box;
  }
}

.skeleton-fade-enter-active,
.skeleton-fade-leave-active {
  transition: opacity 0.3s ease;
}

.skeleton-fade-enter-from,
.skeleton-fade-leave-to {
  opacity: 0;
}

.skeleton-fade-enter-to,
.skeleton-fade-leave-from {
  opacity: 1;
}
</style>
