<!--
  迷你商品卡片组件
  功能：展示商品基本信息的紧凑型卡片，适用于横向滚动等空间受限场景
  特性：支持图片懒加载、价格区间显示、销量展示、紧凑布局、响应式设计
  用途：横向滚动商品列表、特殊商品推荐等空间受限的商品展示场景
-->
<template>
  <!-- 迷你商品卡片容器 -->
  <div class="goods-card-mini">
    <!-- 商品图片区域 -->
    <div class="goods-image">
      <img
        :src="goodsInfo.image"
        :alt="goodsInfo.name"
        loading="lazy"
        decoding="async"
      />
    </div>

    <!-- 商品信息区域 -->
    <div class="goods-info">
      <!-- 商品名称 -->
      <div class="goods-name">{{ goodsInfo.name }}</div>
      <!-- 商品详情（价格和销量） -->
      <div class="goods-details">
        <!-- 政企商城价格区间显示 -->
        <template v-if="isZQBusiness && (goodsInfo.highPrice || goodsInfo.lowPrice)">
          <PriceDisplay
            :high-price="goodsInfo.highPrice"
            :low-price="goodsInfo.lowPrice"
            range-label=""
            size="small"
            color="orange"
          />
        </template>
        <!-- 普通商城单价显示 -->
        <template v-else>
          <PriceDisplay
            :price="goodsInfo.price"
            size="small"
            color="orange"
          />
        </template>
        <!-- 销量信息（非政企商城显示） -->
        <span
          class="goods-sales"
          v-if="goodsInfo.sales > 0 && !isZQBusiness"
        >
          销量: {{ goodsInfo.sales }}
        </span>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed, toRefs } from 'vue'

// 组件导入
import PriceDisplay from '@components/Common/PriceDisplay.vue'

// 工具函数导入
import { getBizCode } from '@/utils/curEnv'

// 组件属性定义
const props = defineProps({
  // 商品信息对象
  goodsInfo: {
    type: Object,
    required: true,
    default: () => ({
      image: '',        // 商品图片URL
      name: '',         // 商品名称
      price: 0,         // 商品价格
      sales: 0,         // 销量
      spec: '',         // 商品规格（迷你卡片中暂不显示）
      lowPrice: '',     // 最低价格（政企商城）
      highPrice: ''     // 最高价格（政企商城）
    })
  }
})

// Props 解构：使用 toRefs 保持响应性
const { goodsInfo } = toRefs(props)

// 业务类型判断：检测是否为政企商城业务
const isZQBusiness = computed(() => getBizCode() === 'zq')
</script>

<style scoped lang="less">
.goods-card-mini {
  background: #FFFFFF;
  border-radius: 6px;
  overflow: hidden;
  transition: all 0.2s ease;
  cursor: pointer;
  position: relative;
  width: 100%;
  height: 100%;
  max-width: 160px;
  min-width: 100px;
  display: flex;
  flex-direction: column;
}

.goods-image {
  position: relative;
  width: 100%;
  //height: 120px;
  overflow: hidden;
  background: #F8F9FA;
  flex: 1;
  flex-shrink: 0;

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.3s ease;
    background-color: #F8F9FA;
  }

  &:hover img {
    transform: scale(1.05);
  }
}

.goods-info {
  padding: 8px;
  background: #FFFFFF;

  .goods-name {
    font-size: 12px;
    font-weight: 400;
    color: #171E24;
    margin: 0 0 6px 0;
    line-height: 1.3;
    display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      overflow: hidden;
      word-break: break-all;
    text-decoration: none;
  }

  .goods-details {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 4px;

    .goods-price {
      color: var(--wo-biz-theme-color);
      font-size: 14px;
      font-weight: 700;
      line-height: 1;

      &::before {
        content: '¥';
        font-size: 11px;
        font-weight: 400;
        margin-right: 1px;
      }
    }

    .goods-price-range {
      display: flex;
      align-items: baseline;
      color: var(--wo-biz-theme-color);
      font-weight: 700;
      flex: 1 1 auto;
      min-width: 0;

      .goods-price-number {
        font-size: 12px;
        position: relative;
        padding-left: 8px;

        &::before {
          content: '¥';
          position: absolute;
          left: 0;
          top: 0;
          font-size: 11px;
          font-weight: 400;
        }
      }

      .goods-price-separator {
        margin: 0 2px;
        color: #718096;
        font-weight: 400;
      }
    }

    .goods-sales {
      color: #718096;
      font-size: 11px;
      line-height: 1;
      white-space: nowrap;
    }
  }

  .goods-spec {
    color: #4A5568;
    font-size: 11px;
    line-height: 1.2;
    background: #F8F9FA;
    padding: 2px 6px;
    border-radius: 2px;
    margin-top: 2px;
    display: inline-block;
    max-width: 100%;
    overflow: hidden; text-overflow: ellipsis; white-space: nowrap;;
  }
}
</style>
