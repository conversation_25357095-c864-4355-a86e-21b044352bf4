<!--
  福利商城首页组件
  功能：展示福利商城首页内容，包括轮播图、菜单网格、商品分类头部、商品瀑布流
  特性：支持商品分类切换、瀑布流加载、骨架屏展示、平滑切换
  业务：专为员工福利采购提供的商品购买平台
-->
<template>
  <!-- 福利商城首页布局容器 -->
  <BaseHomeLayout
    home-class="welfare-home"
    search-placeholder="搜索商品"
    :header-banner-list="headerBannerList"
    :grid-menu-items="gridMenuItems"
    :grid-columns="5"
    :skeleton-states="skeletonStates"
    @search="handleSearch"
    @banner-click="handleBannerClick"
    @grid-item-click="handleGridItemClick"
    @more-click="handleMoreClick"
  >
    <template #additional-content>
      <!-- 商品分类头部区域 -->
      <div
        v-if="skeletonStates.goodsHeader || typeList.length > 0"
        class="home-goods-header-container"
      >
        <transition name="skeleton-fade" mode="out-in">
          <!-- 商品头部骨架屏 -->
          <GoodsHeaderSkeleton
            v-if="skeletonStates.goodsHeader"
            key="goods-header-skeleton"
          />
          <!-- 商品分类头部 -->
          <GoodsHeader
            v-else
            :typeList="typeList"
            key="goods-header-content"
            @switchTabs="switchTabs"
          />
        </transition>
      </div>
    </template>

    <template #main-content>
      <!-- 商品瀑布流区域 -->
      <WaterfallSection
        :waterfall-goods-list="waterfallGoodsList"
        :waterfall-loading="waterfallLoading"
        :waterfall-finished="waterfallFinished"
        :waterfall-button-can-show="waterfallButtonCanShow"
        :waterfall-render-complete="waterfallRenderComplete"
        :skeleton-states="{ waterfall: skeletonStates.waterfall }"
        @goods-click="handleGoodsClick"
        @load-more="handleWaterfallLoadMore"
        @after-render="handleWaterfallAfterRender"
      />
    </template>
  </BaseHomeLayout>
</template>
<script setup>
import { ref, onMounted, onUnmounted, nextTick } from 'vue'

// 组件导入
import BaseHomeLayout from '@views/Home/components/BaseHomeLayout.vue'
import WaterfallSection from '@components/GoodsListCommon/WaterfallSection.vue'
import GoodsHeader from '@views/Home/components/GoodsHeader.vue'
import GoodsHeaderSkeleton from '@views/Home/components/Skeleton/GoodsHeaderSkeleton.vue'

// 组合式函数导入
import { useHomeData } from '@views/Home/composables/useHomeData.js'
import { useHomeNavigation } from '@views/Home/composables/useHomeNavigation.js'

// 首页数据管理：获取轮播图、菜单、商品列表等数据和状态
const {
  headerBannerList,
  gridMenuItems,
  skeletonStates,
  moduleDataReady,
  waterfallGoodsList,
  waterfallLoading,
  waterfallFinished,
  waterfallButtonCanShow,
  waterfallRenderComplete,
  waterfallCurrentPage,
  getHeaderBannerList,
  getIconList,
  getWaterfallList,
  resetWaterfallState,
  getPartionListData,
  hideSkeletonInOrder
} = useHomeData()

// 首页导航管理：处理各种点击事件和页面跳转
const {
  handleGoodsClick,
  handleBannerClick,
  handleGridItemClick,
  handleMoreClick,
  handleSearch
} = useHomeNavigation()

// 商品分类数据管理
const typeList = ref([])
const goodsPoolIdSelected = ref('')

// 扩展骨架屏状态：添加商品头部骨架屏
skeletonStates.value = {
  ...skeletonStates.value,
  goodsHeader: true
}

// 扩展模块就绪状态：添加商品头部模块状态
moduleDataReady.value = {
  ...moduleDataReady.value,
  goodsHeader: false
}

// 瀑布流渲染完成处理：标记渲染状态完成
const handleWaterfallAfterRender = () => {
  waterfallRenderComplete.value = true
}

// 瀑布流加载更多处理：加载下一页商品数据
const handleWaterfallLoadMore = () => {
  getWaterfallList(goodsPoolIdSelected.value, '', true)
}

// 商品分类标签切换处理：平滑切换避免页面跳动
const switchTabs = async (id) => {
  // 不立即清空数据，避免页面回弹
  goodsPoolIdSelected.value = id

  // 重置分页状态但保留当前数据
  waterfallCurrentPage.value = 1
  waterfallFinished.value = false
  waterfallLoading.value = false
  waterfallButtonCanShow.value = false
  waterfallRenderComplete.value = false

  // 不设置骨架屏状态，直接加载新数据实现平滑切换
  await nextTick()
  getWaterfallList(id, '', false)
}

// 商品池切换处理：重置状态并加载新的商品数据
const changeGoodsPool = (id, sortType = '') => {
  goodsPoolIdSelected.value = id
  resetWaterfallState()
  getWaterfallList(id, sortType, false)
}

// 页面数据初始化：获取商品分区列表并按顺序显示骨架屏
const initPageData = async () => {
  const partionList = await getPartionListData(2)
  typeList.value = partionList

  // 标记商品头部数据就绪
  moduleDataReady.value.goodsHeader = true
  // 按顺序隐藏骨架屏，提供更好的加载体验
  await hideSkeletonInOrder(['banner', 'gridMenu', 'goodsHeader'])

  if (typeList.value.length > 0) {
    const defaultPartition = typeList.value[0]
    goodsPoolIdSelected.value = defaultPartition.id
    changeGoodsPool(defaultPartition.id)
  }
}

// 组件挂载时初始化：并行加载基础数据和页面数据
onMounted(() => {
  getHeaderBannerList()
  getIconList(5) // 福利商城首页使用 showPage: 5
  initPageData()
})

// 组件卸载时清理：预留清理逻辑
onUnmounted(() => {
  // 清理工作预留
})
</script>

<style scoped lang="less">
.welfare-home {
  .home-goods-header-container {
    margin: 10px 0;
    box-sizing: border-box;
  }
}

.skeleton-fade-enter-active,
.skeleton-fade-leave-active {
  transition: opacity 0.3s ease;
}

.skeleton-fade-enter-from,
.skeleton-fade-leave-to {
  opacity: 0;
}

.skeleton-fade-enter-to,
.skeleton-fade-leave-from {
  opacity: 1;
}
</style>
