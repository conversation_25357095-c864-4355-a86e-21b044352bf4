/**
 * 首页导航管理组合式函数
 * 功能：提供首页各种点击事件的统一处理逻辑
 * 特性：支持多业务类型、角色权限控制、路由跳转、外链跳转
 * 适用：通用商城、政企商城等不同业务场景
 */

import { useRouter } from 'vue-router'
import { computed } from "vue"

// 工具函数导入
import { getCustomerManagerInfo, getEnterpriseManagerInfo } from "@utils/zqInfo.js"
import { getBizCode } from "@utils/curEnv.js"

export function useHomeNavigation() {
  // 路由管理
  const router = useRouter()

  // 当前业务代码
  const currentBizCode = getBizCode()

  // 用户角色类型：根据不同信息源获取角色类型
  const userRoleType = computed(() => {
    const { roleType = '' } = getEnterpriseManagerInfo() || getCustomerManagerInfo() || {}
    return roleType
  })

  // 商品点击处理：根据业务类型和角色权限跳转到对应的商品详情页
  const handleGoodsClick = (goodsInfo) => {
    // 政企商城业务：根据角色类型跳转不同的详情页
    if (currentBizCode === 'zq') {
      if (userRoleType.value === '1' || userRoleType.value === '3') {
        // 角色类型1或3：普通用户，跳转普通详情页
        router.push(`/zq/goodsdetail/${goodsInfo.goodsId}`)
        return
      } else if (userRoleType.value === '2') {
        // 角色类型2：客户经理，跳转客户经理详情页
        router.push(`/zq/cm/goodsdetail/${goodsInfo.goodsId}`)
        return
      }
    }

    // 通用商城业务：跳转普通商品详情页
    if (goodsInfo.goodsId) {
      router.push(`/goodsdetail/${goodsInfo.goodsId}`)
    }
  }

  // 轮播图点击处理：跳转到指定链接地址
  const handleBannerClick = ({ item }) => {
    if (item.linkUrl) {
      window.location.href = item.linkUrl
    }
  }

  // 网格菜单项点击处理：跳转到指定功能页面
  const handleGridItemClick = ({ item }) => {
    if (item.url) {
      window.location.href = item.url
    }
  }

  // 更多按钮点击处理：预留扩展功能
  const handleMoreClick = () => {
    // 可以根据业务需要实现具体逻辑
    console.log('更多按钮被点击')
  }

  // 搜索处理：预留搜索功能
  const handleSearch = (keyword) => {
    // 可以根据业务需要实现搜索逻辑
    console.log('搜索关键词:', keyword)
  }

  // 活动点击处理：处理各种活动Banner的点击跳转
  const handleActivityClick = (item, position = '') => {
    if (item.url) {
      window.location.href = item.url
    }
  }

  return {
    handleGoodsClick,
    handleBannerClick,
    handleGridItemClick,
    handleMoreClick,
    handleSearch,
    handleActivityClick
  }
}
