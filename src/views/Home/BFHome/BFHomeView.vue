<!--
  扶贫业务首页组件
  功能：展示扶贫商城首页内容，包括轮播图、菜单网格、商品列表等
  特性：支持各县销冠、新上好物、爆款好物三个商品分区，具备懒加载和骨架屏功能
-->
<template>
  <!-- 扶贫首页布局容器 -->
  <BaseHomeLayout
    home-class="bf-home"
    search-placeholder="搜索商品"
    :header-banner-list="headerBannerList"
    :grid-menu-items="gridMenuItems"
    :grid-columns="5"
    :skeleton-states="skeletonStates"
    @search="handleSearch"
    @banner-click="handleBannerClick"
    @grid-item-click="handleGridItemClick"
    @more-click="handleMoreClick"
  >
    <template #main-content>
      <!-- 各县销冠商品区域 -->
      <SectionContainer
        v-if="skeletonStates.limited || limitedList.length > 0"
        title="各县销冠"
      >
        <WaterfallSection
          :waterfall-goods-list="limitedList"
          :waterfall-loading="limitedLoading"
          :waterfall-finished="limitedFinished"
          :waterfall-button-can-show="limitedButtonCanShow"
          :waterfall-render-complete="true"
          :skeleton-states="{ waterfall: skeletonStates.limited }"
          @goods-click="handleGoodsClick"
          @load-more="handleLimitedLoadMore"
        />
      </SectionContainer>

      <!-- 新上好物商品区域 -->
      <SectionContainer
        v-if="skeletonStates.newer || newerList.length > 0"
        title="新上好物"
      >
        <HorizontalScrollSection
          card-type="mini"
          :goods-list="newerList"
          :skeleton-states="{ horizontal: skeletonStates.newer }"
          @goods-click="handleGoodsClick"
        />
      </SectionContainer>

      <!-- 爆款好物商品区域 -->
      <SectionContainer
        v-if="skeletonStates.hotProducts || hotProductsList.length > 0"
        title="爆款好物"
      >
        <WaterfallSection
          :waterfall-goods-list="hotProductsList"
          :waterfall-loading="hotProductsLoading"
          :waterfall-finished="hotProductsFinished"
          :waterfall-button-can-show="hotProductsButtonCanShow"
          :waterfall-render-complete="true"
          :skeleton-states="{ waterfall: skeletonStates.hotProducts }"
          @goods-click="handleGoodsClick"
          @load-more="handleHotProductsLoadMore"
        />
      </SectionContainer>
    </template>
  </BaseHomeLayout>
</template>
<script setup>
import { ref, onMounted } from 'vue'
import { throttle } from 'lodash-es'
import { closeToast, showLoadingToast } from 'vant'

// 组件导入
import BaseHomeLayout from '@views/Home/components/BaseHomeLayout.vue'
import SectionContainer from '@views/Home/components/SectionContainer.vue'
import WaterfallSection from '@components/GoodsListCommon/WaterfallSection.vue'
import HorizontalScrollSection from '@views/Home/components/HorizontalScrollSection.vue'

// 组合式函数导入
import { useHomeData } from '@views/Home/composables/useHomeData.js'
import { useHomeNavigation } from '@views/Home/composables/useHomeNavigation.js'

// API 和工具函数导入
import { getGoodsList } from '@/api/interface/goods'
import { getBizCode } from '@/utils/curEnv'

// 首页数据管理：使用组合式函数获取轮播图、菜单等基础数据
const {
  headerBannerList,
  gridMenuItems,
  skeletonStates,
  getHeaderBannerList,
  getIconList,
  transformGoodsData
} = useHomeData()

// 首页导航管理：使用组合式函数处理各种点击事件
const {
  handleGoodsClick,
  handleBannerClick,
  handleGridItemClick,
  handleMoreClick,
  handleSearch
} = useHomeNavigation()

// 各县销冠商品数据状态管理
const limitedList = ref([])
const limitedLoading = ref(false)
const limitedFinished = ref(false)
const limitedCurrentPage = ref(1)
const limitedPageSize = ref(10)
const limitedButtonCanShow = ref(false)

// 新上好物商品数据状态管理
const newerList = ref([])
const newerLoading = ref(false)

// 爆款好物商品数据状态管理
const hotProductsList = ref([])
const hotProductsLoading = ref(false)
const hotProductsFinished = ref(false)
const hotProductsCurrentPage = ref(1)
const hotProductsPageSize = ref(10)
const hotProductsButtonCanShow = ref(false)

// 骨架屏状态初始化：为各个商品区域设置加载状态
skeletonStates.value = {
  ...skeletonStates.value,
  limited: true,
  newer: true,
  hotProducts: true
}

// 各县销冠商品列表获取：支持首次加载和分页加载更多
const getLimitedList = async (isLoadMore = false) => {
  if (limitedLoading.value) return

  limitedLoading.value = true

  // 首次加载显示全局加载提示
  if (!isLoadMore) {
    showLoadingToast()
  }

  const [err, json] = await getGoodsList({
    type: 'partion',
    bizCode: getBizCode('GOODS'),
    page_no: limitedCurrentPage.value,
    page_size: limitedPageSize.value,
    id: import.meta.env.VITE_FP_HOME_PAGE_LIMITED_GOODS_ID
  })

  if (!isLoadMore) {
    closeToast()
  }

  if (!err && json && Array.isArray(json)) {
    const newItems = json.map(transformGoodsData)

    if (isLoadMore) {
      // 加载更多：追加到现有列表
      limitedList.value = [...limitedList.value, ...newItems]
      limitedCurrentPage.value++
    } else {
      // 首次加载：替换列表并初始化状态
      limitedList.value = newItems
      skeletonStates.value.limited = false
      limitedButtonCanShow.value = true
      limitedCurrentPage.value = 2
    }

    // 判断是否已加载完所有数据
    limitedFinished.value = json.length === 0
  } else {
    limitedFinished.value = true
    if (!isLoadMore) {
      skeletonStates.value.limited = false
    }
  }

  limitedLoading.value = false
}

// 各县销冠商品加载更多处理：使用节流防止频繁请求
const handleLimitedLoadMore = throttle(() => {
  if (!limitedFinished.value && !limitedLoading.value) {
    getLimitedList(true)
  }
}, 300)

// 新上好物商品列表获取：仅获取第一页数据用于横向滚动展示
const getNewerList = async () => {
  if (newerLoading.value) return

  newerLoading.value = true

  const [err, json] = await getGoodsList({
    type: 'partion',
    bizCode: getBizCode('GOODS'),
    page_no: 1,
    page_size: 10,
    id: import.meta.env.VITE_FP_HOME_PAGE_NEWER_GOODS_ID
  })

  if (!err && json && Array.isArray(json)) {
    newerList.value = json.map(transformGoodsData)
  }

  skeletonStates.value.newer = false
  newerLoading.value = false
}

// 爆款好物商品列表获取：支持首次加载和分页加载更多
const getHotProductsList = async (isLoadMore = false) => {
  if (hotProductsLoading.value) return

  hotProductsLoading.value = true

  // 加载更多时显示加载提示
  if (isLoadMore) {
    showLoadingToast()
  }

  const [err, json] = await getGoodsList({
    type: 'partion',
    bizCode: getBizCode('GOODS'),
    page_no: hotProductsCurrentPage.value,
    page_size: hotProductsPageSize.value,
    id: import.meta.env.VITE_FP_HOME_PAGE_GUESS_GOODS_ID
  })

  if (isLoadMore) {
    closeToast()
  }

  if (!err && json && Array.isArray(json)) {
    const newItems = json.map(transformGoodsData)

    if (isLoadMore) {
      // 加载更多：追加到现有列表
      hotProductsList.value = [...hotProductsList.value, ...newItems]
      hotProductsCurrentPage.value++
    } else {
      // 首次加载：替换列表并初始化状态
      hotProductsList.value = newItems
      skeletonStates.value.hotProducts = false
      hotProductsButtonCanShow.value = true
      hotProductsCurrentPage.value = 2
    }

    // 判断是否已加载完所有数据
    hotProductsFinished.value = json.length === 0
  } else {
    hotProductsFinished.value = true
    if (!isLoadMore) {
      skeletonStates.value.hotProducts = false
    }
  }

  hotProductsLoading.value = false
}

// 爆款好物商品加载更多处理：使用节流防止频繁请求
const handleHotProductsLoadMore = throttle(() => {
  if (!hotProductsFinished.value && !hotProductsLoading.value) {
    getHotProductsList(true)
  }
}, 300)

// 组件挂载时初始化：并行加载所有数据提高页面加载速度
onMounted(() => {
  getHeaderBannerList()
  getIconList()
  getLimitedList(false)
  getNewerList()
  getHotProductsList(false)
})
</script>

<style scoped lang="less">
.bf-home {
  // 扶贫首页样式预留
}
</style>
