<template>
  <BaseHomeLayout
    home-class="labor-home"
    search-placeholder="搜索商品"
    :header-banner-list="headerBannerList"
    :grid-menu-items="gridMenuItems"
    :grid-columns="5"
    :skeleton-states="skeletonStates"
    @search="handleSearch"
    @banner-click="handleBannerClick"
    @grid-item-click="handleGridItemClick"
    @more-click="handleMoreClick"
  >
    <template #main-content>
      <WaterfallSection
        :waterfall-goods-list="waterfallGoodsList"
        :waterfall-loading="waterfallLoading"
        :waterfall-finished="waterfallFinished"
        :waterfall-button-can-show="waterfallButtonCanShow"
        :waterfall-render-complete="waterfallRenderComplete"
        :skeleton-states="{ waterfall: skeletonStates.waterfall }"
        @goods-click="handleGoodsClick"
        @load-more="handleWaterfallLoadMore"
        @after-render="handleWaterfallAfterRender"
      />
    </template>
  </BaseHomeLayout>
</template>
<script setup>
import { ref, onMounted, onUnmounted } from 'vue'
import BaseHomeLayout from '@views/Home/components/BaseHomeLayout.vue'
import WaterfallSection from '@components/GoodsListCommon/WaterfallSection.vue'
import { useHomeData } from '@views/Home/composables/useHomeData.js'
import { useHomeNavigation } from '@views/Home/composables/useHomeNavigation.js'

// 使用组合式函数
const {
  headerBannerList,
  gridMenuItems,
  skeletonStates,
  waterfallGoodsList,
  waterfallLoading,
  waterfallFinished,

  waterfallButtonCanShow,
  waterfallRenderComplete,
  getHeaderBannerList,
  getIconList,
  getWaterfallList,
  resetWaterfallState,
  getPartionListData
} = useHomeData()

const {
  handleGoodsClick,
  handleBannerClick,
  handleGridItemClick,
  handleMoreClick,
  handleSearch
} = useHomeNavigation()

// 页面特有数据
const typeList = ref([])
const goodsPoolIdSelected = ref('')

// 瀑布流渲染完成处理
const handleWaterfallAfterRender = () => {
  waterfallRenderComplete.value = true
}

// 加载更多瀑布流商品
const handleWaterfallLoadMore = () => {
  getWaterfallList(goodsPoolIdSelected.value, '', true)
}

// 切换商品池
const changeGoodsPool = (id, sortType = '') => {
  goodsPoolIdSelected.value = id
  resetWaterfallState()
  getWaterfallList(id, sortType, false)
}

// 初始化页面数据
const initPage = async () => {
  const partionList = await getPartionListData(2)
  typeList.value = partionList

  if (typeList.value.length > 0) {
    const recommond = typeList.value[0]
    goodsPoolIdSelected.value = recommond.id
    changeGoodsPool(recommond.id)
  }
}

onMounted(() => {
  getHeaderBannerList()
  getIconList(6) // LaborHome 使用 showPage: 6
  initPage()
})

onUnmounted(() => {
  // 清理工作
})
</script>

<style scoped lang="less">
.labor-home {
  //padding: 4px12;
  //box-sizing: border-box;
}
</style>
