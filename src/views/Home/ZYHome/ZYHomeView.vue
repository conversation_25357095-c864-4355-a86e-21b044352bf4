<template>
  <BaseHomeLayout home-class="zy-home" search-placeholder="搜索商品" :header-banner-list="headerBannerList"
    :grid-menu-items="gridMenuItems" :grid-columns="5" grid-display-mode="scroll" :skeleton-states="skeletonStates"
    @search="handleSearch" @banner-click="handleBannerClick" @grid-item-click="handleGridItemClick"
    @more-click="handleMoreClick">
    <template #additional-content>
      <div v-if="skeletonStates.subBanner || subBanner.length > 0" class="home-sub-banner-container">
        <transition name="skeleton-fade" mode="out-in">
          <SubBannerSkeleton v-if="skeletonStates.subBanner" key="sub-banner-skeleton" />
          <div v-else-if="subBanner.length > 0" key="sub-banner-content" class="home-sub-banner">
            <img @click="handleActivityClick(subBanner[0], 'bottom')" :src="subBanner[0].imgUrl" alt=""
              class="home-sub-banner-img" />
          </div>
        </transition>
      </div>

      <div v-if="skeletonStates.thirdBanner || thirdBanner.length > 0" class="home-third-banner-container">
        <transition name="skeleton-fade" mode="out-in">
          <ThirdBannerSkeleton v-if="skeletonStates.thirdBanner" key="third-banner-skeleton" />
          <div v-else-if="thirdBanner.length > 0" key="third-banner-content" class="home-third-banner">
            <div v-if="thirdBanner[0]" @click="handleActivityClick(thirdBanner[0], 'left')"
              class="home-third-banner-left">
              <img :src="thirdBanner[0].imgUrl" alt="" class="home-third-banner-img" />
            </div>
            <div v-if="thirdBanner[1]" @click="handleActivityClick(thirdBanner[1], 'right')"
              class="home-third-banner-right">
              <img :src="thirdBanner[1].imgUrl" alt="" class="home-third-banner-img" />
            </div>
          </div>
        </transition>
      </div>

      <HorizontalScrollSection v-if="skeletonStates.specialGoods || specialGoodsList.length > 0"
        class="home-special-goods-container" :goods-list="specialGoodsList" :background-image="specialPoolIdImage"
        :show-hot-zone="true" card-type="mini" :skeleton-states="{ horizontal: skeletonStates.specialGoods }"
        @goods-click="handleGoodsClick" @hot-zone-click="handleHotZoneClick" />

      <div v-if="skeletonStates.goodsHeader || typeList.length > 0" class="home-goods-header-container">
        <transition name="skeleton-fade" mode="out-in">
          <GoodsHeaderSkeleton v-if="skeletonStates.goodsHeader" key="goods-header-skeleton" />
          <GoodsHeader v-else :typeList="typeList" key="goods-header-content" @switchTabs="switchTabs" />
        </transition>
      </div>
    </template>

    <template #main-content>
      <WaterfallSection :waterfall-goods-list="waterfallGoodsList" :waterfall-loading="waterfallLoading"
        :waterfall-finished="waterfallFinished" :waterfall-button-can-show="waterfallButtonCanShow"
        :waterfall-render-complete="waterfallRenderComplete" :skeleton-states="{ waterfall: skeletonStates.waterfall }"
        @goods-click="handleGoodsClick" @load-more="handleWaterfallLoadMore"
        @after-render="handleWaterfallAfterRender" />
    </template>
  </BaseHomeLayout>
</template>
<script setup>
import { ref, onMounted, onUnmounted, nextTick } from 'vue'
import BaseHomeLayout from '@views/Home/components/BaseHomeLayout.vue'
import WaterfallSection from '@components/GoodsListCommon/WaterfallSection.vue'
import HorizontalScrollSection from '@views/Home/components/HorizontalScrollSection.vue'
import GoodsHeader from '@views/Home/components/GoodsHeader.vue'
import GoodsHeaderSkeleton from '@views/Home/components/Skeleton/GoodsHeaderSkeleton.vue'
import SubBannerSkeleton from '@views/Home/components/Skeleton/SubBannerSkeleton.vue'
import ThirdBannerSkeleton from '@views/Home/components/Skeleton/ThirdBannerSkeleton.vue'
import { useHomeData } from '@views/Home/composables/useHomeData.js'
import { useHomeNavigation } from '@views/Home/composables/useHomeNavigation.js'
import { getBannerInfo, getIconInfo } from '@/api/interface/bannerIcon'
import { getGoodsList } from '@/api/interface/goods'
import { getBizCode } from '@/utils/curEnv'
import { curChannelBiz } from '@/utils/storage'
import { closeToast, showLoadingToast } from 'vant'

// 使用组合式函数
const {
  headerBannerList,
  gridMenuItems,
  skeletonStates,
  moduleDataReady,
  waterfallGoodsList,
  waterfallLoading,
  waterfallFinished,
  waterfallButtonCanShow,
  waterfallRenderComplete,
  waterfallCurrentPage,
  channelFilterd,
  transformGoodsData,
  getWaterfallList,
  resetWaterfallState,
  getPartionListData,
  hideSkeletonInOrder
} = useHomeData()

const {
  handleGoodsClick,
  handleBannerClick,
  handleGridItemClick,
  handleMoreClick,
  handleSearch,
  handleActivityClick
} = useHomeNavigation()

// 页面特有数据
const subBanner = ref([])
const thirdBanner = ref([])
const typeList = ref([])
const goodsPoolIdSelected = ref('')
const specialGoodsList = ref([])
const specialPoolIdSelected = ref('')
const specialPoolIdImage = ref('')

// 扩展骨架屏状态
skeletonStates.value = {
  ...skeletonStates.value,
  subBanner: true,
  thirdBanner: true,
  specialGoods: true,
  goodsHeader: true
}

moduleDataReady.value = {
  ...moduleDataReady.value,
  subBanner: false,
  thirdBanner: false,
  specialGoods: false,
  goodsHeader: false
}

// 瀑布流渲染完成处理
const handleWaterfallAfterRender = () => {
  waterfallRenderComplete.value = true
}

// 加载更多瀑布流商品
const handleWaterfallLoadMore = () => {
  getWaterfallList(goodsPoolIdSelected.value, '', true)
}

// 热区点击处理
const handleHotZoneClick = () => {
  console.log('热区被点击')
}

// 加载Banner配置信息
const loadBannerConfigInfo = async () => {
  showLoadingToast()

  // 主Banner
  const [, json] = await getBannerInfo({ bizCode: getBizCode(), showPage: 1 })
  headerBannerList.value = channelFilterd(json.sort((a, b) => b.orderBy - a.orderBy) || []).map(item => ({
    type: 'image',
    url: item.imgUrl,
    alt: item.bannerChName,
    linkUrl: item.url,
  }))
  moduleDataReady.value.banner = true

  // 子Banner
  const [, json1] = await getBannerInfo({ bizCode: getBizCode(), showPage: 2 })
  subBanner.value = channelFilterd(json1 || [])
  moduleDataReady.value.subBanner = true

  // 第三Banner
  const [, json2] = await getBannerInfo({ bizCode: getBizCode(), showPage: 3 })
  thirdBanner.value = channelFilterd(json2 || [])
  moduleDataReady.value.thirdBanner = true

  // 背景图
  const [, json3] = await getBannerInfo({ bizCode: getBizCode(), showPage: 4 })
  const backImgList = json3 || []
  if (backImgList.length > 0) {
    specialPoolIdImage.value = backImgList[0].imgUrl
  }

  closeToast()
  await hideSkeletonInOrder(['banner', 'gridMenu', 'subBanner', 'thirdBanner'])
}

// 获取图标列表
const getIconList = async () => {
  const [err, json] = await getIconInfo({
    bizCode: getBizCode('QUERY'),
    channel: curChannelBiz.get(),
    showPage: 1
  })

  if (!err && json) {
    const iconData = json.map(item => ({
      title: item.chName || item.title,
      subtitle: item.iconSubTitle || item.subtitle,
      icon: item.imgUrl || item.icon,
      url: item.url,
      badge: item.badge || item.iconBadge
    }))
    gridMenuItems.value = iconData
  } else {
    gridMenuItems.value = []
  }

  moduleDataReady.value.gridMenu = true
  await hideSkeletonInOrder(['banner', 'gridMenu'])
}

// 获取特殊商品列表
const getSpecialGoodsList = async () => {
  if (!specialPoolIdSelected.value) return

  const params = {
    type: 'partion',
    id: specialPoolIdSelected.value,
    bizCode: getBizCode('GOODS'),
    page_no: 1,
    page_size: 20,
  }

  const [err, json] = await getGoodsList(params)
  if (!err && json) {
    specialGoodsList.value = json.map(transformGoodsData)
  }

  moduleDataReady.value.specialGoods = true
  await hideSkeletonInOrder(['banner', 'gridMenu', 'subBanner', 'thirdBanner', 'specialGoods'])
}

// 切换标签页
const switchTabs = async (id) => {
  // 不立即清空数据，避免页面回弹
  goodsPoolIdSelected.value = id
  // 重置分页状态但保留当前数据
  waterfallCurrentPage.value = 1
  waterfallFinished.value = false
  waterfallLoading.value = false
  waterfallButtonCanShow.value = false
  waterfallRenderComplete.value = false

  // 不设置骨架屏状态，直接加载新数据
  await nextTick()
  getWaterfallList(id, '', false)
}

// 切换商品池
const changeGoodsPool = (id, sortType = '') => {
  goodsPoolIdSelected.value = id
  resetWaterfallState()
  getWaterfallList(id, sortType, false)
}

// 初始化页面数据
const initPage = async () => {
  // 获取主商品池
  const partionList = await getPartionListData(2)
  typeList.value = partionList

  moduleDataReady.value.goodsHeader = true
  await hideSkeletonInOrder(['banner', 'gridMenu', 'subBanner', 'thirdBanner', 'specialGoods', 'goodsHeader'])

  if (typeList.value.length > 0) {
    const recommond = typeList.value[0]
    goodsPoolIdSelected.value = recommond.id
    changeGoodsPool(recommond.id)
  }

  // 获取特殊商品池
  const specialPartionList = await getPartionListData(1)
  if (specialPartionList.length > 0) {
    specialPoolIdSelected.value = specialPartionList[0].id
    getSpecialGoodsList()
  }
}

onMounted(() => {
  loadBannerConfigInfo()
  getIconList()
  initPage()
})

onUnmounted(() => {
  // 清理工作
})
</script>

<style scoped lang="less">
.zy-home {
  .home-sub-banner-container {
    margin: 8px 10px;
    box-sizing: border-box;
  }

  .home-sub-banner {
    position: relative;
    width: 100%;
    box-sizing: border-box;

    .home-sub-banner-img {
      display: block;
      width: 100%;
      border-radius: 12px;
      box-sizing: border-box;
    }
  }

  .home-third-banner-container {
    margin: 8px 12px 30px;
    box-sizing: border-box;
  }

  .home-third-banner {
    display: flex;
    height: 210px;
    gap: 8px;
    box-sizing: border-box;

    .home-third-banner-left {
      display: flex;
      width: 50%;
      box-sizing: border-box;
    }

    .home-third-banner-right {
      display: flex;
      width: 50%;
      box-sizing: border-box;
    }

    .home-third-banner-img {
      width: 100%;
      height: 210px;
      border-radius: 12px;
      object-fit: cover;
      box-sizing: border-box;
    }
  }

  .home-goods-header-container {
    margin: 10px 0;
    box-sizing: border-box;
  }

  .home-special-goods-container {
    :deep(.home-horizontal-scroll-wrapper) {
      padding: 10px;
      box-sizing: border-box;
    }
  }
}

.skeleton-fade-enter-active,
.skeleton-fade-leave-active {
  transition: opacity 0.3s ease;
}

.skeleton-fade-enter-from,
.skeleton-fade-leave-to {
  opacity: 0;
}

.skeleton-fade-enter-to,
.skeleton-fade-leave-from {
  opacity: 1;
}
</style>
