<!--
  政企商城首页组件
  功能：展示政企商城首页内容，支持省份服务商筛选、商品瀑布流展示
  特性：支持角色权限控制、省份服务商选择、商品池切换、瀑布流加载
  业务：专为政企客户提供的B2B商品采购平台
-->
<template>
  <!-- 省份服务商筛选组件 -->
  <ProvinceFilter @confirm="handleSelectionConfirm" />

  <!-- 政企商城首页布局容器 -->
  <BaseHomeLayout
    home-class="zq-home"
    search-placeholder="搜索商品"
    :header-banner-list="headerBannerList"
    :grid-menu-items="gridMenuItems"
    :grid-columns="5"
    :skeleton-states="skeletonStates"
    @search="handleSearch"
    @banner-click="handleBannerClick"
    @grid-item-click="handleGridItemClick"
    @more-click="handleMoreClick"
  >
    <template #main-content>
      <!-- 商品瀑布流区域 -->
      <WaterfallSection
        :waterfall-goods-list="waterfallGoodsList"
        :waterfall-loading="waterfallLoading"
        :waterfall-finished="waterfallFinished"
        :waterfall-button-can-show="waterfallButtonCanShow"
        :waterfall-render-complete="waterfallRenderComplete"
        :skeleton-states="{ waterfall: skeletonStates.waterfall }"
        loadMode="scroll"
        @goods-click="handleGoodsClick"
        @load-more="handleWaterfallLoadMore"
        @after-render="handleWaterfallAfterRender"
      />
      <!-- TODO: 没有数据要增加提示和样式 -->
    </template>
  </BaseHomeLayout>
</template>
<script setup>
import { ref, onMounted, onUnmounted, computed, defineAsyncComponent } from 'vue'

// 组件导入
import BaseHomeLayout from '@views/Home/components/BaseHomeLayout.vue'
import WaterfallSection from '@components/GoodsListCommon/WaterfallSection.vue'

// 异步加载省份筛选组件：提高首屏加载性能
const ProvinceFilter = defineAsyncComponent(() => import('@/components/ZQCommon/ProvinceFilter.vue'))

// 组合式函数导入
import { useHomeData } from '@views/Home/composables/useHomeData.js'
import { useHomeNavigation } from '@views/Home/composables/useHomeNavigation.js'

// 工具函数和状态管理导入
import { getEnterpriseManagerInfo } from '@/utils/zqInfo'
import { useProvinceServiceStore } from '@/store/modules/provinceService.js'

// 首页数据管理：获取轮播图、菜单、商品列表等数据和状态
const {
  headerBannerList,
  gridMenuItems,
  skeletonStates,
  waterfallGoodsList,
  waterfallLoading,
  waterfallFinished,
  waterfallCurrentPage,
  waterfallPageSize,
  waterfallButtonCanShow,
  waterfallRenderComplete,
  getHeaderBannerList,
  getIconList,
  getWaterfallList,
  getWaterfallListWithStore,
  resetWaterfallState,
  getPartionListData
} = useHomeData()

// 首页导航管理：处理各种点击事件和页面跳转
const {
  handleGoodsClick,
  handleBannerClick,
  handleGridItemClick,
  handleMoreClick,
  handleSearch
} = useHomeNavigation()

// 省份服务商状态管理
const provinceServiceStore = useProvinceServiceStore()

// 商品分区数据管理
const typeList = ref([])
const goodsPoolIdSelected = ref('')

// 用户角色类型：根据角色决定使用不同的商品加载策略
const userRoleType = computed(() => {
  const { roleType: rt = '' } = getEnterpriseManagerInfo() || {}
  return rt
})

// 瀑布流渲染完成处理：标记渲染状态完成
const handleWaterfallAfterRender = () => {
  waterfallRenderComplete.value = true
}

// 瀑布流加载更多处理：根据角色类型选择不同的加载策略
const handleWaterfallLoadMore = () => {
  if (userRoleType.value === '4') {
    // 角色类型4：白名单，使用支持省份服务商筛选的加载方法
    getWaterfallListWithStore(goodsPoolIdSelected.value, '', true, provinceServiceStore)
  } else {
    // 其他角色：使用普通加载方法
    getWaterfallList(goodsPoolIdSelected.value, '', true)
  }
}

// 商品池切换处理：根据角色类型选择不同的加载策略
const changeGoodsPool = (id, sortType = '') => {
  goodsPoolIdSelected.value = id
  resetWaterfallState()

  if (userRoleType.value === '4') {
    // 角色类型4：白名单，使用支持省份服务商筛选的加载方法
    getWaterfallListWithStore(id, sortType, false, provinceServiceStore)
  } else {
    // 其他角色：使用普通加载方法
    getWaterfallList(id, sortType, false)
  }
}

// 省份服务商选择确认处理：更新筛选条件并重新加载商品
const handleSelectionConfirm = (selection) => {
  console.log('确认选择，当前选中的省份ID:', selection.areaId)
  console.log('确认选择，当前选中的服务商ID:', selection.isvId)
  console.log('确认选择，当前选中的省份名称:', selection.provinceName)
  console.log('确认选择，当前选中的服务商名称:', selection.serviceName)

  // 保存选择到状态管理中
  if (selection.areaId) {
    provinceServiceStore.selectProvince(selection.areaId)
  }
  if (selection.isvId) {
    provinceServiceStore.selectService(selection.isvId)
  }

  // 重置瀑布流状态并重新加载商品列表
  resetWaterfallState()

  // 使用支持省份服务商筛选的加载方法重新加载商品
  getWaterfallListWithStore(goodsPoolIdSelected.value, '', false, provinceServiceStore)
}

// 页面数据初始化：获取商品分区列表并加载默认商品池
const initPageData = async () => {
  const partionList = await getPartionListData(2)
  typeList.value = partionList

  if (typeList.value.length > 0) {
    const defaultPartition = typeList.value[0]
    goodsPoolIdSelected.value = defaultPartition.id
    changeGoodsPool(defaultPartition.id)
  }
}

// 组件挂载时初始化：并行加载基础数据和页面数据
onMounted(() => {
  getHeaderBannerList()
  getIconList(8) // 政企商城首页使用 showPage: 8
  initPageData()
})

// 组件卸载时清理：预留清理逻辑
onUnmounted(() => {
  // 清理工作预留
})
</script>

<style scoped lang="less">
.zq-home {
  padding-bottom: 48px;
  box-sizing: border-box;
}
</style>
