<!--
  数字乡村首页组件
  功能：展示数字乡村商城首页内容，包括轮播图、菜单网格、热门商品推荐、商品瀑布流、本地供应商推荐
  特性：支持模块化显示控制、热门商品推荐、瀑布流加载、悬浮订单入口、底部供应商推荐
  业务：专为数字乡村业务提供的本地化商品展示和供应商推荐平台
-->
<template>
  <!-- 数字乡村首页布局容器 -->
  <BaseHomeLayout
    :show-modules="moduleDisplayConfig"
    home-class="dv-home"
    search-placeholder="搜索商品"
    :header-banner-list="headerBannerList"
    :grid-menu-items="gridMenuItems"
    :grid-columns="5"
    :skeleton-states="skeletonStates"
    @search="handleSearch"
    @banner-click="handleBannerClick"
    @grid-item-click="handleGridItemClick"
    @more-click="handleMoreClick"
  >
    <template #additional-content>
      <!-- 热门商品推荐区域 -->
      <section
        v-if="shouldShowRecommendSection"
        class="dv-recommend-section"
      >
        <Transition name="skeleton-fade" mode="out-in">
          <!-- 推荐区域骨架屏 -->
          <RecommendSkeleton
            v-if="skeletonStates.recommend"
            key="recommend-skeleton"
          />
          <!-- 热门商品推荐内容 -->
          <RecommendView
            v-else
            key="recommend-content"
            :hot-goods="hotGoods"
          />
        </Transition>
      </section>
    </template>

    <template #main-content>
      <!-- 主要内容区域 -->
      <main class="dv-main-content">
        <!-- 商品瀑布流区域 -->
        <WaterfallSection
          class="dv-waterfall"
          :waterfall-goods-list="waterfallGoodsList"
          :waterfall-loading="waterfallLoading"
          :waterfall-finished="waterfallFinished"
          :waterfall-button-can-show="waterfallButtonCanShow"
          :waterfall-render-complete="waterfallRenderComplete"
          :skeleton-states="waterfallSkeletonStates"
          @goods-click="handleGoodsClick"
          @load-more="handleWaterfallLoadMore"
          @after-render="handleWaterfallAfterRender"
        />

        <!-- 悬浮订单入口 -->
        <aside class="dv-order-float">
          <img
            src="./assets/order.png"
            alt="购物订单"
            loading="lazy"
            @click="handleOrderListClick"
          >
        </aside>

        <!-- 底部操作栏占位符 -->
        <WoActionBarPlaceholder :height="85" />

        <!-- 底部供应商推荐操作栏 -->
        <WoActionBar class="dv-action-bar">
          <div class="dv-action-content">
            <!-- 操作栏图标 -->
            <div class="dv-action-icon" aria-hidden="true"></div>
            <!-- 操作栏文本 -->
            <div class="dv-action-text">
              <span class="dv-action-title">本地农产品销售攻略</span>
              <span class="dv-action-subtitle">本地供应商推荐</span>
            </div>
            <!-- 查看详情按钮 -->
            <WoButton
              type="gradient"
              size="small"
              round
              @click="handleSupplierRecommendClick"
            >
              查看详情
            </WoButton>
          </div>
        </WoActionBar>
      </main>
    </template>
  </BaseHomeLayout>
</template>
<script setup>
import { ref, onMounted, computed, nextTick } from 'vue'
import { useRouter } from 'vue-router'
import { debounce } from 'lodash-es'
import { closeToast, showLoadingToast } from 'vant'
import { isUnicom } from 'commonkit'

// 组件导入
import BaseHomeLayout from '@views/Home/components/BaseHomeLayout.vue'
import WaterfallSection from '@components/GoodsListCommon/WaterfallSection.vue'
import RecommendView from '@views/Home/components/RecommendView.vue'
import RecommendSkeleton from '@views/Home/components/Skeleton/RecommendSkeleton.vue'
import WoActionBarPlaceholder from '@components/WoElementCom/WoActionBarPlaceholder.vue'
import WoActionBar from '@components/WoElementCom/WoActionBar.vue'
import WoButton from '@components/WoElementCom/WoButton/WoButton.vue'

// 组合式函数导入
import { useHomeData } from '@views/Home/composables/useHomeData.js'
import { useHomeNavigation } from '@views/Home/composables/useHomeNavigation.js'

// API 和工具函数导入
import { getHotGoods } from '@/api/interface/digitalVillage'
import { getBizCode } from '@/utils/curEnv'
import { curChannelBiz } from '@/utils/storage'

// 状态管理导入
import { useUserStore } from '@/store/modules/user'

// 首页数据管理：获取轮播图、菜单、商品列表等数据和状态
const {
  headerBannerList,
  gridMenuItems,
  skeletonStates,
  moduleDataReady,
  waterfallGoodsList,
  waterfallLoading,
  waterfallFinished,
  waterfallButtonCanShow,
  waterfallRenderComplete,
  getHeaderBannerList,
  getWaterfallList,
  resetWaterfallState,
  getPartionListData,
  hideSkeletonInOrder
} = useHomeData()

// 首页导航管理：处理各种点击事件和页面跳转
const {
  handleGoodsClick,
  handleBannerClick,
  handleGridItemClick,
  handleMoreClick,
  handleSearch,
} = useHomeNavigation()

// 路由和状态管理
const router = useRouter()
const userStore = useUserStore()

// 页面数据状态管理
const hotGoods = ref([])
const typeList = ref([])
const goodsPoolIdSelected = ref('')

const showModulesConfig = computed(() => ({
  search: false,
  banner: true,
  gridMenu: false
}))

const shouldShowRecommend = computed(() =>
  skeletonStates.value.recommend || hotGoods.value.length > 0
)

const waterfallSkeletonStates = computed(() => ({
  waterfall: skeletonStates.value.waterfall
}))

skeletonStates.value = {
  ...skeletonStates.value,
  recommend: true
}

moduleDataReady.value = {
  ...moduleDataReady.value,
  recommend: false
}

const loadOrderList = debounce(async () => {
  await userStore.queryLoginStatus()
  if (userStore.isLogin) {
    router.push({ path: '/user/order/list' })
  } else {
    await userStore.login({ reload: false })
  }
}, 300)

const recommend = () => {
  router.push('/digitalVillage/znShop')
}

const handleWaterfallAfterRender = () => {
  waterfallRenderComplete.value = true
}

const handleWaterfallLoadMore = debounce(() => {
  getWaterfallList(goodsPoolIdSelected.value, '', true)
}, 200)

const changeGoodsPool = (id, sortType = '') => {
  goodsPoolIdSelected.value = id
  resetWaterfallState()
  getWaterfallList(id, sortType, false)
}

const initPage = async () => {
  try {
    const partionList = await getPartionListData(2)
    typeList.value = partionList

    if (typeList.value.length > 0) {
      const firstRecommend = typeList.value[0]
      goodsPoolIdSelected.value = firstRecommend.id
      changeGoodsPool(firstRecommend.id)
    }
  } catch (error) {
    console.error('初始化页面数据失败:', error)
  }
}

const initHotGoods = async () => {
  const params = {
    showPage: '1',
    bizCode: getBizCode('GOODS'),
    channel: curChannelBiz.get()
  }

  if (!isUnicom) {
    showLoadingToast()
  }

  try {
    const [err, json] = await getHotGoods(params)

    if (err) {
      console.error('获取热门商品失败:', err.msg)
      hotGoods.value = []
    } else {
      hotGoods.value = json || []
    }
  } catch (error) {
    console.error('获取热门商品异常:', error)
    hotGoods.value = []
  } finally {
    if (!isUnicom) {
      closeToast()
    }
    moduleDataReady.value.recommend = true
    await nextTick()
    await hideSkeletonInOrder(['banner', 'gridMenu', 'recommend'])
  }
}

onMounted(async () => {
  await Promise.allSettled([
    getHeaderBannerList(),
    initHotGoods(),
    initPage()
  ])
})
</script>


<style scoped lang="less">
.dv-home {
  min-height: 100vh;

  .dv-recommend-section {
    padding: 0 10px;
    position: relative;
    box-sizing: border-box;
  }

  .dv-main-content {
    position: relative;
  }

  .dv-waterfall {
    :deep(.home-waterfall-container) {
      padding: 0 10px;
    }
  }

  .dv-order-float {
    position: fixed;
    width: 100px;
    height: auto;
    right: 0;
    top: 150px;
    z-index: 999;

    img {
      width: 100%;
      height: auto;
      cursor: pointer;
      transition: transform 0.2s ease;

      &:hover {
        transform: scale(1.05);
      }

      &:active {
        transform: scale(0.95);
      }
    }
  }

  .dv-action-bar {
    padding: 0;

    .dv-action-content {
      background: #ffffff;
      box-shadow: 0 -9px 32px 0 rgba(0, 0, 0, 0.07);
      min-height: 65px;
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: space-between;
      gap: 12px;
      padding: 0 12px;
    }

    .dv-action-icon {
      background-image: url("./assets/fupin.png");
      height: 54px;
      width: 54px;
      background-repeat: no-repeat;
      background-size: contain;
      background-position: center;
      flex-shrink: 0;
    }

    .dv-action-text {
      display: flex;
      flex-direction: column;
      justify-content: center;
      flex: 1;
      min-width: 0;
    }

    .dv-action-title {
      font-size: 16px;
      color: #00200a;
      font-weight: 500;
      line-height: 1.2;
    }

    .dv-action-subtitle {
      font-size: 12px;
      color: #828282;
      font-weight: 400;
      margin-top: 4px;
      line-height: 1.2;
    }

    :deep(.wo-button) {
      flex-shrink: 0;
    }
  }
}

// 骨架屏过渡动画
.skeleton-fade-enter-active,
.skeleton-fade-leave-active {
  transition: opacity 0.3s ease;
}

.skeleton-fade-enter-from,
.skeleton-fade-leave-to {
  opacity: 0;
}

.skeleton-fade-enter-to,
.skeleton-fade-leave-from {
  opacity: 1;
}

// 响应式优化
@media (max-width: 375px) {
  .dv-home {
    .dv-action-content {
      gap: 8px;
      padding: 0 8px;
    }

    .dv-action-icon {
      width: 48px;
      height: 48px;
    }

    .dv-action-title {
      font-size: 14px;
    }

    .dv-action-subtitle {
      font-size: 11px;
    }
  }
}
</style>
