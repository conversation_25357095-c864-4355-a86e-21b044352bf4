<template>
  <div class="sr-container">
    <van-tabs
      v-model:active="activeTab"
      animated
      swipeable
      color="#FF780A"
      title-active-color="#FF780A"
      title-inactive-color="#171E24"
      line-width="50%"
      background="white"
      @change="handleTabChange"
    >
      <van-tab title="新增推荐">
        <section class="sr-add-section">
          <div class="sr-form-wrapper">
            <van-form ref="formRef">
              <van-field
                v-model="formData.recommendName"
                name="推荐人姓名"
                label="推荐人姓名"
                placeholder="请输入姓名"
                label-width="40%"
                :rules="[{ required: true, message: '请输入推荐人姓名' }]"
              />
              <van-field
                v-model="formData.recommendPhone"
                name="推荐手机号"
                label="推荐手机号"
                maxlength="11"
                placeholder="请输入手机号码"
                label-width="40%"
                :rules="phoneRules"
              />
              <van-field
                v-model="formData.supplierName"
                name="推荐人供应商名称"
                label="推荐人供应商名称"
                placeholder="请输入供应商名称"
                label-width="40%"
                :rules="[{ required: true, message: '请输入供应商名称' }]"
              />
              <van-field
                v-model="formData.supplierContactName"
                name="推荐联系人姓名"
                label="推荐联系人姓名"
                placeholder="请输入姓名"
                label-width="40%"
                :rules="[{ required: true, message: '请输入联系人姓名' }]"
              />
              <van-field
                v-model="formData.supplierContactPhone"
                maxlength="11"
                name="推荐联系人手机号"
                label="推荐联系人手机号"
                placeholder="请输入手机号码"
                label-width="40%"
                :rules="phoneRules"
              />
            </van-form>
          </div>

          <footer class="sr-form-footer">
            <div class="sr-notice">
              <p class="sr-notice-title">说明:</p>
              <p class="sr-notice-text">如有疑问请联系商务合作负责人员</p>
              <p class="sr-notice-contact">尚哲聪：18612261095</p>
            </div>
            <div class="sr-button-group">
              <WoButton size="medium" @click="handleCancel">
                取消
              </WoButton>
              <WoButton
                type="gradient"
                size="medium"
                :loading="submitLoading"
                @click="handleSubmit"
              >
                提交
              </WoButton>
            </div>
          </footer>
        </section>
      </van-tab>

      <van-tab title="已推荐">
        <section class="sr-list-section">
          <div v-if="hasSupplierList" class="sr-list-wrapper">
            <article
              v-for="item in supplierList"
              :key="item.id"
              class="sr-item-card"
            >
              <div :class="getStatusClass(item.status)">
                {{ getStatusText(item.status) }}
              </div>
              <dl class="sr-item-details">
                <div class="sr-detail-row">
                  <dt>推荐供应商名称</dt>
                  <dd>{{ item.supplierName }}</dd>
                </div>
                <div class="sr-detail-row">
                  <dt>供应商联系人姓名</dt>
                  <dd>{{ item.supplierContactName }}</dd>
                </div>
                <div class="sr-detail-row">
                  <dt>供应商联系人手机号</dt>
                  <dd>{{ item.supplierContactPhone }}</dd>
                </div>
                <div class="sr-detail-row">
                  <dt>推荐人姓名</dt>
                  <dd>{{ item.recommendName }}</dd>
                </div>
                <div class="sr-detail-row">
                  <dt>推荐人手机号</dt>
                  <dd>{{ item.recommendPhone }}</dd>
                </div>
                <div class="sr-detail-row">
                  <dt>推荐日期</dt>
                  <dd>{{ item.createTime }}</dd>
                </div>
              </dl>
            </article>

            <footer class="sr-list-footer">
              <span class="sr-footer-title">说明:</span>
              <span class="sr-footer-text">如有疑问请联系商务合作负责人员</span>
              <span class="sr-footer-contact">尚哲聪：18612261095</span>
            </footer>
          </div>

          <div v-else class="sr-empty-state">
            <img
              src="./assets/empty.png"
              alt="暂无数据"
              width="210"
              loading="lazy"
            />
            <div class="sr-empty-content">
              <span class="sr-empty-title">暂无推荐记录</span>
              <span class="sr-empty-text">如有疑问请联系商务合作负责人员</span>
              <span class="sr-empty-contact">尚哲聪：18612261095</span>
            </div>
          </div>
        </section>
      </van-tab>
    </van-tabs>
  </div>
</template>
<script setup>
import { ref, reactive, computed, onMounted, onUnmounted, nextTick } from 'vue'
import { showToast, showLoadingToast, closeToast } from 'vant'
import { debounce } from 'lodash-es'
import { isUnicom } from 'commonkit'
import { addSupplier, getSupplierList } from '@/api/interface/digitalVillage'
import { useRouter } from 'vue-router'
import WoButton from '@/components/WoElementCom/WoButton/WoButton.vue'

const router = useRouter()

const activeTab = ref(0)
const supplierList = ref([])
const submitLoading = ref(false)
const formRef = ref()

const formData = reactive({
  supplierName: '',
  supplierContactName: '',
  supplierContactPhone: '',
  recommendName: '',
  recommendPhone: ''
})

const hasSupplierList = computed(() => supplierList.value.length > 0)

const phoneRules = [
  { required: true, message: '请输入手机号码' },
  { pattern: /^1\d{10}$/, message: '请输入正确的手机号码' }
]

const getStatusClass = (status) => {
  return status === '1' ? 'sr-status-recommended' : 'sr-status-contacted'
}

const getStatusText = (status) => {
  return status === '1' ? '已推荐' : '已联系'
}

const handleTabChange = (index) => {
  if (index === 1) {
    loadSupplierList()
  }
}

const resetForm = () => {
  Object.keys(formData).forEach(key => {
    formData[key] = ''
  })
}

// 防抖优化的提交处理
const handleSubmit = debounce(async () => {
  if (submitLoading.value) return

  try {
    await formRef.value?.validate()
  } catch (error) {
    return
  }

  submitLoading.value = true

  try {
    const [err] = await addSupplier({ ...formData })

    if (!err) {
      showToast('添加成功')
      resetForm()
      activeTab.value = 1
      await nextTick()
      await loadSupplierList()
    }
  } catch (error) {
    console.error('提交失败:', error)
  } finally {
    submitLoading.value = false
  }
}, 300)

const handleCancel = () => {
  router.back()
}

const loadSupplierList = async () => {
  if (!isUnicom) {
    showLoadingToast()
  }

  try {
    const [err, json] = await getSupplierList({})
    if (!err) {
      supplierList.value = json || []
    }
  } catch (error) {
    console.error('获取供应商列表失败:', error)
    supplierList.value = []
  } finally {
    if (!isUnicom) {
      closeToast()
    }
  }
}

onMounted(() => {
  loadSupplierList()
})

onUnmounted(() => {
  closeToast()
})
</script>
<style lang='less' scoped>
.sr-container {
  :deep(.van-tab) {
    font-size: 15px;
    line-height: 20px;
    cursor: pointer;
  }

  :deep(.van-cell) {
    color: #323233;
    font-size: 16px;
    padding: 16px 8px;
  }

  .sr-add-section {
    background-color: #F8F9FA;
    min-height: calc(100vh - 44px);
    display: flex;
    flex-direction: column;
  }

  .sr-form-wrapper {
    flex: 1;
    background-color: #FFFFFF;
  }

  .sr-form-footer {
    background-color: #FFFFFF;
    margin-top: 20px;
  }

  .sr-notice {
    padding: 17px;
    margin-bottom: 20px;

    .sr-notice-title {
      font-size: 13px;
      color: #4A5568;
      line-height: 1.5;
      font-weight: 400;
      margin: 0 0 8px 0;
    }

    .sr-notice-text,
    .sr-notice-contact {
      font-size: 13px;
      color: #4A5568;
      line-height: 21.5;
      font-weight: 400;
      margin: 0 0 4px 0;
    }
  }

  .sr-button-group {
    display: flex;
    justify-content: space-around;
    padding: 0 10px 20px;
    gap: 16px;

    :deep(.wo-button) {
      flex: 1;
      max-width: 120px;
    }
  }

  .sr-list-section {
    background-color: #F8F9FA;
    min-height: calc(100vh - 44px);
  }

  .sr-list-wrapper {
    padding: 1px 0;
  }

  .sr-item-card {
    background: #FFFFFF;
    box-shadow: 0 4px 16px 0 rgba(0, 0, 0, 0.07);
    border-radius: 4px;
    margin: 20px 17px;
    position: relative;
    padding: 50px 20px 20px;
  }

  .sr-status-recommended {
    background: var(--wo-biz-theme-gradient-3);
    border-radius: 50px 0 0 50px;
    height: 22px;
    width: 58px;
    font-size: 13px;
    color: #FFFFFF;
    text-align: center;
    font-weight: 400;
    line-height: 22px;
    position: absolute;
    top: 17px;
    right: 0;
  }

  .sr-status-contacted {
    background: linear-gradient(45deg, #09acf1 0%, #4c73ff 100%);
    border-radius: 50px 0 0 50px;
    height: 22px;
    width: 58px;
    font-size: 13px;
    color: #FFFFFF;
    text-align: center;
    font-weight: 400;
    line-height: 22px;
    position: absolute;
    top: 17px;
    right: 0;
  }

  .sr-item-details {
    margin: 0;
  }

  .sr-detail-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;

    &:last-child {
      margin-bottom: 0;
    }

    dt {
      font-size: 14px;
      color: #4A5568;
      font-weight: 400;
      margin: 0;
    }

    dd {
      font-size: 14px;
      color: #4A5568;
      font-weight: 400;
      margin: 0;
      text-align: right;
      max-width: 60%;
      word-break: break-all;
    }
  }

  .sr-list-footer {
    display: flex;
    flex-direction: column;
    padding: 0 17px 20px;

    .sr-footer-title,
    .sr-footer-text,
    .sr-footer-contact {
      font-size: 14px;
      color: #718096;
      line-height: 1.5;
      margin-bottom: 8px;

      &:last-child {
        margin-bottom: 0;
      }
    }
  }

  .sr-empty-state {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 50px 20px;
    min-height: 50vh;

    img {
      margin-bottom: 20px;
    }
  }

  .sr-empty-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;

    .sr-empty-title {
      font-size: 16px;
      color: #718096;
      margin-bottom: 8px;
      font-weight: 500;
    }

    .sr-empty-text,
    .sr-empty-contact {
      font-size: 14px;
      color: #718096;
      line-height: 1.5;
      margin-bottom: 4px;

      &:last-child {
        margin-bottom: 0;
      }
    }
  }
}
</style>
