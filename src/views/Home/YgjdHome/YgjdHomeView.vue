<!--
  员工京东业务首页组件
  功能：展示员工京东商城首页内容，包括轮播图、菜单网格、热门商品推荐、商品瀑布流
  特性：支持热门商品推荐区域、商品池切换、瀑布流加载、骨架屏展示
  业务：专为员工京东群体提供的商品购买平台
-->
<template>
  <!-- 员工家庭首页布局容器 -->
  <BaseHomeLayout
    home-class="ygjd-home"
    search-placeholder="搜索商品"
    :header-banner-list="headerBannerList"
    :grid-menu-items="gridMenuItems"
    :grid-columns="5"
    :skeleton-states="skeletonStates"
    @search="handleSearch"
    @banner-click="handleBannerClick"
    @grid-item-click="handleGridItemClick"
    @more-click="handleMoreClick"
  >
    <template #additional-content>
      <!-- 热门商品推荐区域 -->
      <div
        v-if="skeletonStates.recommend || hotGoods.length > 0"
        class="home-recommend-container"
      >
        <transition name="skeleton-fade" mode="out-in">
          <!-- 推荐区域骨架屏 -->
          <RecommendSkeleton
            v-if="skeletonStates.recommend"
            key="recommend-skeleton"
          />
          <!-- 热门商品推荐内容 -->
          <RecommendView
            v-else-if="hotGoods.length > 0"
            key="recommend-content"
            :hotGoods="hotGoods"
          />
        </transition>
      </div>
    </template>

    <template #main-content>
      <!-- 商品瀑布流区域 -->
      <WaterfallSection
        class="ygjd-waterfall-section"
        :waterfall-goods-list="waterfallGoodsList"
        :waterfall-loading="waterfallLoading"
        :waterfall-finished="waterfallFinished"
        :waterfall-button-can-show="waterfallButtonCanShow"
        :waterfall-render-complete="waterfallRenderComplete"
        :skeleton-states="{ waterfall: skeletonStates.waterfall }"
        @goods-click="handleGoodsClick"
        @load-more="handleWaterfallLoadMore"
        @after-render="handleWaterfallAfterRender"
      />
    </template>
  </BaseHomeLayout>
</template>
<script setup>
import { ref, onMounted, onUnmounted } from 'vue'
import { closeToast, showLoadingToast } from 'vant'

// 组件导入
import BaseHomeLayout from '@views/Home/components/BaseHomeLayout.vue'
import WaterfallSection from '@components/GoodsListCommon/WaterfallSection.vue'
import RecommendView from "@views/Home/components/RecommendView.vue"
import RecommendSkeleton from '@views/Home/components/Skeleton/RecommendSkeleton.vue'

// 组合式函数导入
import { useHomeData } from '@views/Home/composables/useHomeData.js'
import { useHomeNavigation } from '@views/Home/composables/useHomeNavigation.js'

// API 和工具函数导入
import { getHotGoods } from '@/api/interface/digitalVillage'
import { getBizCode } from '@/utils/curEnv'
import { curChannelBiz } from '@/utils/storage'
import { isUnicom } from 'commonkit'

// 首页数据管理：获取轮播图、菜单、商品列表等数据和状态
const {
  headerBannerList,
  gridMenuItems,
  skeletonStates,
  moduleDataReady,
  waterfallGoodsList,
  waterfallLoading,
  waterfallFinished,
  waterfallButtonCanShow,
  waterfallRenderComplete,
  getHeaderBannerList,
  getIconList,
  getWaterfallList,
  resetWaterfallState,
  getPartionListData,
  hideSkeletonInOrder
} = useHomeData()

// 首页导航管理：处理各种点击事件和页面跳转
const {
  handleGoodsClick,
  handleBannerClick,
  handleGridItemClick,
  handleMoreClick,
  handleSearch
} = useHomeNavigation()

// 商品分区数据管理
const typeList = ref([])
const goodsPoolIdSelected = ref('')

// 热门商品推荐数据管理
const hotGoods = ref([])

// 扩展骨架屏状态：添加推荐区域骨架屏
skeletonStates.value = {
  ...skeletonStates.value,
  recommend: true
}

// 扩展模块就绪状态：添加推荐区域模块状态
moduleDataReady.value = {
  ...moduleDataReady.value,
  recommend: false
}

// 瀑布流渲染完成处理：标记渲染状态完成
const handleWaterfallAfterRender = () => {
  waterfallRenderComplete.value = true
}

// 瀑布流加载更多处理：加载下一页商品数据
const handleWaterfallLoadMore = () => {
  getWaterfallList(goodsPoolIdSelected.value, '', true)
}

// 商品池切换处理：重置状态并加载新的商品数据
const changeGoodsPool = (id, sortType = '') => {
  goodsPoolIdSelected.value = id
  resetWaterfallState()
  getWaterfallList(id, sortType, false)
}

// 热门商品数据初始化：获取员工家庭热门商品推荐
const initHotGoodsData = async () => {
  const params = {
    showPage: '1',
    bizCode: getBizCode('GOODS'),
    channel: curChannelBiz.get()
  }

  // 非联通渠道显示加载提示
  if (!isUnicom) {
    showLoadingToast()
  }

  const [err, json] = await getHotGoods(params)

  if (!isUnicom) {
    closeToast()
  }

  if (err) {
    console.error('获取热门商品失败:', err.msg)
    moduleDataReady.value.recommend = true
    await hideSkeletonInOrder(['banner', 'gridMenu', 'recommend'])
    return
  }

  // 设置热门商品数据
  const hotGoodsData = json || []
  hotGoods.value = hotGoodsData

  // 标记推荐模块数据就绪并按顺序隐藏骨架屏
  moduleDataReady.value.recommend = true
  await hideSkeletonInOrder(['banner', 'gridMenu', 'recommend'])
}

// 页面数据初始化：获取商品分区列表并加载默认商品池
const initPageData = async () => {
  const partionList = await getPartionListData(2)
  typeList.value = partionList

  if (typeList.value.length > 0) {
    const defaultPartition = typeList.value[0]
    goodsPoolIdSelected.value = defaultPartition.id
    changeGoodsPool(defaultPartition.id)
  }
}

// 组件挂载时初始化：并行加载基础数据、热门商品和页面数据
onMounted(() => {
  getHeaderBannerList()
  getIconList(7) // 员工家庭首页使用 showPage: 7
  initHotGoodsData()
  initPageData()
})

// 组件卸载时清理：预留清理逻辑
onUnmounted(() => {
  // 清理工作预留
})
</script>

<style scoped lang="less">
.ygjd-home {
  //padding: 10px;
  //box-sizing: border-box;

  .home-recommend-container {
    padding: 0 10px;
    position: relative;
    box-sizing: border-box;
  }

  .ygjd-waterfall-section {
    :deep(.home-waterfall-container) {
      padding: 0 10px;
    }
  }
}

.skeleton-fade-enter-active,
.skeleton-fade-leave-active {
  transition: opacity 0.3s ease;
}

.skeleton-fade-enter-from,
.skeleton-fade-leave-to {
  opacity: 0;
}

.skeleton-fade-enter-to,
.skeleton-fade-leave-from {
  opacity: 1;
}
</style>
