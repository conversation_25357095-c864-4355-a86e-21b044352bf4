<!--
/**
 * 客户经理底部导航标签组件
 *
 * 主要功能：
 * 1. 提供企业采购信息和本省商品两个主要功能模块的切换导航
 * 2. 支持双向数据绑定，实现父子组件间的状态同步
 * 3. 集成路由导航功能，点击标签自动跳转到对应页面
 * 4. 提供图标状态切换，根据激活状态显示不同的图标样式
 * 5. 支持路由监听，当路由变化时自动更新标签激活状态
 *
 * 技术特点：
 * - 使用Vant UI的Tabbar组件实现底部导航
 * - 采用配置化设计，便于扩展和维护
 * - 集成Vue Router，实现页面路由跳转
 * - 支持props验证，确保传入参数的有效性
 * - 实现双向绑定和事件发射机制
 *
 * 使用场景：
 * - 客户经理模块的底部导航栏
 * - 需要在企业管理和商品管理间切换的场景
 * - 移动端底部固定导航的实现
 */
-->

<template>
  <!-- 底部导航容器 -->
  <nav class="tab-nav">
    <!-- 使用Vant Tabbar组件实现底部导航 -->
    <!-- 固定在底部，双向绑定激活状态，监听切换事件 -->
    <van-tabbar fixed v-model:active="activeTab" @change="handleTabChange">
      <!-- 遍历标签配置列表，为每个标签创建导航项 -->
      <van-tabbar-item v-for="tab in tabList" :key="tab.name" :name="tab.name">
        <!-- 自定义图标插槽 -->
        <template #icon>
          <!-- 图标包装容器，确保图标居中显示 -->
          <div class="tab-nav__icon-wrapper">
            <!-- 根据激活状态显示对应的图标 -->
            <img :src="getTabIcon(tab)" :alt="tab.label" class="tab-nav__icon" />
          </div>
        </template>
        <!-- 标签文本，根据激活状态应用不同样式 -->
        <span class="tab-nav__text" :class="{ 'tab-nav__text--active': activeTab === tab.name }">{{ tab.label }}</span>
      </van-tabbar-item>
    </van-tabbar>
  </nav>
</template>

<script setup>
import { computed, ref, watch, toRefs } from 'vue'
import { useRouter, useRoute } from 'vue-router'

// 导入标签图标资源
import tabEnterprise1 from '../assets/tab-enterprise1.png'
import tabEnterprise2 from '../assets/tab-enterprise2.png'
import tabGoods1 from '../assets/tab-goods1.png'
import tabGoods2 from '../assets/tab-goods2.png'

// ==================== 标签配置定义 ====================
// 标签配置对象，定义每个标签的基本信息和路由
const TAB_CONFIG = {
  // 企业采购信息标签配置
  enterprise: {
    name: 'enterprise',                           // 标签唯一标识
    label: '企业采购信息',                        // 显示文本
    route: '/zq/manager/enterprise-list',         // 对应路由路径
    activeIcon: tabEnterprise2,                   // 激活状态图标
    inactiveIcon: tabEnterprise1                  // 非激活状态图标
  },
  // 本省商品标签配置
  goods: {
    name: 'goods',                                // 标签唯一标识
    label: '本省商品',                           // 显示文本
    route: '/zq/manager/goods-list',              // 对应路由路径
    activeIcon: tabGoods2,                        // 激活状态图标
    inactiveIcon: tabGoods1                       // 非激活状态图标
  }
}

// ==================== 组件属性定义 ====================
// 定义组件接收的props参数
const props = defineProps({
  // 当前激活的标签名称
  currentTab: {
    type: String,
    default: 'enterprise',
    // 验证传入值必须是有效的标签名称
    validator: (value) => ['enterprise', 'goods'].includes(value)
  }
})

// 使用toRefs解构props，保持响应性
const { currentTab } = toRefs(props)

// 定义组件向父组件发射的事件
const emit = defineEmits(['update:currentTab', 'tab-change'])

// ==================== 路由管理 ====================
// 获取路由器实例，用于页面跳转
const router = useRouter()

// 获取当前路由信息，用于监听路由变化
const route = useRoute()

// ==================== 标签状态管理 ====================
// 内部激活标签状态，用于管理当前选中的标签
const activeTab = ref(currentTab.value)

// 监听props中currentTab的变化，同步到内部状态
// 确保父组件传入的值能正确更新组件状态
watch(currentTab, (newTab) => {
  activeTab.value = newTab
}, { immediate: true })

// 监听路由变化，自动更新标签激活状态
// 当用户通过其他方式导航时，确保标签状态与路由同步
watch(() => route.path, (newPath) => {
  // 查找与当前路径匹配的标签配置
  const matchedTab = Object.values(TAB_CONFIG).find(tab => tab.route === newPath)
  
  // 如果找到匹配的标签且与当前激活标签不同，则更新状态
  if (matchedTab && matchedTab.name !== activeTab.value) {
    activeTab.value = matchedTab.name
  }
}, { immediate: true })

// ==================== 计算属性和工具函数 ====================
// 计算属性：获取标签列表
// 将配置对象转换为数组，便于模板遍历
const tabList = computed(() => Object.values(TAB_CONFIG))

// 获取标签图标的工具函数
// 根据标签是否激活返回对应的图标资源
const getTabIcon = (tab) => {
  return activeTab.value === tab.name ? tab.activeIcon : tab.inactiveIcon
}

// ==================== 事件处理 ====================
// 标签切换事件处理函数
// 当用户点击标签时触发，处理状态更新和路由跳转
const handleTabChange = (tabName) => {
  // 如果点击的是当前激活标签，不执行任何操作
  if (tabName === activeTab.value) return

  // 更新内部激活状态
  activeTab.value = tabName

  // 向父组件发射事件，实现双向绑定
  emit('update:currentTab', tabName)
  emit('tab-change', tabName)

  // 获取标签对应的路由配置
  const tabConfig = TAB_CONFIG[tabName]
  
  // 如果配置了路由且与当前路由不同，则进行页面跳转
  if (tabConfig?.route && route.path !== tabConfig.route) {
    router.push(tabConfig.route)
  }
}
</script>

<style lang="less" scoped>
.tab-nav {
  width: 100%;

  &__icon-wrapper {
    display: flex;
    justify-content: center;
    align-items: center;
  }

  &__icon {
    width: 22px;
    height: 22px;
    object-fit: contain;
  }

  &__text {
    margin: 4px 0 8px;
    font-size: 11px;
    color: #718096;
    transition: color 0.2s ease;

    &--active {
      color: var(--wo-biz-theme-color);
      font-weight: 500;
    }
  }
}
</style>
