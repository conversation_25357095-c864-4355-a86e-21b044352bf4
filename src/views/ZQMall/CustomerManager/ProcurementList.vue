<!--
采购订单列表组件

主要功能：
- 展示企业客户的采购订单列表，支持按订单状态筛选
- 提供订单详情展示，包括商品信息、订单状态、物流信息等
- 支持订单状态管理，包括确认收货等操作
- 集成物流查询功能，可查看订单包裹物流信息
- 支持分页加载，提供无限滚动体验

技术特性：
- 使用 Vue 3 Composition API 进行状态管理
- 集成 Vant UI 组件库提供移动端优化体验
- 支持懒加载图片优化性能
- 响应式设计适配不同屏幕尺寸
- 实时订单状态更新和物流信息查询

使用场景：
- 企业管理员查看和管理客户采购订单
- 订单状态跟踪和确认收货操作
- 物流信息查询和订单详情查看
-->
<template>
  <div class="procurement-list">
    <!-- 订单状态筛选标签页 -->
    <!-- 提供待确认、已确认、其他、全部四种状态筛选 -->
    <Tabs v-model="curOrderStateIndex" class="procurement-list__tabs">
      <Tab v-for="item in orderStateList" :key="item.id" :title="item.name" />
    </Tabs>

    <!-- 占位空间，避免固定定位的标签页遮挡内容 -->
    <div class="procurement-list__spacer" />

    <!-- 订单列表容器 -->
    <!-- 支持下拉刷新和上拉加载更多功能 -->
    <List
      v-model="loading"
      :finished="finished"
      finished-text="没有更多了"
      class="procurement-list__content"
      @load="handleLoad"
    >
      <!-- 单个订单项 -->
      <!-- 展示订单的完整信息包括供应商、商品、状态等 -->
      <article
        v-for="item in orderList"
        :key="item.id"
        class="order-item"
      >
        <!-- 订单头部：供应商信息 -->
        <header class="order-item__header">
          <span class="order-item__logo" />
          <h3 class="order-item__title">{{ item.supplier.name }}</h3>
        </header>

        <!-- 物流信息区域 -->
        <!-- 仅在订单状态为待签收或已签收时显示 -->
        <div
          v-if="shouldShowExpress(item)"
          class="order-item__express"
          @click="handleExpressClick(item)"
        >
          {{ getExpressText(item) }}
        </div>

        <!-- 商品列表 -->
        <!-- 展示订单中的所有商品信息 -->
        <div
          v-for="(orderItem, index) in item.skuNumInfoList"
          :key="index"
          class="order-item__product"
        >
          <!-- 商品图片，使用懒加载优化性能 -->
          <img
            v-lazy="orderItem.sku.detailImageUrl[0]"
            class="order-item__image"
            :alt="orderItem.sku.name"
          />
          <!-- 商品描述信息 -->
          <div class="order-item__desc">
            <h4 class="order-item__name">{{ orderItem.sku.name }}</h4>
            <p class="order-item__specs">{{ orderItem.sku.param }}</p>
          </div>
          <!-- 商品数量 -->
          <span class="order-item__count">x{{ orderItem.skuNum }}</span>
        </div>

        <!-- 订单底部信息 -->
        <footer class="order-item__footer">
          <!-- 订单编号 -->
          <p class="order-item__info">商品采购单号：{{ item.bizOrderId }}</p>

          <!-- 订单状态信息容器 -->
          <div class="order-item__status-wrapper">
            <!-- 左侧：订单状态和下单时间 -->
            <div class="order-item__left">
              <!-- 订单状态标签 -->
              <span
                v-if="orderStateFormat(item.orderState)"
                :class="getStateClass(item.orderState)"
                class="order-item__state"
              >
                {{ orderStateFormat(item.orderState) }}
              </span>
              <!-- 下单时间 -->
              <p class="order-item__info">下单时间：{{ formatDate(item.orderDate) }}</p>
            </div>

            <!-- 分隔线，仅在已签收状态显示 -->
            <div v-if="item.orderState === '9'" class="order-item__divider" />

            <!-- 右侧：收货确认状态（仅已签收订单显示） -->
            <div v-if="shouldShowReceiveStatus(item)" class="order-item__right">
              <!-- 收货确认状态标签 -->
              <span :class="getReceiveStateClass(item)" class="order-item__state">
                {{ getReceiveStateText(item) }}
              </span>
              <!-- 确认时间（仅已确认时显示） -->
              <p v-if="item.isAck === '1'" class="order-item__info">
                确认时间：{{ formatDate(item.ackTime) }}
              </p>
            </div>
          </div>
        </footer>

        <!-- 操作按钮区域 -->
        <!-- 仅在已签收但未确认收货时显示确认按钮 -->
        <div v-if="shouldShowConfirmBtn(item)" class="order-item__actions">
          <button class="order-item__btn" @click="handleConfirmReceive(item)">
            确认客户已收货
          </button>
        </div>
      </article>
    </List>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { Tab, Tabs, List } from 'vant'
import dayjs from 'dayjs'
import { getOrderList, getOrderExpress } from '@/api/interface/order'
import { managerConfirmReceive } from '@/api/interface/zq'
import { getBizCode } from '@/utils/curEnv'

// ==================== 常量定义 ====================
// 订单状态映射表
// 将数字状态码转换为用户友好的中文描述
const ORDER_STATE_MAP = {
  '2': '客户已取消',
  '3': '客户待发货',
  '4': '客户待发货',
  '5': '客户待签收',
  '9': '客户已签收'
}

// ==================== 路由和基础数据 ====================
// 获取当前路由实例，用于读取查询参数
const route = useRoute()
// 获取路由器实例，用于页面跳转
const router = useRouter()

// 从路由查询参数中获取企业代码
const ciCode = route.query.ciCode

// 订单状态筛选选项列表
// 提供待确认、已确认、其他、全部四种筛选状态
const orderStateList = [
  { id: '20', name: '待确认' },
  { id: '21', name: '已确认' },
  { id: '22', name: '其他' },
  { id: '', name: '全部' }
]

// ==================== 响应式状态管理 ====================
// 当前选中的订单状态索引，默认选择"全部"（索引3）
const curOrderStateIndex = ref(3)
// 当前页码，用于分页加载
const currentPage = ref(1)
// 订单列表数据
const orderList = ref([])
// 加载状态标识
const loading = ref(false)
// 是否已加载完所有数据
const finished = ref(false)

// ==================== 计算属性 ====================
// 当前选中的订单状态ID
// 根据当前选中的索引获取对应的状态ID
const curOrderState = computed(() =>
  orderStateList[curOrderStateIndex.value].id
)

// ==================== 工具函数 ====================
// 日期格式化函数
// 将时间戳或日期字符串格式化为 YYYY-MM-DD 格式
const formatDate = (date) => dayjs(date).format('YYYY-MM-DD')

// 订单状态格式化函数
// 根据状态码返回对应的中文描述
const orderStateFormat = (state) => ORDER_STATE_MAP[state] || ''

// 判断是否显示物流信息
// 仅在订单状态为"待签收"或"已签收"时显示
const shouldShowExpress = (item) =>
  item.orderState === '5' || item.orderState === '9'

// 获取物流信息显示文本
// 根据包裹数量生成相应的提示文本
const getExpressText = (item) => {
  if (!item.orderExpress?.orderPackageList) return '包裹查询中...'
  const packageCount = item.orderExpress.orderPackageList.length
  return packageCount > 1
    ? `该订单已拆分${packageCount}个包裹发出，点击可查看物流`
    : '该订单包裹发出，点击可查看物流'
}

// 判断是否显示收货确认状态
// 仅在订单状态为"已签收"时显示
const shouldShowReceiveStatus = (item) => item.orderState === '9'

// 判断是否显示确认收货按钮
// 仅在订单已签收但未确认收货时显示
const shouldShowConfirmBtn = (item) =>
  item.orderState === '9' && item.isAck === '0'

// 获取订单状态样式类名
// 根据订单状态生成对应的CSS类名
const getStateClass = (state) => `order-item__state--${state}`

// 获取收货确认状态样式类名
// 根据确认状态生成对应的CSS类名
const getReceiveStateClass = (item) =>
  `order-item__state--${item.isAck === '1' ? '9' : '3'}`

// 获取收货确认状态文本
// 根据确认状态返回对应的中文描述
const getReceiveStateText = (item) =>
  item.isAck === '1' ? '已确认客户收货' : '待确认客户收货'

// ==================== 数据获取 ====================
// 获取订单列表数据
// 支持分页加载和状态筛选
const fetchOrderList = async () => {
  loading.value = true
  try {
    // 构建请求参数
    const params = {
      disriBiz: getBizCode('ORDER'),
      orderState: curOrderState.value,
      pageNum: currentPage.value,
      pageSize: 10,
      accountInfo: {
        role: '2',
        enterpriseCode: ciCode
      }
    }

    // 调用订单列表API
    const [err, json] = await getOrderList(params)
    if (err) {
      console.error('获取订单列表失败:', err.msg)
      return
    }

    // 追加新数据到现有列表
    orderList.value = [...orderList.value, ...json.list]

    // 为需要显示物流信息的订单获取物流数据
    orderList.value.forEach(async (item) => {
      if (shouldShowExpress(item)) {
        const [expressErr, expressJson] = await getOrderExpress(item.id, '2')
        if (!expressErr) {
          item.orderExpress = expressJson
        }
      }
    })

    // 更新分页状态
    currentPage.value++
    if (json.list.length < 10) {
      finished.value = true
    }
  } finally {
    loading.value = false
  }
}

// 重置列表并重新获取数据
// 用于状态切换时的数据刷新
const resetAndFetch = () => {
  currentPage.value = 1
  orderList.value = []
  finished.value = false
  fetchOrderList()
}

// ==================== 事件处理 ====================
// 处理列表加载更多事件
const handleLoad = () => fetchOrderList()

// 处理物流信息点击事件
// 跳转到物流详情页面
const handleExpressClick = (item) => {
  router.push({
    name: 'zq-user-order-entry-express',
    query: { orderId: item.id, roleType: '2' },
    params: { orderExpress: item.orderExpress }
  })
}

// 处理确认收货按钮点击事件
// 调用API确认客户收货并更新订单状态
const handleConfirmReceive = async (item) => {
  const [err, json] = await managerConfirmReceive(item.id)
  if (err) {
    console.error('确认收货失败:', err.msg)
    return
  }

  // 更新订单的确认状态和时间
  item.ackTime = json.actTime
  item.isAck = json.isAck
}

// ==================== 监听器 ====================
// 监听订单状态切换，自动重新获取数据
watch(curOrderStateIndex, resetAndFetch)
</script>

<style lang="less" scoped>
.procurement-list {
  background-color: #F8F9FA;

  &__tabs {
    position: fixed;
    left: 0;
    right: 0;
    top: 0;
    z-index: 2;
  }

  &__spacer {
    height: 50px;
  }

  &__content {
    padding: 10px;
    min-height: calc(100vh - 50px);
  }
}

.order-item {
  margin-bottom: 10px;
  padding: 15px 12px;
  background-color: #FFFFFF;
  border-radius: 10px;

  &__header {
    display: flex;
    align-items: center;
  }

  &__logo {
    width: 20px;
    height: 20px;
    background-image: url('./assets/enterprise-logo.png');
    background-size: 100%;
    flex-shrink: 0;
  }

  &__title {
    margin: 0 0 0 4px;
    line-height: 15px;
    font-size: 15px;
    color: #171E24;
    font-weight: 500;
  }

  &__express {
    margin: 13px 0;
    padding: 7px 10px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background: #F8F9FA;
    border-radius: 6px;
    font-size: 13px;
    color: #4A5568;
    font-weight: 400;
    cursor: pointer;

    &::after {
      content: '';
      width: 6px;
      height: 6px;
      border-top: 1px solid #718096;
      border-right: 1px solid #718096;
      transform: rotate(45deg);
      flex-shrink: 0;
    }
  }

  &__product {
    display: flex;
    margin-top: 14px;
  }

  &__image {
    flex-shrink: 0;
    width: 60px;
    height: 60px;
    border-radius: 6px;
    object-fit: cover;
  }

  &__desc {
    flex-grow: 1;
    margin-left: 8px;
  }

  &__name {
    margin: 0 0 4px 0;
    line-height: 18px;
    font-size: 13px;
    color: #171E24;
    font-weight: 400;
    display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
        word-break: break-all;
  }

  &__specs {
    margin: 0;
    line-height: 1.5;
    font-size: 12px;
    color: #4A5568;
    font-weight: 400;
  }

  &__count {
    flex-shrink: 0;
    min-width: 20px;
    text-align: right;
    line-height: 1.5;
    font-size: 12px;
    color: #4A5568;
    font-weight: 400;
  }

  &__footer {
    margin-top: 22px;
  }

  &__info {
    margin: 8px 0;
    font-size: 13px;
    color: #4A5568;
    font-weight: 400;
  }

  &__status-wrapper {
    display: flex;
    align-items: stretch;
  }

  &__left,
  &__right {
    display: flex;
    flex-direction: column;
    flex: 1;
    align-items: flex-start;
  }

  &__divider {
    width: 1px;
    background-color: #E2E8EE;
    margin: 4px 10px;
  }

  &__state {
    text-align: center;
    border-radius: 4px;
    font-size: 12px;
    font-weight: 400;
    display: inline-block;
    padding: 2px 4px;
    width: auto;
    max-width: fit-content;
    min-width: unset;
    flex: 0 0 auto;

    &--2 {
      background: #F8F9FA;
      color: #718096;
    }

    &--3,
    &--4,
    &--5 {
      background: #fff7f0;
      color: var(--wo-biz-theme-color);
    }

    &--9 {
      background: #F8F9FA;
      color: #10B981;
    }
  }

  &__actions {
    display: flex;
    justify-content: center;
    margin-top: 6px;
  }

  &__btn {
    width: 180px;
    height: 36px;
    background: var(--wo-biz-theme-gradient-1);
    border-radius: 18px;
    border: 0;
    font-size: 15px;
    color: #FFFFFF;
    text-align: center;
    font-weight: 400;
    cursor: pointer;
  }
}
</style>

<style lang="less">
.procurement-list {
  .van-tabs--line .van-tabs__wrap {
    height: 50px;
  }

  .van-tab {
    color: #171E24;
    font-weight: 500;
  }

  .van-tabs__line {
    background: var(--wo-biz-theme-color);
    border-radius: 4px;
    width: 60px;
  }
}
</style>
