<!--
/**
 * 企业采购信息列表页面组件
 *
 * 主要功能：
 * 1. 展示客户经理管辖范围内的企业列表信息
 * 2. 提供企业名称搜索功能，支持实时过滤和回车搜索
 * 3. 支持点击企业项目跳转到对应的采购详情页面
 * 4. 集成底部导航标签，提供模块间切换功能
 * 5. 处理空状态显示，区分无数据和搜索无结果两种情况
 *
 * 技术特点：
 * - 使用Vue 3 Composition API实现响应式数据管理
 * - 采用计算属性实现实时搜索过滤功能
 * - 集成Vue Router实现页面路由跳转
 * - 使用Vant UI组件库提供统一的视觉体验
 * - 响应式布局设计，适配移动端显示
 *
 * 使用场景：
 * - 客户经理查看管辖企业列表
 * - 快速搜索定位特定企业信息
 * - 进入企业采购详情页面的入口
 */
-->

<template>
  <!-- 企业列表页面容器 -->
  <div class="enterprise-list">
    <!-- 搜索区域 -->
    <div class="enterprise-list__search">
      <!-- 搜索输入框包装器 -->
      <div class="search-wrapper">
        <!-- 企业名称搜索输入框，支持双向绑定和回车搜索 -->
        <input 
          v-model="searchValue" 
          type="text" 
          placeholder="输入企业名称" 
          class="search-input"
          @keyup.enter="handleSearch" 
        />
        <!-- 搜索按钮，点击触发搜索操作 -->
        <button class="search-btn" @click="handleSearch">搜索</button>
      </div>
    </div>

    <!-- 企业列表内容区域 -->
    <div class="enterprise-list__content">
      <!-- 遍历过滤后的企业列表，为每个企业创建可点击的卡片 -->
      <div 
        v-for="item in filteredList" 
        :key="item.ciCode" 
        class="enterprise-item"
        @click="handleEnterpriseSelect(item)"
      >
        <!-- 企业名称标题，支持多行显示和文本截断 -->
        <h3 class="enterprise-item__name">{{ item.ciName }}</h3>
        <!-- 操作区域，显示采购详情入口 -->
        <div class="enterprise-item__action">
          <span>采购详情</span>
          <!-- 右箭头图标，表示可点击进入 -->
          <van-icon name="arrow" />
        </div>
      </div>
      
      <!-- 空状态显示，根据是否有搜索条件显示不同提示 -->
      <div v-if="filteredList.length === 0" class="enterprise-list__empty">
        <van-empty :description="searchValue ? '没有找到企业' : '暂无企业数据'" />
      </div>
    </div>

    <!-- 底部导航标签组件，当前激活企业标签 -->
    <TabComponent currentTab="enterprise" />
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import TabComponent from './components/Tab.vue'
import { queryZqInfo } from '@/utils/zqInfo'

// ==================== 路由管理 ====================
// 获取路由器实例，用于页面跳转
const router = useRouter()

// ==================== 搜索功能管理 ====================
// 搜索输入框的值，用于过滤企业列表
const searchValue = ref('')

// ==================== 企业数据管理 ====================
// 存储从zqInfo获取的企业列表数据
const enterpriseList = ref([])

// 计算属性：根据搜索条件过滤企业列表
// 如果没有搜索条件则返回完整列表，否则按企业名称进行模糊匹配
const filteredList = computed(() => {
  // 如果搜索框为空，返回完整的企业列表
  if (!searchValue.value) return enterpriseList.value
  
  // 根据企业名称进行模糊搜索过滤
  return enterpriseList.value.filter(item =>
    item.ciName.includes(searchValue.value)
  )
})

// ==================== 数据获取 ====================
// 获取企业数据的函数
// 从zqInfo工具函数中获取客户经理的企业列表信息
const getEnterpriseData = () => {
  // 调用zqInfo查询函数获取客户经理信息
  const customerManagerInfo = queryZqInfo()
  
  // 提取企业列表数据，如果不存在则使用空数组
  enterpriseList.value = customerManagerInfo.ciList || []
}

// ==================== 事件处理 ====================
// 搜索按钮点击处理函数
// 实际搜索逻辑由计算属性filteredList自动处理
const handleSearch = () => {
  // 搜索逻辑由计算属性自动处理，这里可以添加额外的搜索行为
  // 例如：统计搜索次数、记录搜索日志等
}

// 企业选择处理函数
// 当用户点击企业项目时，跳转到对应的采购列表页面
const handleEnterpriseSelect = (enterprise) => {
  // 跳转到采购列表页面，传递企业代码作为查询参数
  router.push({
    path: '/zq/manager/procurement-list',
    query: { ciCode: enterprise.ciCode }
  })
}

// ==================== 生命周期管理 ====================
// 组件挂载时获取企业数据
onMounted(() => {
  getEnterpriseData()
})
</script>

<style lang="less" scoped>
.enterprise-list {
  height: 100vh;
  background-color: #FFFFFF;
  display: flex;
  flex-direction: column;

  &__search {
    position: sticky;
    top: 0;
    z-index: 10;
    padding: 10px;

    .search-wrapper {
      position: relative;
      display: flex;
      align-items: center;
      border: 1px solid var(--wo-biz-theme-color);
      border-radius: 9999px;
      padding: 1px;

      .search-input {
        flex: 1;
        height: 28px;
        padding-left: 30px;
        background: url('./assets/search.png') no-repeat 9px center;
        background-size: 16px;
        font-size: 14px;
        color: #171E24;
        border: none;
        outline: none;

        &::placeholder {
          color: #718096;
        }
      }

      .search-btn {
        position: absolute;
        right: 1px;
        width: 60px;
        height: 28px;
        line-height: 28px;
        text-align: center;
        color: #FFFFFF;
        font-size: 14px;
        font-weight: 400;
        background: var(--wo-biz-theme-gradient-2);
        border: none;
        border-radius: 50px;
        cursor: pointer;
      }
    }
  }

  &__content {
    flex: 1;
    overflow-y: auto;
    padding: 10px;
    padding-bottom: 66px;
    -webkit-overflow-scrolling: touch; scrollbar-width: none; -ms-overflow-style: none; &::-webkit-scrollbar { display: none; };

    .enterprise-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 15px;
      padding: 0 15px;
      height: 76px;
      background-color: #FFFFFF;
      border-radius: 6px;
      box-shadow: 0 0 15px 0 rgba(0, 0, 0, 0.05);
      cursor: pointer;

      &__name {
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
        word-break: break-all;
        padding-right: 15px;
        font-size: 15px;
        color: #171E24;
        font-weight: 500;
        margin: 0;
      }

      &__action {
        display: flex;
        align-items: center;
        flex-shrink: 0;
        color: #4A5568;
        font-size: 14px;

        .van-icon {
          margin-left: 4px;
        }
      }
    }
  }

  &__empty {
    padding: 40px 0;
  }
}
</style>
