<!--
/**
 * 商品详情页面组件
 *
 * 主要功能：
 * 1. 商品信息展示 - 显示商品图片轮播、基础信息、价格等核心商品数据
 * 2. 规格选择管理 - 支持多规格商品的规格选择，实时更新价格和库存
 * 3. 购买流程控制 - 处理加入购物车和立即购买的完整业务流程
 * 4. 用户权限验证 - 检查用户登录状态、白名单权限、区域销售限制
 * 5. 地址配送管理 - 根据用户地址计算配送信息和物流预测
 * 6. 营销活动展示 - 显示商品相关的营销活动和优惠信息
 * 7. 库存状态监控 - 实时检查商品库存状态和销售状态
 * 8. 分享功能支持 - 支持商品分享到微信等社交平台
 *
 * 技术特性：
 * - 使用 Composition API 进行状态管理和逻辑复用
 * - 集成 Pinia 状态管理，管理用户信息和购物车数据
 * - 支持骨架屏加载，提升用户体验
 * - 实现防抖优化，避免频繁的接口调用
 * - 支持懒加载，优化页面性能
 * - 集成地址选择和配送信息查询
 * - 支持京东商品的特殊处理逻辑
 *
 * 使用场景：
 * - 商城商品详情页面展示
 * - 支持 B2B 和 B2C 不同业务模式
 * - 适配移动端和桌面端显示
 * - 支持多供应商商品展示
 */
-->
<template>
  <div class="goods-detail">
    <!-- 页面加载状态：显示骨架屏，提升用户体验 -->
    <GoodsDetailSkeleton v-if="isLoading" />

    <!-- 商品详情主要内容区域 -->
    <div v-else class="goods-content">
      <!-- 商品图片轮播区域：支持图片预览和视频播放 -->
      <section class="image-section">
        <GoodsSwiper
          :imageList="goodsMediaList"
          :loop="true"
          :autoplay="true"
          mode="square"
          @image-click="handleImagePreview"
          @slide-change="handleSlideChange"
        />
      </section>

      <!-- 商品基础信息展示：名称、价格、描述等核心信息 -->
      <GoodsBasicInfo :goods-info="goodsInfo" />

      <!-- 商品规格选择区域：支持多规格选择和商品编码显示 -->
      <div class="spec-section">
        <div class="spec-header">
          <span class="spec-title">规格</span>
          <!-- 商品编码显示和复制功能 -->
          <div class="goods-code" v-if="currentSku.supplierSkuId">
            <span class="code-label">商品编码：</span>
            <span class="code-value">{{ currentSku.supplierSkuId }}</span>
            <img
              src="@/components/GoodsDetailCommon/assets/copy.png"
              alt="复制"
              class="copy-icon"
              width="16"
              height="16"
              @click="handleCopyCode"
            />
          </div>
        </div>

        <!-- 规格选项列表：动态渲染所有可选规格 -->
        <div class="spec-options">
          <div class="radio-wrapper" v-for="(specs, groupIndex) in displaySpecsList" :key="groupIndex">
            <!-- 规格组标题：如颜色、尺寸等 -->
            <div class="spec-group-title" v-if="specs.length > 0 && hasSpecs">
              {{ getSpecGroupName(groupIndex) }}
            </div>
            <!-- 规格选项按钮：支持选中状态和禁用状态 -->
            <button
              v-for="(spec, specIndex) in specs"
              :key="specIndex"
              :class="{
                active: specOptions.curSpecs.indexOf(spec) >= 0,
                disabled: specOptions.curDisabledSpecs.indexOf(spec) >= 0
              }"
              @click="selectSpec(spec)"
            >
              {{ removeSpecPrefix(spec) }}
            </button>
          </div>
        </div>
      </div>

      <!-- 底部操作栏占位符：为固定底部操作栏预留空间 -->
      <WoActionBarPlaceholder />

      <!-- 底部操作栏：返回首页按钮 -->
      <WoActionBar>
        <WoButton type="primary" block size="xlarge" @click="handleGoToGoodsList">
          返回首页
        </WoButton>
      </WoActionBar>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, watch, nextTick } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useUserStore } from '@store/modules/user.js'
import { useNewCartStore } from '@store/modules/newCart.js'
import {
  productIntroduction,
  queryPredictSkuPromise,
  checkSkuSale,
  setFrontCache,
  isWhiteUserLimitCheck,
  getLimitAreaList,
  getActiveList
} from '@api/interface/goods.js'
import { getBuyNowGoods, jdAddressCheck } from '@api/index.js'
import GoodsSwiper from '@components/Common/GoodsSwiper.vue'
import WoActionBarPlaceholder from '@components/WoElementCom/WoActionBarPlaceholder.vue'
import GoodsDetailSkeleton from '@components/GoodsDetailCommon/GoodsDetailSkeleton.vue'
import GoodsBasicInfo from '@components/GoodsDetailCommon/GoodsBasicInfo.vue'
import WoButton from '@components/WoElementCom/WoButton/WoButton.vue'
import WoActionBar from '@components/WoElementCom/WoActionBar.vue'
import { getBizCode } from '@utils/curEnv.js'
import { debounce } from 'lodash-es'
import { closeToast, showLoadingToast, showToast } from 'vant'
import useClipboard from 'vue-clipboard3'
import { removeSpecPrefix } from '@utils/goodsDetail.js'
import { useAlert, useGoodsDetail } from '@/composables/index.js'
import { buyProductNow, buyProductNowSession } from '@utils/storage.js'
import { urlAppend } from 'commonkit'
import { getDefaultShareUrl, shareData } from '@utils/share.js'
import { log, setWeiXinShareData, share } from 'commonkit'
import {JD_GOODS_CODE} from "@utils/types.js";

// ===================== 工具函数和组合式函数初始化 =======================
const $alert = useAlert()
const { toClipboard } = useClipboard()

// ===================== 路由和状态管理初始化 =======================
const route = useRoute()
const router = useRouter()
const goodsId = route.params.goodsId
const skuId = route.params.skuId
const userStore = useUserStore()
const newCartStore = useNewCartStore()

// ===================== 商品详情 Hook 初始化 =======================
// 使用商品详情 hook，管理商品基础数据和规格选择逻辑
const {
  spu,
  curSpecs,
  curSkuId,
  querySpu,
  querySku,
  querySpecsList,
  setSpecs,
  queryCurSpecs,
  queryDisabledSpecs,
  isSpecsComplete
} = useGoodsDetail(goodsId, skuId)

// ===================== 页面加载状态管理 =======================
// 页面整体加载状态，控制骨架屏显示
const isLoading = ref(true)
// 内容加载完成状态，控制内容区域显示
const contentLoaded = ref(false)
// 详情页错误状态，用于错误页面显示
const detailErr = ref(false)
// 数据获取完成状态，标识是否已获取到商品数据
const isDataGet = ref(false)
// 初始加载完成标识，用于控制某些只在初始化后执行的逻辑
const initialLoadComplete = ref(false)
// URL更新标识，防止重复更新URL中的skuId
const hasUpdatedUrlForSku = ref(false)

// ===================== 弹窗状态管理 =======================
// 规格选择弹窗显示状态
const showSpecPopup = ref(false)
// 地址选择弹窗显示状态
const showAddressPopup = ref(false)
// 规格弹窗操作类型：1-加入购物车，2-立即购买
const specActionType = ref(1)
// 分享菜单显示状态
const showShareMenu = ref(true)

// ===================== 商品基础信息 =======================
// 商品类型判断，是否为京东商品
const isJD = ref(false)
// 用户选择的商品数量
const goodsNum = ref(1)
// 图片预览当前索引
const previewImageIndex = ref(0)

// ===================== 商品详情数据 =======================
// 商品介绍详情数据
const productIntroductionData = ref('')
// 物流服务信息数据
const logisticsServicesInfo = ref({
  logisticsType: 1,
  returnRuleStr: '',
  predictContent: '预计48小时之内发货',
  isJD: false
})

// ===================== 商品限制和状态 =======================
// 白名单用户是否限购，true没有限购，false有限购
const limitState = ref(true)
// 所选区域暂不支持销售，true没有区域限制，false有区域限制
const regionalSalesState = ref(true)

// ===================== 营销活动数据 =======================
// 营销位类型1数据（如满减活动）
const marketTemplatesType1 = ref([])
// 营销位类型4数据（如电子券）
const marketTemplatesType4 = ref([])
// 减免价格信息
const reducePrice = ref(0)

// ===================== 组件引用 =======================
// 规格选项组件引用
const specOptionsRef = ref(null)
// 规格选择组件引用
const specSelectionRef = ref(null)
// 懒加载商品介绍组件引用
const lazyGoodsIntroduceRef = ref(null)
// 物流服务显示控制
const isShowLogisticsServices = ref(true)

// ===================== 计算属性 - 购物车相关 =======================
// 从store获取购物车数量
const cartCount = computed(() => {
  return newCartStore.countByGoods
})

// ===================== 计算属性 - 规格数据相关 =======================
// 规格数据集合，包含规格列表、当前规格、禁用规格等信息
const specsData = computed(() => {
  return {
    specsList: querySpecsList(),
    curSpecs: queryCurSpecs ? queryCurSpecs() : [],
    disabledSpecs: queryDisabledSpecs ? queryDisabledSpecs() : [],
    currentSku: currentSku.value
  }
})

// ===================== 计算属性 - 商品限制相关 =======================
// 限购逻辑计算，处理每人每次或每人总计限购
const xgObj = computed(() => {
  const limitTemplate = spu.value?.limitTemplate
  let limitText = ''
  if (limitTemplate && limitTemplate?.limitCountType) {
    switch (limitTemplate.limitCountType) {
      case '1':
        limitText = `每人每次限购${limitTemplate.limitNum}件`
        break
      case '2':
        limitText = `每人限购${limitTemplate.limitNum}件`
        break
      default:
        limitText = ''
    }
  }
  const limitNum = limitTemplate && limitTemplate?.limitNum ? limitTemplate.limitNum : 1
  return {
    isXg: spu.value?.isXg === '1',
    limitNum,
    limitText
  }
})

// 起购逻辑计算，处理最低购买数量限制
const lowestBuyObj = computed(() => {
  const lowestBuyValue = currentSku.value?.lowestBuy
  const isLowestBuy = lowestBuyValue ? parseInt(lowestBuyValue, 10) > 1 : false
  const lowestBuyNum = lowestBuyValue ? parseInt(lowestBuyValue, 10) : 1
  const lowestBuyText = lowestBuyValue ? `${lowestBuyNum}件起购` : ''

  return {
    isLowestBuy,
    lowestBuyNum,
    lowestBuyText
  }
})


// ===================== 计算属性 - 地址信息相关 =======================
// 用户地址信息计算，格式化地址数据供组件使用
const addressInfo = computed(() => {
  const curAddrInfo = userStore.curAddressInfo
  console.warn(2131331, curAddrInfo);

  return {
    provinceName: curAddrInfo.provinceName || '',
    cityName: curAddrInfo.cityName || '',
    countyName: curAddrInfo.countyName || '',
    townName: curAddrInfo.townName || '',
    addrDetail: curAddrInfo.addrDetail || '',
    receiverName: curAddrInfo.recName || '',
    receiverPhone: curAddrInfo.recPhone || ''
  }
})

// ===================== 计算属性 - SKU商品信息综合 =======================
// SKU商品信息综合计算，整合商品基础信息、限购和起购逻辑
const skuGoodsInfo = computed(() => {
  const sku = currentSku.value
  const xg = xgObj.value
  const lowestBuy = lowestBuyObj.value

  // 确定购买限制，优先级：起购 > 限购
  let purchaseLimit = 999 // 默认限制
  let purchaseLimitType = 'none'
  let purchaseLimitText = ''

  // 优先处理起购逻辑
  if (lowestBuy.isLowestBuy) {
    purchaseLimit = lowestBuy.lowestBuyNum
    purchaseLimitType = 'minimum'
    purchaseLimitText = lowestBuy.lowestBuyText
  }

  // 处理限购逻辑
  if (xg.isXg && xg.limitText) {
    purchaseLimit = xg.limitNum
    purchaseLimitType = xg.limitText.includes('每次') ? 'perTime' : 'perPerson'
    purchaseLimitText = xg.limitText
  }

  return {
    image: sku.listImageUrl || '',
    price: sku.price || 0,
    supplierSkuId: sku.supplierSkuId || '',
    stock: sku.stock || 0,
    purchaseLimit,
    purchaseLimitType,
    purchaseLimitText,
    currSku: sku,
    xgObj: xg,
    lowestBuyObj: lowestBuy
  }
})

// ===================== 工具函数 - 数据更新相关 =======================
// 防抖的异步数据更新函数，避免频繁请求京东商品信息
const debouncedUpdateGoodsInfo = debounce(async () => {
  const sku = querySku()
  if (sku && sku.supplierCode && sku.supplierCode.indexOf(JD_GOODS_CODE) > -1) {
    try {
      await Promise.all([
        queryPredictSku(),
        getProductIntroduction()
      ])
    } catch (error) {
      console.error('更新商品信息失败:', error)
    }
  }
}, 300)

// ===================== 工具函数 - 滚动位置管理 =======================
// 保存的滚动位置
let savedScrollPosition = 0

// 保存当前页面滚动位置
const saveScrollPosition = () => {
  savedScrollPosition = window.scrollY || document.documentElement.scrollTop
}

// 恢复页面滚动位置，使用requestAnimationFrame确保DOM更新完成
const restoreScrollPosition = () => {
  requestAnimationFrame(() => {
    window.scrollTo(0, savedScrollPosition)
  })
}

// ===================== 计算属性 - 商品基础数据 =======================
// 当前选中的SKU数据
const currentSku = computed(() => {
  return querySku() || {}
})

// 商品基础信息计算，包含名称、价格、图片等
const goodsInfo = computed(() => {
  const sku = currentSku.value
  if (!sku || !spu.value) {
    return {
      name: '',
      price: 0,
      originalPrice: 0,
      imageUrl: ''
    }
  }
  return {
    name: sku.name || '',
    price: sku.price || 0,
    originalPrice: sku.originalPrice || 0,
    imageUrl: sku.listImageUrl || ''
  }
})

// 当前SKU完整数据副本
const currentSKU = computed(() => {
  const sku = currentSku.value
  return {
    ...sku
  }
})

// ===================== 计算属性 - 媒体资源相关 =======================
// 商品媒体列表，处理商品详情图片数据
const goodsMediaList = computed(() => {
  const sku = currentSku.value
  if (!sku || !sku.detailImageUrl) {
    return []
  }
  return sku.detailImageUrl.map(url => ({
    type: 'image',
    url,
    alt: '商品图片'
  }))
})

// ===================== 计算属性 - 规格选择相关 =======================
// 规格选项数据计算，整合规格列表、当前选择和禁用状态
const specOptions = computed(() => {
  // 获取规格数据，参考 GoodsChoose.vue 的逻辑
  const specsList = querySpecsList()
  const curSpecs = queryCurSpecs ? queryCurSpecs() : []
  const disabledSpecs = queryDisabledSpecs ? queryDisabledSpecs() : []

  // 如果没有规格数据，返回默认规格结构
  if (!specsList || specsList.length === 0 || specsList[0].length === 0) {
    return {
      specsList: [],
      curSpecs: ['默认规格'], // 默认选中默认规格
      curDisabledSpecs: [],
    }
  }

  return {
    specsList: specsList,
    curSpecs: curSpecs,
    curDisabledSpecs: disabledSpecs,
  }
})

// 缓存的规格选项数据，在商品初始化时计算一次，避免重复计算
const cachedSpecOptions = ref([])

// 初始化规格选项数据，处理SKU列表并过滤可用选项
const initSpecOptions = () => {
  if (!spu.value || !spu.value.skuList) {
    cachedSpecOptions.value = []
    return
  }

  const disabledSpecs = queryDisabledSpecs ? queryDisabledSpecs() : []

  const options = spu.value.skuList.map(sku => {
    const isDisabled = checkSkuDisabled(sku, disabledSpecs)

    return {
      id: sku.skuId,
      name: getSkuDisplayName(sku),
      image: sku.listImageUrl,
      disabled: isDisabled,
      skuData: sku,
    }
  })

  // 只缓存可选择的SKU（过滤掉禁用的）
  cachedSpecOptions.value = options.filter(option => !option.disabled)

  console.log('初始化规格选项数据:', {
    totalOptions: options.length,
    selectableOptions: cachedSpecOptions.value.length
  })
}

// 动态计算选中状态的规格选项，实时更新选中状态
const enhancedSpecOptions = computed(() => {
  return cachedSpecOptions.value.map(option => ({
    ...option,
    selected: option.id === curSkuId.value
  }))
})

// 判断商品是否有规格选择
const hasSpecs = computed(() => {
  const specs = specOptions.value
  return specs && specs.specsList && specs.specsList.length > 0 && specs.specsList[0].length > 0
})

// 显示用的规格列表，无规格时显示默认规格
const displaySpecsList = computed(() => {
  return hasSpecs.value ? specOptions.value.specsList : [['默认规格']]
})


// ===================== 工具函数 - 规格处理相关 =======================
// 检查SKU是否被禁用，通过对比SKU规格与禁用规格列表
const checkSkuDisabled = (sku, disabledSpecs) => {
  const skuSpecs = getSkuSpecs(sku)
  return skuSpecs.some(spec => disabledSpecs.includes(spec))
}

// 获取SKU的规格数组，提取param参数并格式化
const getSkuSpecs = (sku) => {
  const { param, param1, param2, param3 } = sku
  return ['_p0_' + param, '_p1_' + param1, '_p2_' + param2, '_p3_' + param3]
    .filter(p => p !== '_p0_undefined' && p !== '_p1_undefined' &&
      p !== '_p2_undefined' && p !== '_p3_undefined')
}

// ===================== 计算属性 - 规格显示相关 =======================
// 选中规格的显示文本，包含规格名称和数量
const selectedSpec = computed(() => {
  const sku = currentSku.value
  const specsList = querySpecsList()

  // 如果没有规格数据，显示默认规格
  if (!specsList || specsList.length === 0 || specsList[0].length === 0) {
    return `默认规格 ${goodsNum.value}件`
  }

  if (!sku || !isSpecsComplete()) {
    return `默认 ${goodsNum.value}件`
  }

  const specs = []
  if (sku.param) specs.push(sku.param)
  if (sku.param1) specs.push(sku.param1)
  if (sku.param2) specs.push(sku.param2)
  if (sku.param3) specs.push(sku.param3)

  const specText = specs.join('，') || '默认'
  return `${specText} ${goodsNum.value}件`
})

// ===================== 计算属性 - 配送信息相关 =======================
// 配送信息计算，包含配送地址、预计送达、退货政策等
const deliveryInfo = computed(() => {
  const curAddrInfo = userStore.curAddressInfo
  const location = `${curAddrInfo.provinceName} ${curAddrInfo.cityName} ${curAddrInfo.countyName} ${curAddrInfo.townName || ''}`.trim()

  return {
    location: location || '配送地址',
    predictContent: logisticsServicesInfo.value.predictContent || (isJD.value ? 'JD配送' : '普通配送'),
    returnPolicy: logisticsServicesInfo.value.returnRuleStr || '7天无理由退货',
    service: '店铺售后由沃百富商城提供服务'
  }
})

// ===================== 核心函数 - 商品详情加载 =======================
// 商品详情主加载函数，处理商品数据获取、初始化和各种状态设置
const loadGoodsDetail = async () => {
  // 根据业务代码判断是否显示物流服务
  isShowLogisticsServices.value = getBizCode() !== 'fupin'

  try {
    // 获取商品SPU数据
    const json = await querySpu()

    // 处理API响应错误
    if (json.code !== '0000') {
      if (json.code === '8888') {
        console.warn('此商品信息更新中，暂时无法购买，请您选购其他商品。')
      } else {
        console.warn(json.msg)
      }
      detailErr.value = true
      isLoading.value = false
      return
    }

    // 验证商品数据有效性
    if (!spu.value) {
      console.warn('商品数据为空')
      isLoading.value = false
      return
    }

    // 检查当前SKU并判断是否为京东商品
    const sku = querySku()
    if (sku && sku.supplierCode) {
      isJD.value = sku.supplierCode.indexOf(JD_GOODS_CODE) > -1
    }

    // URL更新：如果没有skuId参数但有默认SKU，更新URL保持状态同步
    if (!route.params.skuId && !hasUpdatedUrlForSku.value && sku && sku.skuId) {
      try {
        await router.replace({
          name: 'zq-cm-goods-detail',
          params: { goodsId, skuId: sku.skuId },
          query: route.query
        })
        hasUpdatedUrlForSku.value = true
      } catch (e) {
        console.warn('更新 URL skuId 失败:', e)
      }
    }

    // 初始化规格选项数据
    initSpecOptions()

    // 处理营销活动数据
    processMarketingTemplates()

    // 基础数据加载完成，更新页面状态
    isLoading.value = false
    isDataGet.value = true

    // 等待DOM更新完成
    await nextTick()

    // 添加内容加载动画效果
    setTimeout(() => {
      contentLoaded.value = true
      initialLoadComplete.value = true
    }, 100)

    // 京东商品异步加载额外信息
    if (isJD.value) {
      Promise.all([
        queryPredictSku(),
        checkIsSkuSale()
      ]).catch(error => {
        console.error('加载额外商品信息失败:', error)
      })
    }

    // 延迟滚动到选中规格位置
    setTimeout(() => {
      scrollToSelectedSpec()
    }, 200)

    // 根据起购要求设置初始购买数量
    const lowestBuy = lowestBuyObj.value
    if (lowestBuy.isLowestBuy) {
      goodsNum.value = lowestBuy.lowestBuyNum
    }

    // 初始化懒加载商品介绍组件
    setTimeout(() => {
      lazyGoodsIntroduceRef.value?.init()
    }, 500)

    // 处理默认规格选择
    await nextTick()
    if (!hasSpecs.value && specOptions.value.curSpecs.length === 0) {
      selectSpec('默认规格')
    }

    // 检查用户权限和限制
    await checkWhiteUserLimit()

    // 查询商品销售区域限制
    await querySale()

    // 已登录用户地址检查
    if (userStore.isLogin) {
      await addressCheck()
    }

    // 初始化分享功能
    await shareInit()

  } catch (error) {
    console.error('加载商品详情失败:', error)
    detailErr.value = true
    isLoading.value = false
  }
}

// ===================== 数据获取函数 - 商品介绍相关 =======================
// 获取商品介绍详情数据，用于懒加载显示
const getProductIntroduction = async () => {
  const sku = querySku()
  if (!sku) return

  const { supplierSkuId, supplierCode } = sku
  const [err, json] = await productIntroduction({
    supplierSkuId,
    supplierCode
  })
  if (!err) {
    // 直接更新响应式数据
    productIntroductionData.value = json
  }
}

// ===================== 数据获取函数 - 物流配送相关 =======================
// 查询物流配送时间，根据用户地址和商品信息预测送达时间
const queryPredictSku = async () => {
  const info = userStore.curAddressInfo
  const addressInfo = JSON.stringify({
    provinceId: info.provinceId,
    provinceName: info.provinceName,
    cityId: info.cityId,
    cityName: info.cityName,
    countyId: info.countyId,
    countyName: info.countyName,
    townId: info.townId,
    townName: info.townName
  })

  const sku = currentSku.value
  if (!sku || !sku.supplierCode || !sku.supplierSkuId) {
    logisticsServicesInfo.value = {
      ...logisticsServicesInfo.value,
      predictContent: '预计48小时之内发货'
    }
    return
  }

  const params = {
    supplierCode: sku.supplierCode,
    supplierSkuId: sku.supplierSkuId,
    skuNum: goodsNum.value,
    addressInfoStr: addressInfo
  }

  const [err, res] = await queryPredictSkuPromise(params)
  if (!err) {
    logisticsServicesInfo.value = {
      ...logisticsServicesInfo.value,
      ...res
    }
    return
  }

  logisticsServicesInfo.value = {
    ...logisticsServicesInfo.value,
    predictContent: '预计48小时之内发货'
  }
}

// ===================== 数据获取函数 - SKU状态检查 =======================
// 检查SKU是否可售，验证商品当前销售状态
const checkIsSkuSale = async () => {
  const sku = currentSku.value
  if (!sku || !sku.supplierCode || !sku.supplierSkuId) return

  const params = {
    supplierCode: sku.supplierCode,
    supplierSkuId: sku.supplierSkuId
  }

  const [err, res] = await checkSkuSale(params)
  if (!err) {
    logisticsServicesInfo.value = {
      ...logisticsServicesInfo.value,
      ...res[0]
    }
    return
  }

  logisticsServicesInfo.value = {
    ...logisticsServicesInfo.value,
    logisticsType: 0,
    returnRuleStr: ''
  }
}

// ===================== 工具函数 - SKU显示相关 =======================
// 获取SKU的显示名称，将多个规格参数组合成可读的显示文本
const getSkuDisplayName = (sku) => {
  const specs = []
  if (sku.param) specs.push(sku.param)
  if (sku.param1) specs.push(sku.param1)
  if (sku.param2) specs.push(sku.param2)
  if (sku.param3) specs.push(sku.param3)
  return specs.join('，') || '默认'
}

// ===================== 工具函数 - 规格状态更新 =======================
// 根据SKU更新规格状态，将SKU参数转换为规格选择状态
const updateSpecsFromSku = (sku) => {
  const specs = []
  if (sku.param) specs.push('_p0_' + sku.param)
  if (sku.param1) specs.push('_p1_' + sku.param1)
  if (sku.param2) specs.push('_p2_' + sku.param2)
  if (sku.param3) specs.push('_p3_' + sku.param3)

  // 更新当前规格状态addToCart
  curSpecs.value = specs
}

// ===================== 数据获取函数 - 用户权限检查 =======================
// 检查白名单用户限制，验证用户是否有购买权限
const checkWhiteUserLimit = async () => {
  const goodsDetail = spu.value
  if (goodsDetail && goodsDetail.isCheckWhiteUser && goodsDetail.isCheckWhiteUser === '1') {
    // 如果登录的话，查询用户是否有资格在白名单内
    if (userStore.isLogin) {
      const [err, json] = await isWhiteUserLimitCheck(goodsId)
      if (!err) {
        limitState.value = json
      }
    }
  }
}

// ===================== 数据获取函数 - 区域销售限制 =======================
// 查询商品限制销售区域，检查当前地址是否允许购买该商品
const querySale = async () => {
  const info = userStore.curAddressInfo
  const params = {
    area: JSON.stringify({
      provinceId: info.provinceId,
      cityId: info.cityId,
      countyId: info.countyId,
      townId: info.townId
    }),
    goodsIdList: goodsId
  }

  // 商品数据正常时才进行区域限购查询
  if (!detailErr.value) {
    if (userStore.isLogin) {
      const [err, json] = await getLimitAreaList(params)
      if (!err && json) {
        regionalSalesState.value = json.length <= 0
      }
    }
    isDataGet.value = true
  } else {
    isDataGet.value = false
  }
}

// ===================== 工具函数 - 地址验证 =======================
// 地址检查方法，验证收货地址是否符合配送要求
const addressCheck = async () => {
  showLoadingToast()
  const [err, json] = await jdAddressCheck()
  closeToast()

  if (err) {
    showToast(err.msg)
    return false
  }

  if (!json) {
    $alert({
      title: '',
      message: '由于物流配送地址库规则升级，收货地址需要精确到街道，请您重新设置地址，按指引操作及保存!',
      confirmButtonText: '修改地址',
      cancelButtonText: '取消',
      showCancelButton: true,
      onConfirmCallback: () => {
        // 确认修改地址
        router.push({
          name: 'address-edit',
          query: {
            addrId: userStore.curAddressInfo.addressId,
            isInvalid: '1'
          }
        })
      }
    })
    return false
  }

  return true
}

// ===================== 营销活动处理函数 =======================
// 处理营销活动模板，分类不同类型的营销位信息
const processMarketingTemplates = () => {
  const goodsDetail = spu.value
  if (!goodsDetail || !goodsDetail.marketTemplates) {
    return
  }

  // 活动营销位信息 （templateType 为 营销位类型）
  marketTemplatesType1.value = goodsDetail.marketTemplates.filter(
    (item) => item.templateType === '1'
  )
  marketTemplatesType4.value = goodsDetail.marketTemplates.filter(
    (item) => item.templateType === '4'
  )

  // 如果有营销位类型1，获取电子券信息
  if (userStore.isLogin && marketTemplatesType1.value.length > 0) {
    getElectronic()
  }
}

// 获取电子券信息，处理特定营销活动的优惠券数据
const getElectronic = async () => {
  if (userStore.isLogin && marketTemplatesType1.value && marketTemplatesType1.value.length > 0) {
    if (
      marketTemplatesType1.value?.[0] &&
      marketTemplatesType1.value[0].reqType === '1' &&
      marketTemplatesType1.value[0].templateNo === 'wxy618'
    ) {
      const [err, json] = await getActiveList({ templateNo: marketTemplatesType1.value[0].templateNo || '' })
      if (!err) {
        reducePrice.value = json
      }
    }
  }
}

// 营销活动按钮点击处理，跳转到相应的营销活动页面
const marketingBtn = () => {
  if (!marketTemplatesType1.value || marketTemplatesType1.value.length === 0) {
    return
  }

  const { reqType, reqUrl, templateNo } = marketTemplatesType1.value[0]
  // reqType=1 跳转链接形式
  if (reqType === '1') {
    if (templateNo === 'wxy618') {
      // 保证金活动，特殊处理，拼接 goodsId,skuId,callback
      const host = window.location.origin
      const path = import.meta.env.VITE_BASE_URL
      const callbackUrl = host + path + `/goodsdetail/${goodsId}/${currentSku.value.skuId}?distri_biz_code=ziying`
      if (reducePrice.value > 0 && Number(goodsInfo.value.price) === 0) {
        showToast('您已经参与过该活动，请下次再试吧')
        return
      }
      window.location.href = urlAppend(reqUrl, {
        goodsId: goodsId,
        skuId: currentSku.value.skuId,
        callback: callbackUrl
      })
    } else {
      window.location.href = reqUrl
    }
  }
}

// 跳转到促销活动详情页面，处理保证金等特殊活动
const goToPromotionDetail = (item) => {
  const { reqUrl } = item
  // 解析 reqUrl
  const urlObj = new URL(reqUrl)
  const currentUrl = window.location.href
  // 更新或添加参数
  urlObj.searchParams.set('goodsId', goodsId)
  urlObj.searchParams.set('skuId', currentSku.value.skuId)
  urlObj.searchParams.set('callback', encodeURIComponent(currentUrl))

  // 获取完整URL
  const url = urlObj.toString()
  // 判断当前商品是否有货
  if (!stockState.value) {
    showToast('抱歉，所选商品暂时无货，请选择其他商品办理。')
  } else {
    try {
      window.location.href = url
    } catch (error) {
      console.error('跳转保证金页面失败:', error)
      showToast('跳转失败，请稍后再试')
    }
  }
}

// ===================== 工具函数 - 复制操作 =======================
// 复制商品编码到剪贴板，提供用户便捷的商品信息获取方式
const handleCopyCode = async () => {
  try {
    await toClipboard(currentSku.value.supplierSkuId)
    showToast('复制成功');
  } catch (error) {
    console.error('复制失败:', error)
    showToast('复制失败');
  }
}

// ===================== 工具函数 - 规格显示 =======================
// 获取规格组名称，根据规格数量动态生成显示名称
const getSpecGroupName = (groupIndex) => {
  return hasSpecs.value ? `规格${groupIndex + 1}` : '规格'
}

// ===================== 事件处理函数 - 规格选择 =======================
// 处理规格选择逻辑，支持字符串规格和对象规格两种形式
const selectSpec = (spec) => {
  console.log('选择规格:', spec)

  // 处理默认规格的特殊情况
  if (typeof spec === 'string') {
    const { curDisabledSpecs, curSpecs } = specOptions.value

    if (curDisabledSpecs.indexOf(spec) >= 0) return

    if (spec === '默认规格' && curSpecs.indexOf(spec) >= 0) {
      return
    }

    // 如果传入的是规格字符串，使用原有的setSpecs方法
    if (spec === '默认规格') {
      // 对于默认规格，不需要特殊处理，保持当前状态
      // 如果没有规格数据，curSpecs应该为空数组
      if (!querySpecsList() || querySpecsList().length === 0 || querySpecsList()[0].length === 0) {
        curSpecs.value = []
      }
    } else {
      setSpecs(spec)
    }

    // 切换规格时重置数量为1，但要考虑起购要求
    const lowestBuy = lowestBuyObj.value
    goodsNum.value = lowestBuy.isLowestBuy ? lowestBuy.lowestBuyNum : 1
  }

  // 如果传入的是完整的spec对象
  else if (spec && spec.skuData) {
    curSkuId.value = spec.skuData.skuId

    // 更新当前规格
    const { param, param1, param2, param3 } = spec.skuData
    curSpecs.value = ['_p0_' + param, '_p1_' + param1, '_p2_' + param2, '_p3_' + param3]
      .filter(p => p !== '_p0_undefined' && p !== '_p1_undefined' &&
        p !== '_p2_undefined' && p !== '_p3_undefined')

    // 切换规格时重置数量为1，但要考虑起购要求
    const lowestBuy = lowestBuyObj.value
    goodsNum.value = lowestBuy.isLowestBuy ? lowestBuy.lowestBuyNum : 1
  }

  // 触发相关数据更新和滚动
  nextTick(() => {
    debouncedUpdateGoodsInfo()
  })

  // 延迟执行滚动，确保DOM完全更新
  setTimeout(() => {
    scrollToSelectedSpec()
  }, 100)
}

// ===================== 工具函数 - 滚动控制 =======================
// 自动滚动到选中的规格，确保选中项在可视区域内居中显示
const scrollToSelectedSpec = async () => {
  console.log('开始执行 scrollToSelectedSpec')

  // 等待多个 tick 确保 DOM 完全更新
  await nextTick()
  await nextTick()

  // 通过子组件引用获取 specOptionsRef
  const container = specSelectionRef.value?.specOptionsRef
  if (!container) {
    console.log('specOptionsRef 不存在')
    return
  }

  // 修复选择器，使用正确的类名
  const selectedSpecElement = container.querySelector('.spec-option.is-active')
  console.log('找到的选中元素:', selectedSpecElement)

  if (!selectedSpecElement) {
    console.log('未找到选中的规格元素，尝试查找所有规格元素')
    const allSpecs = container.querySelectorAll('.spec-option')
    console.log('所有规格元素:', allSpecs)
    return
  }

  const containerWidth = container.clientWidth
  const selectedElementLeft = selectedSpecElement.offsetLeft
  const selectedElementWidth = selectedSpecElement.offsetWidth

  console.log('滚动计算参数:', {
    containerWidth,
    selectedElementLeft,
    selectedElementWidth,
    scrollWidth: container.scrollWidth
  })

  // 计算需要滚动的距离，让选中的规格居中显示
  const targetScrollLeft = selectedElementLeft - (containerWidth / 2) + (selectedElementWidth / 2)

  // 确保滚动位置不会超出边界
  const maxScrollLeft = container.scrollWidth - containerWidth
  const finalScrollLeft = Math.max(0, Math.min(targetScrollLeft, maxScrollLeft))

  console.log('滚动目标位置:', {
    targetScrollLeft,
    maxScrollLeft,
    finalScrollLeft
  })

  // 使用 requestAnimationFrame 确保在正确的时机执行滚动
  requestAnimationFrame(() => {
    console.log('执行滚动到位置:', finalScrollLeft)
    container.scrollTo({
      left: finalScrollLeft,
      behavior: 'smooth'
    })
  })
}


// ===================== 事件处理函数 - 数量变化 =======================
// 处理商品数量变化，验证起购和限购要求
const handleQuantityChange = (quantity) => {
  const xg = xgObj.value
  const lowestBuy = lowestBuyObj.value

  // 验证起购要求
  if (lowestBuy.isLowestBuy && quantity < lowestBuy.lowestBuyNum) {
    console.warn(`最少购买${lowestBuy.lowestBuyNum}件哦！`)
    goodsNum.value = lowestBuy.lowestBuyNum
    return
  }

  // 验证限购要求
  if (xg.isXg && quantity > xg.limitNum) {
    console.warn(`超出限购数量：${xg.limitText}`)
    goodsNum.value = xg.limitNum
    return
  }

  goodsNum.value = quantity
}

// ===================== 事件处理函数 - 加入购物车 =======================
// 处理加入购物车操作，包含登录检查、地址验证、规格验证等完整流程
const handleAddToCart = async () => {
  // 检查登录状态
  await userStore.queryLoginStatus()
  if (!userStore.isLogin) {
    // 未登录，引导用户登录
    const loginSuccess = await userStore.login()
    if (!loginSuccess) {
      // 用户取消登录或登录失败
      return
    }
  }
  showSpecPopup.value = false
  const isPassed = await addressCheck()
  if (!isPassed) {
    return
  }

  if (!isSpecsComplete()) {
    showToast('请选择完整商品规格！')
    return
  }

  // 需要判断商品状态 state： 0-不能购买，1-上架，2-下架，null-状态异常
  if (currentSku.value.state !== '1') {
    showToast('商品已下架，看看其他商品吧')
    return
  }

  // 检查库存状态
  if (!currentSku.value.stock || currentSku.value.stock <= 0) {
    showToast('商品暂时无货，看看其他商品吧')
    return
  }


  const info = userStore.curAddressInfo
  const addressInfo = JSON.stringify({
    provinceId: info.provinceId,
    provinceName: info.provinceName,
    cityId: info.cityId,
    cityName: info.cityName,
    countyId: info.countyId,
    countyName: info.countyName,
    townId: info.townId,
    townName: info.townName
  })

  showLoadingToast()

  try {
    const err = await newCartStore.add({
      goodsId: currentSku.value.goodsId,
      skuId: currentSku.value.skuId,
      goodsNum: goodsNum.value,
      addressInfo
    })
    closeToast()

    if (err) {
      // 添加购物车失败
      showToast(err.msg)
    } else {
      // 添加购物车成功
      setTimeout(() => {
        showToast('加入购物车成功')
        if (lowestBuyObj.value.isLowestBuy) {
          goodsNum.value = +lowestBuyObj.value.lowestBuyNum
        } else {
          goodsNum.value = 1
        }
      }, 0)
    }
  } catch (error) {
    showToast('添加购物车失败，请重试')
  }
}

// ===================== 事件处理函数 - 立即购买 =======================
// 处理立即购买操作，包含完整的购买流程验证和订单创建
const handleBuyNow = async () => {
  // 检查登录状态
  await userStore.queryLoginStatus()
  if (!userStore.isLogin) {
    // 未登录，引导用户登录
    const loginSuccess = await userStore.login()
    if (!loginSuccess) {
      // 用户取消登录或登录失败
      return
    }
  }
  showSpecPopup.value = false
  const isPassed = await addressCheck()
  if (!isPassed) {
    return
  }

  if (!isSpecsComplete()) {
    showToast('请选择完整商品规格！')
    return
  }

  // 需要判断商品状态 state： 0-不能购买，1-上架，2-下架，null-状态异常
  if (currentSku.value.state !== '1') {
    showToast('商品已下架，看看其他商品吧')
    return
  }

  // 检查库存状态
  if (!currentSku.value.stock || currentSku.value.stock <= 0) {
    showToast('商品暂时无货，看看其他商品吧')
    return
  }

  const info = userStore.curAddressInfo
  const addressInfo = JSON.stringify({
    provinceId: info.provinceId,
    provinceName: info.provinceName,
    cityId: info.cityId,
    cityName: info.cityName,
    countyId: info.countyId,
    countyName: info.countyName,
    townId: info.townId,
    townName: info.townName
  })

  showLoadingToast()

  try {
    const [res] = await getBuyNowGoods({
      goodsId: currentSku.value.goodsId,
      skuId: currentSku.value.skuId,
      goodsNum: goodsNum.value,
      addressInfo,
      bizCode: getBizCode('ORDER')
    })
    closeToast()
    if (res?.code !== '0000') {
      if (res?.code === '1003') {
        $alert({
          title: '',
          message: '由于物流配送地址库规则升级，收货地址需要精确到街道，请您重新设置地址，按指引操作及保存!',
          confirmButtonText: '修改地址',
          cancelButtonText: '取消',
          showCancelButton: true,
          confirmButtonColor: '#007EE6',
          cancelButtonColor: '#007EE6',
          onConfirmCallback: () => {
            router.push({
              name: 'address-edit',
              query: {
                addrId: info.addressId,
                isInvalid: '1'
              }
            })
          }
        })
      } else {
        showToast(res?.msg || '购买失败，请重试')
      }
    } else {
      // 购买成功
      // 缓存立即购买的数据
      buyProductNow.set(res)
      buyProductNowSession.set(res)

      // 调用setFrontCache接口缓存数据
      showLoadingToast()
      try {
        await setFrontCache({
          content: JSON.stringify(res)
        })
      } catch (cacheError) {
        console.error('缓存数据失败:', cacheError)
      } finally {
        closeToast()
      }

      // 直接下单，给订单页传参数用于订单页回显数据
      const query = {
        goodsId: currentSku.value.goodsId,
        skuId: currentSku.value.skuId,
        goodsNum: goodsNum.value,
        supplierCode: currentSku.value.supplierCode
      }

      // 如果当前页面URL中包含这些参数，则添加到query对象中
      if (route.query.curSelectedMoney) {
        query.curSelectedMoney = route.query.curSelectedMoney
      }
      if (route.query.curSelectedTime) {
        query.curSelectedTime = route.query.curSelectedTime
      }
      if (route.query.orderNo) {
        query.orderNo = route.query.orderNo
      }

      router.push({
        path: '/orderconfirm',
        query
      })
      // 重置商品数量
      if (lowestBuyObj.value.isLowestBuy) {
        goodsNum.value = +lowestBuyObj.value.lowestBuyNum
      } else {
        goodsNum.value = 1
      }
    }
  } catch (error) {
    showToast('购买失败，请重试')
  }
}

// ===================== 事件处理函数 - 按钮操作 =======================
// 显示规格选择弹窗，用于加入购物车操作
const addToCart = () => {
  if (cartButtonDisabledStatus.value) {
    return
  }
  specActionType.value = 1
  showSpecPopup.value = true
}

// 跳转到购物车页面
const goToCart = () => {
  router.push({ name: 'cart' })
}

// 显示规格选择弹窗，用于立即购买操作
const buyNow = () => {
  if (cartButtonDisabledStatus.value) {
    return
  }
  specActionType.value = 2
  showSpecPopup.value = true
}

// ===================== 事件处理函数 - 媒体轮播 =======================
// 处理图片预览操作，设置预览索引
const handleImagePreview = ({ item, index }) => {
  // 图片预览逻辑
  if (item.type === 'image') {
    previewImageIndex.value = index
    // 可以在这里实现图片预览功能
  }
}

// 处理视频播放操作
const handleVideoPlay = ({ item }) => {
  // 视频播放逻辑
  console.log('播放视频:', item)
  // 可以打开视频播放器或全屏播放
}

// 处理轮播切换事件
const handleSlideChange = (index) => {
  // 轮播切换回调
  console.log('当前轮播索引:', index)
}

// ===================== 事件处理函数 - 页面导航 =======================
// 返回商品列表页面
const handleGoToGoodsList = () => {
  router.push('/zq/manager/goods-list')
}

// ===================== 事件处理函数 - 地址选择 =======================
// 点击配送区域，打开地址选择弹窗
const handleAddressClick = () => {
  // 点击配送区域，打开地址选择弹窗
  showAddressPopup.value = true
}

// 地址选择完成后的回调处理
const handleAddressSelect = async (address) => {
  // 地址选择完成回调
  console.log('选择了新地址:', address)

  // 地址变更后重新查询商品相关信息
  await reloadGoodsInfoAfterAddressChange()
}

// 地址弹窗关闭回调
const handleAddressPopupClose = () => {
  // 地址弹窗关闭回调
  showAddressPopup.value = false
}

// ===================== 工具函数 - 地址变更处理 =======================
// 地址变更后重新查询商品相关信息，确保数据准确性
const reloadGoodsInfoAfterAddressChange = async () => {
  try {
    // 如果是京东商品，需要重新查询物流信息
    if (isJD.value) {
      // 重新查询物流配送时间
      await queryPredictSku()

      // 重新检查SKU是否可售
      await checkIsSkuSale()
    }

    // 重新检查白名单用户限制
    await checkWhiteUserLimit()

    // 重新查询商品限制销售区域
    await querySale()

    console.log('地址变更后商品信息更新完成')
  } catch (error) {
    console.error('地址变更后更新商品信息失败:', error)
  }
  if (userStore.isLogin) {
    await addressCheck()
  }
}

// ===================== 计算属性 - 商品状态检查 =======================
// 商品上架状态检查，判断商品是否可以购买
const onSaleState = computed(() => {
  // state： 0-不能购买，1-上架，2-下架，null-状态异常
  return !(currentSku.value && currentSku.value.state === '2')
})

// 商品库存状态检查，判断是否有库存
const stockState = computed(() => {
  return currentSku.value && currentSku.value.stock > 0
})

// 用户购买资格状态检查，判断用户是否有购买权限
const userStatus = computed(() => {
  // 用户购买资格检查
  // 如果商品需要检查白名单用户且用户已登录，则返回limitState的值
  const goodsDetail = spu.value
  if (goodsDetail && goodsDetail.isCheckWhiteUser && goodsDetail.isCheckWhiteUser === '1' && userStore.isLogin) {
    return limitState.value
  }
  // 其他情况默认有购买资格
  return true
})

// ===================== 计算属性 - 按钮状态控制 =======================
// 购物车按钮禁用状态，综合判断所有购买条件
const cartButtonDisabledStatus = computed(() => {
  return isDataGet.value ?
    !onSaleState.value || !stockState.value || !userStatus.value || !regionalSalesState.value || !limitState.value :
    true
})

// ===================== 监听器 - 数据变化响应 =======================
// 监听地址变化，重新查询物流信息和购物车数据
const watchAddress = computed(() => userStore.curAddressInfo)
watch(watchAddress, async (newAddr, oldAddr) => {
  if (newAddr && oldAddr && (
    newAddr.provinceId !== oldAddr.provinceId ||
    newAddr.cityId !== oldAddr.cityId ||
    newAddr.countyId !== oldAddr.countyId ||
    newAddr.townId !== oldAddr.townId
  )) {
    if (isJD.value) {
      await queryPredictSku()
    }
    // 新增：地址切换时调用购物车query
    if (userStore.isLogin) {
      try {
        await newCartStore.query()
        console.log('地址切换后购物车数据已更新')
      } catch (error) {
        console.error('地址切换后更新购物车数据失败:', error)
      }
    }
  }
}, { deep: true })

// 监听规格数据变化，用于调试和状态跟踪
watch([curSpecs, () => queryDisabledSpecs()], ([newCurSpecs, newDisabledSpecs]) => {
  console.log('规格数据更新:', {
    curSpecs: newCurSpecs,
    disabledSpecs: newDisabledSpecs
  })
}, { deep: true })

// 监听当前SKU ID变化，自动滚动到选中规格
watch(curSkuId, (newSkuId, oldSkuId) => {
  if (newSkuId && newSkuId !== oldSkuId && initialLoadComplete.value) {
    console.log('SKU ID 变化，触发滚动:', { newSkuId, oldSkuId })
    setTimeout(() => {
      scrollToSelectedSpec()
    }, 150)
  }
})

// 监听登录状态变化，重新检查用户权限和营销活动
watch(() => userStore.isLogin, async (newLoginStatus, oldLoginStatus) => {
  if (newLoginStatus !== oldLoginStatus && spu.value) {
    // 登录状态发生变化，重新检查白名单用户限制
    await checkWhiteUserLimit()

    // 如果用户登录了，重新获取营销活动信息
    if (newLoginStatus && marketTemplatesType1.value.length > 0) {
      await getElectronic()
    }

    // 新增：登录状态变化时调用购物车query
    if (newLoginStatus) {
      try {
        await newCartStore.query()
        console.log('登录后购物车数据已更新')
      } catch (error) {
        console.error('登录后更新购物车数据失败:', error)
      }
    }
  }
})

// ===================== 生命周期钩子 =======================
// 组件挂载时初始化数据和事件监听
onMounted(async () => {
  loadGoodsDetail()
  // 监听滚动事件，实时保存滚动位置
  window.addEventListener('scroll', saveScrollPosition, { passive: true })

  // 新增：页面进入时判断登录状态，如果登录了就查询购物车
  if (userStore.isLogin) {
    try {
      await newCartStore.query()
      console.log('页面加载时购物车数据已更新')
    } catch (error) {
      console.error('页面加载时更新购物车数据失败:', error)
    }
  }
})

// 组件卸载时清理事件监听器
onUnmounted(() => {
  // 清理滚动事件监听器
  window.removeEventListener('scroll', saveScrollPosition)
})

// ===================== 分享功能处理 =======================
// 初始化分享数据，根据业务类型设置不同的分享文案
const shareInit = async () => {
  const bizCode = getBizCode()
  const intro = () => {
    const sku = spu.value?.skuList?.[0] || currentSku.value
    if (bizCode === 'ziying') {
      return sku.comment || '足不出户囤遍好物！购商品，来精选。'
    } else if (bizCode === 'fupin') {
      return sku.comment || '消费帮扶，共献爱心，乡村振兴，有我联通。'
    } else if (bizCode === 'fulihui') {
      return sku.comment || '足不出户囤遍好物！购商品，来福利汇。'
    } else if (bizCode === 'lnzx') {
      return sku.comment || '联农智选，好货甄选，品质可信。'
    } else {
      return sku.comment || sku.name || sku.merchantName || ''
    }
  }

  const firstSku = spu.value?.skuList?.[0] || currentSku.value
  shareData.title = firstSku.name || goodsInfo.value.name
  shareData.describe = intro()
  shareData.picUrl = goodsInfo.value.imageUrl || ''
  shareData.link = await getDefaultShareUrl()
  log('[GOODS-DETAIL] shareInfo', shareData)
  setWeiXinShareData(shareData)
}

// 处理右上角分享按钮点击事件
const onDropdownShare = (e) => {
  // 分享功能，不能写在异步函数中
  share(shareData, e)
}

// ===================== 计算属性 - 营销活动状态 =======================
// 检查是否有类型1的营销活动（电子券等）
const hasMarketingType1 = computed(() => {
  return marketTemplatesType1.value && marketTemplatesType1.value.length > 0
})

// 检查是否有类型4的营销活动
const hasMarketingType4 = computed(() => {
  return marketTemplatesType4.value && marketTemplatesType4.value.length > 0
})
</script>

<style scoped lang="less">
.goods-detail {
  min-height: 100vh;
  background-color: #FFFFFF;
  padding-bottom: 55px;
}

.goods-content {
  background-color: #FFFFFF;
}

.image-section {
  background-color: #FFFFFF;
  padding: 0;
}

// 规格选择区域样式
.spec-section {
  padding: 10px 17px 0 17px;
  background-color: #FFFFFF;
  margin-bottom: 10px;

  .spec-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 16px;

    .spec-title {
      font-size: 16px;
      color: #171E24;
      font-weight: 500;
    }

    .goods-code {
      display: flex;
      align-items: center;
      gap: 4px;

      .code-label {
        font-size: 12px;
        color: #999999;
      }

      .code-value {
        font-size: 12px;
        color: #999999;
      }

      .copy-icon {
        width: 10px;
        height: 10px;
        flex-shrink: 0;
        margin-left: 2px;
        cursor: pointer;
      }
    }
  }

  .spec-options {
    .radio-wrapper {
      line-height: 40px;

      .spec-group-title {
        font-size: 14px;
        color: #171E24;
        font-weight: 500;
        margin-bottom: 8px;
        margin-top: 16px;

        &:first-child {
          margin-top: 0;
        }
      }

      button {
        display: inline-block;
        min-width: 75px;
        font-size: 13px;
        color: #171E24;
        line-height: 1.2;
        background: #F7F7F7;
        border-radius: 4px;
        padding: 8px 12px;
        margin-right: 8px;
        margin-bottom: 8px;
        outline: none;
        border: 1px solid transparent;
        cursor: pointer;
        transition: all 0.2s ease;

        &.active {
          background: rgba(255, 120, 10, 0.10);
          border: 1px solid var(--wo-biz-theme-color);
          color: var(--wo-biz-theme-color);
        }

        &.disabled {
          background: #F7F7F7;
          color: #B1BEC9;
          cursor: not-allowed;
        }
      }
    }
  }
}
</style>
