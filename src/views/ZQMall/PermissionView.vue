<!--
/**
 * 政企商城权限验证组件
 *
 * 主要功能：
 * 1. 提供统一的权限验证机制，确保只有授权用户才能访问业务内容
 * 2. 集成用户登录状态检查，自动处理未登录用户的访问控制
 * 3. 基于角色类型进行权限判断，支持细粒度的访问控制
 * 4. 提供插槽机制，允许包装任意业务组件进行权限保护
 * 5. 显示友好的无权限提示界面，提升用户体验
 *
 * 技术特点：
 * - 使用Vue 3 Composition API实现响应式权限状态管理
 * - 集成Pinia状态管理，统一处理用户登录状态
 * - 采用插槽模式，提供灵活的组件包装能力
 * - 支持异步权限验证，确保数据准确性
 *
 * 使用场景：
 * - 需要权限保护的页面或组件的外层包装
 * - 政企商城系统中的访问控制
 * - 基于角色的内容展示控制
 */
-->

<template>
  <!-- 权限验证容器 -->
  <div class="zq-permission">
    <!-- 无权限状态显示 -->
    <!-- 当权限状态小于0时显示无权限提示界面 -->
    <template v-if="status < 0">
      <!-- 无权限图标，使用预加载和高优先级获取提升用户体验 -->
      <img 
        src="@/assets/images/no-p.png" 
        alt="无权限" 
        class="zq-permission__icon" 
        width="200" 
        height="200" 
        loading="eager" 
        decoding="async" 
        fetchpriority="high"
      >
      <!-- 无权限提示文本 -->
      <p class="zq-permission__text">暂无该业务查看权限</p>
    </template>
    
    <!-- 有权限时显示插槽内容 -->
    <!-- 当权限验证通过时，渲染包装的业务组件 -->
    <slot v-else />
  </div>
</template>

<script setup>
import { onMounted, ref } from 'vue'
import { useUserStore } from '@/store/modules/user'
import { queryZqInfo } from '@/utils/zqInfo'

// ==================== 权限状态管理 ====================
// 权限状态标识，控制是否显示业务内容
// 0: 初始状态，1: 有权限，负数: 无权限
const status = ref(0)

// 获取用户状态管理store实例，用于处理登录验证
const userStore = useUserStore()

// ==================== 权限验证逻辑 ====================
// 组件挂载时执行权限验证
// 先验证登录状态，再验证角色权限
onMounted(async () => {
  // 执行用户登录验证，确保用户已登录
  const loginStatus = await userStore.login()
  
  // 如果登录成功，进行角色权限验证
  if (loginStatus) {
    // 获取政企信息，包含用户角色数据
    const zqInfo = queryZqInfo() || {}
    
    // 提取角色类型进行权限判断
    const roleType = zqInfo.roleType
    
    // 如果角色类型为负数，表示无权限；否则设置为有权限状态
    status.value = typeof roleType === 'number' && roleType < 0 ? roleType : 1
  } 
  // 如果登录失败，设置为无权限状态
  else {
    status.value = -99
  }
})
</script>

<style lang="less" scoped>
.zq-permission {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  background-color: #FFFFFF;
}
.zq-permission__icon {
  width: 200px;
  height: 200px;
  margin-bottom: 10px;
}
.zq-permission__text {
  font-size: 14px;
  color: #718096;
  margin-bottom: 50px;
}
</style>
