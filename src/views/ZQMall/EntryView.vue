<!--
/**
 * 政企商城入口组件
 *
 * 主要功能：
 * 1. 根据用户角色类型和权限信息进行路由分发，实现不同角色的差异化入口
 * 2. 支持企业管理员、客户经理、服务商等多种角色的权限验证和页面跳转
 * 3. 处理ISV服务商参数，确保用户访问正确的服务商页面
 * 4. 提供无权限状态的友好提示界面，避免用户困惑
 * 5. 集成错误上报机制，便于问题排查和数据分析
 *
 * 技术特点：
 * - 使用Vue 3 Composition API实现响应式状态管理
 * - 基于角色类型进行条件路由跳转，支持多种业务场景
 * - 集成政企信息查询工具，获取用户权限数据
 * - 采用声明式渲染，根据权限状态显示对应界面
 *
 * 使用场景：
 * - 政企商城系统的统一入口页面
 * - 需要根据用户角色进行页面分发的场景
 * - 权限验证失败时的提示页面
 */
-->

<template>
  <!-- 无权限提示界面 -->
  <!-- 当用户没有访问权限时显示此界面，提供友好的错误提示 -->
  <div v-if="noPermission" class="zq-entry">
    <!-- 无权限图标，使用预加载和高优先级获取提升用户体验 -->
    <img
      src="@/assets/images/no-p.png"
      alt="无权限"
      class="zq-entry__icon"
      width="200"
      height="200"
      loading="eager"
      decoding="async"
      fetchpriority="high"
    >
    <!-- 无权限提示文本 -->
    <p class="zq-entry__text">暂无该业务查看权限</p>
  </div>
</template>

<script setup>
import { onBeforeMount, ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { woReport } from 'commonkit'
import { queryZqInfo } from '@/utils/zqInfo'

// ==================== 路由和权限状态管理 ====================
// 无权限状态标识，控制是否显示无权限提示界面
const noPermission = ref(false)

// 获取当前路由信息，用于读取URL参数
const route = useRoute()

// 获取路由器实例，用于页面跳转
const router = useRouter()

// ==================== 权限验证和路由分发逻辑 ====================
// 组件挂载前执行权限验证和路由分发
// 根据用户角色类型和ISV参数决定跳转到哪个页面
onBeforeMount(() => {
  // 从URL查询参数中获取ISV服务商标识
  // 支持字符串和其他类型的参数，确保类型安全
  const rawIsv = (route.query && route.query.isv) || ''
  const isv = typeof rawIsv === 'string' ? rawIsv : String(rawIsv || '')

  // 获取政企信息，包含用户角色和权限数据
  const zqInfo = queryZqInfo() || {}

  // 提取用户角色类型，转换为字符串便于比较
  const role = String(zqInfo && zqInfo.roleType)

  // 提取用户可访问的ISV服务商列表，确保数组类型安全
  const list = Array.isArray(zqInfo && zqInfo.isvList) ? zqInfo.isvList : []

  // 角色类型1：企业经办人
  // 需要验证ISV权限，确保只能访问授权的服务商页面
  if (role === '1') {
    // 如果只有一个可访问的服务商，直接跳转到该服务商页面
    if (list.length === 1) {
      const only = list[0]
      // 处理服务商标识的不同数据格式（字符串或对象）
      const targetIsv = typeof only === 'string' ? only : (only && only.isvId) || ''
      router.replace({ path: '/home', query: { isv: targetIsv } })
    }
    // 如果URL中指定了ISV且在权限列表中，跳转到指定服务商页面
    else if (isv && list.some(item => (typeof item === 'string' ? item === isv : item && item.isvId === isv))) {
      router.replace({ path: '/home', query: { isv } })
    }
    // 权限验证失败，显示无权限提示并上报错误
    else {
      noPermission.value = true
      woReport('政企商城入口 zqInfo 数据异常', JSON.stringify(zqInfo))
    }
  }
  // 角色类型4：白名单
  // 直接跳转到首页，携带ISV参数
  else if (role === '4') {
    router.replace({ path: '/home', query: { isv } })
  }
  // 角色类型2：客户经理
  // 跳转到企业列表管理页面
  else if (role === '2') {
    router.replace('/zq/manager/enterprise-list')
  }
  // 其他角色或未知角色，显示无权限提示
  else {
    noPermission.value = true
  }
})
</script>

<style lang="less" scoped>
.zq-entry {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  background-color: #FFFFFF;
}
.zq-entry__icon {
  width: 200px;
  height: 200px;
  margin-bottom: 10px;
}
.zq-entry__text {
  font-size: 14px;
  color: #718096;
  margin-bottom: 50px;
}
</style>
