<!--
/**
 * 企业管理端订单结果页面组件
 * 
 * @description 用于展示企业用户订单提交后的结果状态页面，包括成功提示、描述信息和后续操作按钮
 * 
 * @features
 * - 订单结果状态展示：显示订单提交成功的图标和标题
 * - 动态内容配置：支持自定义标题、描述文本和按钮文字
 * - 智能跳转功能：提供防抖处理的页面跳转，避免重复点击
 * - 响应式布局：适配不同屏幕尺寸的设备显示
 * - 性能优化：图片预加载和渲染优化
 * 
 * @usage
 * 主要用于企业管理端的订单流程结束页面，向用户确认订单状态并引导后续操作
 * 
 * <AUTHOR> Team
 * @since 2024
 */
-->

<template>
  <!-- 订单结果页面主容器：提供全屏居中布局 -->
  <main class="order-result">
    <!-- 内容区域：包含所有结果展示元素 -->
    <section class="order-result__content">
      <!-- 成功图标容器：展示订单提交成功的视觉反馈 -->
      <div class="order-result__icon-wrapper">
        <img
          class="order-result__icon"
          src="./assets/success.png"
          alt="提交成功"
          loading="eager"
          decoding="sync"
          width="130"
          height="130"
        />
      </div>

      <!-- 结果标题：显示订单操作的结果状态 -->
      <h1 class="order-result__title">{{ resultTitle }}</h1>

      <!-- 描述信息区域：提供详细的说明文字 -->
      <div class="order-result__description">
        <p class="order-result__desc-text">{{ descriptionText }}</p>
      </div>

      <!-- 操作按钮：引导用户进行后续操作（如查看订单） -->
      <WoButton
        type="secondary"
        size="large"
        block
        @click="handleGoToQuery"
      >
        {{ actionButtonText }}
      </WoButton>
    </section>
  </main>
</template>

<script setup>
// ================================================================================================
// 依赖导入区域
// ================================================================================================

// Vue Router 导入：提供页面跳转功能
import { useRouter } from 'vue-router'
// Lodash 工具函数导入：提供防抖功能避免重复点击
import { debounce } from 'lodash-es'
// Vue 响应式 API 导入：用于 props 解构保持响应性
import { toRefs } from 'vue'
// 自定义按钮组件导入：提供统一的按钮样式和交互
import WoButton from '@components/WoElementCom/WoButton/WoButton.vue'

// ================================================================================================
// 组件属性定义区域
// ================================================================================================

// 组件 Props 配置：定义订单结果页面的可配置属性
const props = defineProps({
  // 结果页面标题文字：显示在页面顶部的主要标题
  resultTitle: {
    type: String,
    default: '提交成功'
  },
  // 描述文本内容：提供详细的说明信息
  descriptionText: {
    type: String,
    default: '5天内服务商确认发货情况'
  },
  // 操作按钮文字：显示在按钮上的文本
  actionButtonText: {
    type: String,
    default: '查看订单'
  },
  // 订单类型标识：用于跳转时的查询参数
  orderType: {
    type: Number,
    default: 3
  },
  // 跳转目标路径：点击按钮后要跳转的页面路径
  redirectPath: {
    type: String,
    default: '/user/order/list'
  }
})

// Props 响应式解构：保持属性的响应性以便在模板中使用
const {
  resultTitle,
  descriptionText,
  actionButtonText,
  orderType,
  redirectPath
} = toRefs(props)

// ================================================================================================
// 路由和工具初始化区域
// ================================================================================================

// 路由实例：用于页面跳转操作
const router = useRouter()

// ================================================================================================
// 业务逻辑处理区域
// ================================================================================================

// 防抖处理的页面跳转函数：避免用户快速重复点击导致的多次跳转
const debouncedGoToQuery = debounce(() => {
  // 生成时间戳参数：确保每次跳转都是新的请求，避免缓存问题
  const timestamp = Date.now().toString()

  // 执行页面跳转：使用 replace 方式避免在浏览器历史中留下记录
  router.replace({
    path: redirectPath.value,
    query: {
      type: orderType.value, // 订单类型参数：用于目标页面的数据筛选
      _t: timestamp // 时间戳参数：防止缓存影响
    }
  })
}, 300) // 300ms 防抖延迟：平衡用户体验和防重复点击效果

// 查看订单按钮点击处理函数：触发防抖跳转逻辑
const handleGoToQuery = () => {
  debouncedGoToQuery()
}
</script>

<style scoped lang="less">
.order-result {
  display: flex;
  justify-content: center;
  min-height: 100vh;
  background-color: #FFFFFF;
  padding: 30px 20px;
}

.order-result__content {
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
  max-width: 500px;
}

.order-result__icon-wrapper {
  width: 100%;
  margin-bottom: 12px;
  container-type: inline-size;
  display: flex;
  justify-content: center;
  align-items: center;
}

.order-result__icon {
  width: 130px;
  height: 130px;
  object-fit: contain;
  image-rendering: -webkit-optimize-contrast;
  image-rendering: crisp-edges;
}

.order-result__title {
  font-size: 17px;
  color: #171E24;
  font-weight: 500;
  margin: 0 0 15px 0;
  text-align: center;
  font-display: swap;
}

.order-result__description {
  text-align: center;
  line-height: 1.5;
  padding: 0 30px;
  margin-bottom: 50px;
  width: 100%;
}

.order-result__desc-text {
  font-size: 15px;
  color: #4A5568;
  margin: 0;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.order-result__content > * {
  will-change: auto;
}

.order-result__icon {
  transform: translateZ(0);
}
</style>
