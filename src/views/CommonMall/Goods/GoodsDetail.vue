<!--
/**
 * 商品详情页面组件
 *
 * 主要功能：
 * 1. 展示商品的详细信息，包括图片轮播、基础信息、价格、库存等
 * 2. 提供商品规格选择功能，支持多规格商品的选择和切换
 * 3. 集成营销活动展示，包括优惠券、促销活动等营销信息
 * 4. 支持商品加购和立即购买功能，提供完整的购买流程
 * 5. 提供配送信息展示，包括配送地址、物流时效、退换货政策等
 * 6. 集成商品介绍内容，支持富文本和图片展示
 * 7. 提供商品分享功能，支持多平台分享
 * 8. 实现商品状态检查，包括上下架状态、库存状态、区域限制等
 *
 * 技术特点：
 * - 使用骨架屏优化首屏加载体验
 * - 采用懒加载技术优化商品介绍内容的加载性能
 * - 集成防抖技术优化用户交互响应
 * - 支持动态规格切换和价格更新
 * - 实现智能滚动定位，提升用户体验
 * - 集成状态管理，实现数据持久化
 * - 支持多业务场景适配，兼容不同业务需求
 *
 * 使用场景：
 * - 用户浏览商品详细信息
 * - 商品规格选择和购买决策
 * - 商品分享和推荐
 * - 营销活动参与和优惠获取
 */
-->

<template>
  <!-- 商品详情页面主容器 -->
  <div class="goods-detail">
    <!-- 骨架屏组件，在数据加载时显示，提升用户体验 -->
    <GoodsDetailSkeleton v-if="isLoading" />

    <!-- 商品内容区域，数据加载完成后显示 -->
    <div v-else class="goods-content">
      <!-- 商品图片轮播区域 -->
      <section class="image-section">
        <!-- 商品图片轮播组件，支持自动播放、循环播放、图片预览等功能 -->
        <!-- 传递图片列表、轮播配置，监听图片点击和切换事件 -->
        <GoodsSwiper :imageList="goodsMediaList" :loop="true" :autoplay="true" mode="square" @image-click="handleImagePreview"
          @slide-change="handleSlideChange" />
      </section>

      <!-- 商品基础信息区域，展示商品名称、价格、评价等核心信息 -->
      <GoodsBasicInfo :goods-info="goodsInfo" />

      <!-- 营销活动区域，展示优惠券、促销活动等营销信息 -->
      <!-- 传递营销模板数据，监听营销活动点击事件 -->
      <MarketingSection
        :market-templates="marketTemplatesType1"
        @marketing-click="marketingBtn"
      />

      <!-- 规格选择区域，支持多规格商品的选择和展示 -->
      <!-- 使用ref获取组件实例，传递选中规格和规格选项数据 -->
      <!-- 监听规格点击和选择事件 -->
      <SpecSelection
        ref="specSelectionRef"
        :selected-spec="selectedSpec"
        :spec-options="enhancedSpecOptions"
        @spec-click="showSpecPopup = true"
        @select-spec="selectSpec"
      />

      <!-- 优惠活动区域，展示促销活动、满减活动等优惠信息 -->
      <!-- 传递促销列表和业务代码，监听促销活动点击事件 -->
      <PromotionActivity
        :promotion-list="marketTemplatesType4"
        :biz-code="getBizCode()"
        @promotion-click="goToPromotionDetail"
      />

      <!-- 配送信息区域，展示配送地址、物流时效、服务政策等信息 -->
      <!-- 传递配送信息、物流信息、京东标识等数据 -->
      <!-- 监听地址点击事件，支持地址切换 -->
      <DeliveryInfo
        :delivery-info="deliveryInfo"
        :logistics-info="logisticsServicesInfo"
        :is-j-d="isJD"
        :show-logistics-services="isShowLogisticsServices"
        @address-click="handleAddressClick"
      />

      <!-- 商品介绍区域，懒加载商品详细介绍内容 -->
      <!-- 使用ref获取组件实例，传递当前SKU和介绍数据 -->
      <!-- 监听介绍数据加载事件 -->
      <LazyGoodsIntroduce
        ref="lazyGoodsIntroduceRef"
        :current-sku="currentSku"
        :product-introduction-data="productIntroductionData"
        @load-introduce-data="getProductIntroduction"
      />
    </div>

    <!-- 底部操作栏占位符，为固定底部操作栏预留空间 -->
    <WoActionBarPlaceholder />

    <!-- 商品状态提示信息，展示商品的各种状态提示 -->
    <!-- 传递数据获取状态、上架状态、库存状态、用户状态等信息 -->
    <StatusTips
      :is-data-get="isDataGet"
      :on-sale-state="onSaleState"
      :stock-state="stockState"
      :user-status="userStatus"
      :regional-sales-state="regionalSalesState"
      :limit-state="limitState"
    />

    <!-- 底部操作栏，提供加购、立即购买、购物车等操作按钮 -->
    <!-- 传递购物车数量、按钮禁用状态，监听各种操作事件 -->
    <ActionBar
      :cart-count="cartCount"
      :cart-button-disabled="cartButtonDisabledStatus"
      @go-to-cart="goToCart"
      @add-to-cart="addToCart"
      @buy-now="buyNow"
    />

    <!-- 规格选择弹窗，提供详细的规格选择和数量调整功能 -->
    <!-- 使用v-model双向绑定弹窗显示状态，传递地址信息、商品信息等数据 -->
    <!-- 监听规格选择、加购、购买、数量变化等事件 -->
    <SpecSelectionPopup v-model:visible="showSpecPopup" :address-info="addressInfo" :goods-info="skuGoodsInfo"
      :spec-options="specOptions" :initial-quantity="goodsNum" :action-type="specActionType"
      :cart-button-disabled="cartButtonDisabledStatus" @select-spec="selectSpec" @add-to-cart="handleAddToCart"
      @buy-now="handleBuyNow" @quantity-change="handleQuantityChange" />

    <!-- 地址选择弹窗，支持快速选择和切换配送地址 -->
    <!-- 使用v-model双向绑定弹窗显示状态，传递提示信息 -->
    <!-- 监听地址选择和弹窗关闭事件 -->
    <AddressQuickSelectionPopup v-model:visible="showAddressPopup" :show-tips="true"
      :tips-text="logisticsServicesInfo.predictContent" @select="handleAddressSelect"
      @close="handleAddressPopupClose" />

    <!-- 悬浮按钮组件，提供快速操作入口 -->
    <!-- 设置偏移量，隐藏购物车按钮（因为底部已有操作栏） -->
    <FloatingBubble :offset="200" :isShowCart="false" />

    <!-- 右上角分享功能下拉菜单 -->
    <!-- 传递显示状态，监听分享事件 -->
    <ShareDropdown :visible="showShareMenu" @share="onDropdownShare" />
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, watch, nextTick } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useUserStore } from '@store/modules/user.js'
import { useNewCartStore } from '@store/modules/newCart.js'
import {
  productIntroduction,
  queryPredictSkuPromise,
  checkSkuSale,
  setFrontCache,
  isWhiteUserLimitCheck,
  getLimitAreaList
} from '@api/interface/goods.js'
import GoodsSwiper from '@components/Common/GoodsSwiper.vue'
import WoActionBarPlaceholder from '@components/WoElementCom/WoActionBarPlaceholder.vue'
import LazyGoodsIntroduce from '@components/GoodsDetailCommon/LazyGoodsIntroduce.vue'
import AddressQuickSelectionPopup from '@components/Common/Address/AddressQuickSelectionPopup.vue'
import SpecSelectionPopup from '@components/GoodsDetailCommon/SpecSelectionPopup.vue'
import GoodsDetailSkeleton from '@components/GoodsDetailCommon/GoodsDetailSkeleton.vue'
import GoodsBasicInfo from '@components/GoodsDetailCommon/GoodsBasicInfo.vue'
import MarketingSection from '@components/GoodsDetailCommon/MarketingSection.vue'
import SpecSelection from '@components/GoodsDetailCommon/SpecSelection.vue'
import PromotionActivity from '@components/GoodsDetailCommon/PromotionActivity.vue'
import DeliveryInfo from '@components/GoodsDetailCommon/DeliveryInfo.vue'
import StatusTips from '@components/GoodsDetailCommon/StatusTips.vue'
import ActionBar from '@components/GoodsDetailCommon/ActionBar.vue'
import { getBizCode } from '@utils/curEnv.js'
import { debounce } from 'lodash-es'
import { closeToast, showLoadingToast, showToast } from 'vant'
import { useAlert, useGoodsDetail } from '@/composables/index.js'
import { getBuyNowGoods, jdAddressCheck } from '@api/index.js'
import { buyProductNow, buyProductNowSession } from '@utils/storage.js'
import { getActiveList } from '@api/interface/goods.js'
import { urlAppend } from 'commonkit'
import FloatingBubble from '@components/Common/FloatingBubble.vue'
import ShareDropdown from '@components/GoodsDetailCommon/ShareDropdown.vue'
import { getDefaultShareUrl, shareData } from '@utils/share.js'
import { log, setWeiXinShareData, share } from 'commonkit'
import {JD_GOODS_CODE} from "@utils/types.js";

// ==================== 工具和组合函数 ====================
// 弹窗提示工具函数
const $alert = useAlert()

// ==================== 路由和状态管理 ====================
// 路由相关实例，用于获取参数和页面跳转
const route = useRoute()
const router = useRouter()
// 从路由参数中获取商品ID和SKU ID
const goodsId = route.params.goodsId
const skuId = route.params.skuId
// 用户状态管理store，用于获取用户信息、地址信息、登录状态等
const userStore = useUserStore()
// 购物车状态管理store，用于购物车操作和数据管理
const newCartStore = useNewCartStore()

// ==================== 页面状态控制 ====================
// 是否显示物流服务信息，根据业务场景控制
const isShowLogisticsServices = ref(true)
// 初始加载是否完成，用于控制某些交互功能的启用时机
const initialLoadComplete = ref(false)
// 是否已更新URL中的SKU ID，避免重复更新
const hasUpdatedUrlForSku = ref(false)
// 懒加载商品介绍组件的引用
const lazyGoodsIntroduceRef = ref(null)

// ==================== 商品详情数据管理 ====================
// 使用商品详情组合函数，获取商品相关的状态和方法
// 传入商品ID和SKU ID进行初始化
const {
  spu,                    // 商品SPU数据
  curSpecs,               // 当前选中的规格
  curSkuId,               // 当前SKU ID
  querySpu,               // 查询SPU数据方法
  querySku,               // 查询SKU数据方法
  querySpecsList,         // 查询规格列表方法
  setSpecs,               // 设置规格方法
  queryCurSpecs,          // 查询当前规格方法
  queryDisabledSpecs,     // 查询禁用规格方法
  isSpecsComplete         // 检查规格是否完整方法
} = useGoodsDetail(goodsId, skuId)

// ==================== 购物车相关状态 ====================
// 从购物车store获取商品数量，用于底部操作栏显示
const cartCount = computed(() => {
  return newCartStore.countByGoods
})

// ==================== 页面加载和显示状态 ====================
// 页面是否正在加载，控制骨架屏显示
const isLoading = ref(true)
// 内容是否已加载完成，用于动画效果控制
const contentLoaded = ref(false)
// 商品详情是否加载出错
const detailErr = ref(false)

// ==================== 弹窗显示状态管理 ====================
// 规格选择弹窗显示状态
const showSpecPopup = ref(false)
// 地址选择弹窗显示状态
const showAddressPopup = ref(false)
// 规格操作类型：1-加购，2-立即购买
const specActionType = ref(0)
// 分享菜单显示状态
const showShareMenu = ref(true)

// ==================== 商品相关状态 ====================
// 图片预览索引
const previewImageIndex = ref(0)
// 是否为京东商品
const isJD = ref(false)
// 商品购买数量
const goodsNum = ref(1)
// 商品介绍数据
const productIntroductionData = ref('')

// ==================== 物流服务信息 ====================
// 物流服务相关信息，包含配送时效、退换货政策等
const logisticsServicesInfo = ref({
  logisticsType: 1,                        // 物流类型
  returnRuleStr: '',                       // 退换货规则
  predictContent: '预计48小时之内发货',      // 预计发货时间
  isJD: false                              // 是否为京东配送
})

// ==================== 商品状态检查 ====================
// 数据是否已获取完成
const isDataGet = ref(false)
// 白名单用户限购状态：true-无限购，false-有限购
const limitState = ref(true)
// 区域销售限制状态：true-无区域限制，false-有区域限制
const regionalSalesState = ref(true)

// ==================== 营销活动数据 ====================
// 营销位类型1数据（如优惠券等）
const marketTemplatesType1 = ref([])
// 营销位类型4数据（如促销活动等）
const marketTemplatesType4 = ref([])
// 减免价格金额
const reducePrice = ref(0)

// ==================== 组件引用 ====================
// 规格选择组件引用
const specSelectionRef = ref(null)

// ==================== 限购规则计算 ====================
// 限购相关信息的计算属性，处理限购数量和限购文案
const xgObj = computed(() => {
  const limitTemplate = spu.value?.limitTemplate
  let limitText = ''

  // 根据限购类型生成对应的限购文案
  if (limitTemplate && limitTemplate?.limitCountType) {
    switch (limitTemplate.limitCountType) {
      case '1':
        limitText = `每人每次限购${limitTemplate.limitNum}件`
        break
      case '2':
        limitText = `每人限购${limitTemplate.limitNum}件`
        break
      default:
        limitText = ''
    }
  }

  // 获取限购数量，默认为1
  const limitNum = limitTemplate && limitTemplate?.limitNum ? limitTemplate.limitNum : 1

  return {
    isXg: spu.value?.isXg === '1',  // 是否有限购
    limitNum,                       // 限购数量
    limitText                       // 限购文案
  }
})

// ==================== 起购规则计算 ====================
// 起购相关信息的计算属性，处理起购数量和起购文案
const lowestBuyObj = computed(() => {
  const lowestBuyValue = currentSku.value?.lowestBuy
  // 判断是否有起购要求（起购数量大于1）
  const isLowestBuy = lowestBuyValue ? parseInt(lowestBuyValue, 10) > 1 : false
  // 获取起购数量，默认为1
  const lowestBuyNum = lowestBuyValue ? parseInt(lowestBuyValue, 10) : 1
  // 生成起购文案
  const lowestBuyText = lowestBuyValue ? `${lowestBuyNum}件起购` : ''

  return {
    isLowestBuy,      // 是否有起购要求
    lowestBuyNum,     // 起购数量
    lowestBuyText     // 起购文案
  }
})


// ==================== 地址信息计算 ====================
// 用户当前地址信息的计算属性，用于配送和物流相关功能
const addressInfo = computed(() => {
  const curAddrInfo = userStore.curAddressInfo

  return {
    provinceName: curAddrInfo.provinceName || '',   // 省份名称
    cityName: curAddrInfo.cityName || '',           // 城市名称
    countyName: curAddrInfo.countyName || '',       // 区县名称
    townName: curAddrInfo.townName || '',           // 街道名称
    addrDetail: curAddrInfo.addrDetail || '',       // 详细地址
    receiverName: curAddrInfo.recName || '',        // 收货人姓名
    receiverPhone: curAddrInfo.recPhone || ''       // 收货人电话
  }
})

// ==================== SKU商品信息计算 ====================
// SKU商品信息的计算属性，整合商品基础信息和购买限制信息
const skuGoodsInfo = computed(() => {
  const sku = currentSku.value
  const xg = xgObj.value
  const lowestBuy = lowestBuyObj.value

  // 确定购买限制相关信息
  let purchaseLimit = 999           // 默认购买限制数量
  let purchaseLimitType = 'none'    // 购买限制类型
  let purchaseLimitText = ''        // 购买限制文案

  // 优先处理起购逻辑（起购要求优先级高于限购）
  if (lowestBuy.isLowestBuy) {
    purchaseLimit = lowestBuy.lowestBuyNum
    purchaseLimitType = 'minimum'
    purchaseLimitText = lowestBuy.lowestBuyText
  }

  // 处理限购逻辑
  if (xg.isXg && xg.limitText) {
    purchaseLimit = xg.limitNum
    // 根据限购文案判断限购类型
    purchaseLimitType = xg.limitText.includes('每次') ? 'perTime' : 'perPerson'
    purchaseLimitText = xg.limitText
  }

  return {
    image: sku.listImageUrl || '',              // 商品图片
    price: sku.price || 0,                      // 商品价格
    supplierSkuId: sku.supplierSkuId || '',     // 供应商SKU ID
    stock: sku.stock || 0,                      // 库存数量
    purchaseLimit,                              // 购买限制数量
    purchaseLimitType,                          // 购买限制类型
    purchaseLimitText,                          // 购买限制文案
    currSku: sku,                               // 当前SKU完整数据
    xgObj: xg,                                  // 限购对象
    lowestBuyObj: lowestBuy                     // 起购对象
  }
})

// ==================== 防抖数据更新 ====================
// 防抖的异步数据更新函数，避免频繁的数据请求
// 主要用于规格切换时的数据更新
const debouncedUpdateGoodsInfo = debounce(async () => {
  const sku = querySku()
  // 仅对京东商品执行额外的数据更新
  if (sku && sku.supplierCode && sku.supplierCode.indexOf(JD_GOODS_CODE) > -1) {
    try {
      // 并行执行物流预测和商品介绍获取
      await Promise.all([
        queryPredictSku(),          // 查询物流预测信息
        getProductIntroduction()    // 获取商品介绍内容
      ])
    } catch (error) {
      console.error('更新商品信息失败:', error)
    }
  }
}, 300)

// ==================== 滚动位置管理 ====================
// 保存的滚动位置，用于页面状态恢复
let savedScrollPosition = 0

// 保存当前滚动位置
const saveScrollPosition = () => {
  savedScrollPosition = window.scrollY || document.documentElement.scrollTop
}

// ==================== 核心计算属性 ====================
// 当前SKU数据的计算属性
const currentSku = computed(() => {
  return querySku() || {}
})

// 商品基础信息的计算属性，用于商品基础信息组件
const goodsInfo = computed(() => {
  const sku = currentSku.value
  // 如果SKU或SPU数据不存在，返回默认值
  if (!sku || !spu.value) {
    return {
      name: '',
      price: 0,
      originalPrice: 0,
      imageUrl: ''
    }
  }
  return {
    name: sku.name || '',                    // 商品名称
    price: sku.price || 0,                   // 商品价格
    originalPrice: sku.originalPrice || 0,   // 原价
    imageUrl: sku.listImageUrl || ''         // 商品图片
  }
})

// ==================== 商品媒体资源计算 ====================
// 商品图片列表的计算属性，用于轮播组件
const goodsMediaList = computed(() => {
  const sku = currentSku.value
  // 如果SKU不存在或没有详情图片，返回空数组
  if (!sku || !sku.detailImageUrl) {
    return []
  }
  // 将图片URL数组转换为轮播组件需要的格式
  return sku.detailImageUrl.map(url => ({
    type: 'image',        // 媒体类型
    url,                  // 图片URL
    alt: '商品图片'       // 图片描述
  }))
})

// ==================== 规格选项数据计算 ====================
// 规格选项的计算属性，用于规格选择弹窗
const specOptions = computed(() => {
  // 获取规格相关数据
  const specsList = querySpecsList()
  const curSpecs = queryCurSpecs ? queryCurSpecs() : []
  const disabledSpecs = queryDisabledSpecs ? queryDisabledSpecs() : []

  // 如果没有规格数据，返回默认规格结构
  if (!specsList || specsList.length === 0 || specsList[0].length === 0) {
    return {
      specsList: [],                // 空规格列表
      curSpecs: ['默认规格'],       // 默认选中默认规格
      curDisabledSpecs: [],         // 空禁用规格列表
    }
  }

  return {
    specsList: specsList,           // 规格列表
    curSpecs: curSpecs,             // 当前选中规格
    curDisabledSpecs: disabledSpecs, // 禁用规格列表
  }
})

// ==================== 规格选项缓存管理 ====================
// 缓存的规格选项数据，避免重复计算，提升性能
const cachedSpecOptions = ref([])

// 初始化规格选项数据，在商品数据加载完成后调用
const initSpecOptions = () => {
  // 如果SPU数据或SKU列表不存在，清空缓存
  if (!spu.value || !spu.value.skuList) {
    cachedSpecOptions.value = []
    return
  }

  // 获取禁用规格列表
  const disabledSpecs = queryDisabledSpecs ? queryDisabledSpecs() : []

  // 遍历SKU列表，生成规格选项数据
  const options = spu.value.skuList.map(sku => {
    const isDisabled = checkSkuDisabled(sku, disabledSpecs)

    return {
      id: sku.skuId,                    // SKU ID
      name: getSkuDisplayName(sku),     // SKU显示名称
      image: sku.listImageUrl,          // SKU图片
      disabled: isDisabled,             // 是否禁用
      skuData: sku,                     // 完整SKU数据
    }
  })

  // 只缓存可选择的SKU（过滤掉禁用的），提升用户体验
  cachedSpecOptions.value = options.filter(option => !option.disabled)

  console.log('初始化规格选项数据:', {
    totalOptions: options.length,
    selectableOptions: cachedSpecOptions.value.length
  })
}

// 动态计算选中状态的规格选项，用于规格选择组件
const enhancedSpecOptions = computed(() => {
  return cachedSpecOptions.value.map(option => ({
    ...option,
    selected: option.id === curSkuId.value  // 根据当前SKU ID标记选中状态
  }))
})

// ==================== 规格处理工具函数 ====================
// 检查SKU是否被禁用
const checkSkuDisabled = (sku, disabledSpecs) => {
  const skuSpecs = getSkuSpecs(sku)
  // 如果SKU的任一规格在禁用列表中，则该SKU被禁用
  return skuSpecs.some(spec => disabledSpecs.includes(spec))
}

// 获取SKU的规格数组，用于规格匹配和禁用检查
const getSkuSpecs = (sku) => {
  const { param, param1, param2, param3 } = sku
  // 构建规格标识数组，过滤掉undefined值
  return ['_p0_' + param, '_p1_' + param1, '_p2_' + param2, '_p3_' + param3]
    .filter(p => p !== '_p0_undefined' && p !== '_p1_undefined' &&
      p !== '_p2_undefined' && p !== '_p3_undefined')
}

// ==================== 选中规格显示计算 ====================
// 选中规格的显示文本，包含规格信息和数量
const selectedSpec = computed(() => {
  const sku = currentSku.value
  const specsList = querySpecsList()

  // 如果没有规格数据，显示默认规格
  if (!specsList || specsList.length === 0 || specsList[0].length === 0) {
    return `默认规格 ${goodsNum.value}件`
  }

  // 如果SKU不存在或规格不完整，显示默认
  if (!sku || !isSpecsComplete()) {
    return `默认 ${goodsNum.value}件`
  }

  // 收集SKU的所有规格参数
  const specs = []
  if (sku.param) specs.push(sku.param)
  if (sku.param1) specs.push(sku.param1)
  if (sku.param2) specs.push(sku.param2)
  if (sku.param3) specs.push(sku.param3)

  // 生成规格文本，用中文逗号连接
  const specText = specs.join('，') || '默认'
  return `${specText} ${goodsNum.value}件`
})

// ==================== 配送信息计算 ====================
// 配送信息的计算属性，整合地址、物流、服务等信息
const deliveryInfo = computed(() => {
  const curAddrInfo = userStore.curAddressInfo
  // 拼接完整的配送地址
  const location = `${curAddrInfo.provinceName} ${curAddrInfo.cityName} ${curAddrInfo.countyName} ${curAddrInfo.townName || ''}`.trim()

  return {
    location: location || '配送地址',  // 配送地址
    // 配送时效：优先使用物流服务信息，其次根据是否京东商品显示不同文案
    predictContent: logisticsServicesInfo.value.predictContent || (isJD.value ? 'JD配送' : '普通配送'),
    // 退换货政策：优先使用物流服务信息，默认为7天无理由退货
    returnPolicy: logisticsServicesInfo.value.returnRuleStr || '7天无理由退货',
    // 售后服务说明
    service: '店铺售后由沃百富商城提供服务'
  }
})

// ==================== 商品详情数据加载 ====================
// 商品详情的主要加载函数，负责获取和处理商品数据
const loadGoodsDetail = async () => {
  // 根据业务代码决定是否显示物流服务信息
  isShowLogisticsServices.value = getBizCode() !== 'fupin'

  try {
    // 查询SPU数据
    const json = await querySpu()

    // 检查API响应状态
    if (json.code !== '0000') {
      if (json.code === '8888') {
        console.warn('此商品信息更新中，暂时无法购买，请您选购其他商品。')
      } else {
        console.warn(json.msg)
      }
      // 标记详情加载错误并停止加载状态
      detailErr.value = true
      isLoading.value = false
      return
    }

    // 检查SPU数据是否存在
    if (!spu.value) {
      console.warn('商品数据为空')
      isLoading.value = false
      return
    }

    // 检查当前SKU并判断是否为京东商品
    const sku = querySku()
    if (sku && sku.supplierCode) {
      isJD.value = sku.supplierCode.indexOf(JD_GOODS_CODE) > -1
    }

    // URL SKU ID更新逻辑
    // 如果进入页面时没有传skuId，但已选择了默认规格，则更新URL
    if (!route.params.skuId && !hasUpdatedUrlForSku.value && sku && sku.skuId) {
      try {
        await router.replace({
          name: 'goods-detail',
          params: { goodsId, skuId: sku.skuId },
          query: route.query  // 保留原有查询参数
        })
        hasUpdatedUrlForSku.value = true
      } catch (e) {
        console.warn('更新 URL skuId 失败:', e)
      }
    }

    // 初始化规格选项数据，为规格选择功能做准备
    initSpecOptions()

    // 处理营销活动数据，解析营销模板
    processMarketingTemplates()

    // 基础数据加载完成，可以显示页面内容
    isLoading.value = false
    isDataGet.value = true

    // 等待DOM更新后再执行后续操作
    await nextTick()

    // 添加内容加载动画效果
    setTimeout(() => {
      contentLoaded.value = true
      // 标记初始加载完成，启用某些交互功能
      initialLoadComplete.value = true
    }, 100)

    // 京东商品的额外数据加载
    if (isJD.value) {
      // 异步加载额外信息，不阻塞页面渲染
      // 商品介绍改为懒加载，提升首屏性能
      Promise.all([
        queryPredictSku(),      // 查询物流预测信息
        checkIsSkuSale()        // 检查SKU销售状态
      ]).catch(error => {
        console.error('加载额外商品信息失败:', error)
      })
    }

    // 数据加载完成后，延迟滚动到选中的规格
    setTimeout(() => {
      scrollToSelectedSpec()
    }, 200)

    // 根据起购要求设置初始购买数量
    const lowestBuy = lowestBuyObj.value
    if (lowestBuy.isLowestBuy) {
      goodsNum.value = lowestBuy.lowestBuyNum
    }

    // 初始化懒加载商品介绍组件
    setTimeout(() => {
      lazyGoodsIntroduceRef.value?.init()
    }, 500)

    // 检查白名单用户限制
    await checkWhiteUserLimit()

    // 查询商品限制销售区域
    await querySale()

    // 如果用户已登录，进行地址检查
    if (userStore.isLogin) {
      await addressCheck()
    }

    // 激活客户端分享功能
    await shareInit()

  } catch (error) {
    console.error('加载商品详情失败:', error)
    detailErr.value = true
    isLoading.value = false
  }
}

// ==================== 商品介绍数据获取 ====================
// 获取商品详细介绍内容，主要用于京东商品
const getProductIntroduction = async () => {
  const sku = querySku()
  if (!sku) return

  const { supplierSkuId, supplierCode } = sku
  // 调用商品介绍API
  const [err, json] = await productIntroduction({
    supplierSkuId,
    supplierCode
  })
  if (!err) {
    // 直接更新响应式数据
    productIntroductionData.value = json
  }
}

// ==================== 物流配送时间查询 ====================
// 查询物流配送预测时间，主要用于京东商品
const queryPredictSku = async () => {
  const info = userStore.curAddressInfo
  // 构建地址信息JSON字符串
  const addressInfo = JSON.stringify({
    provinceId: info.provinceId,
    provinceName: info.provinceName,
    cityId: info.cityId,
    cityName: info.cityName,
    countyId: info.countyId,
    countyName: info.countyName,
    townId: info.townId,
    townName: info.townName
  })

  const sku = currentSku.value
  // 如果SKU信息不完整，使用默认配送时间
  if (!sku || !sku.supplierCode || !sku.supplierSkuId) {
    logisticsServicesInfo.value = {
      ...logisticsServicesInfo.value,
      predictContent: '预计48小时之内发货'
    }
    return
  }

  // 构建查询参数
  const params = {
    supplierCode: sku.supplierCode,
    supplierSkuId: sku.supplierSkuId,
    skuNum: goodsNum.value,
    addressInfoStr: addressInfo
  }

  // 调用物流预测API
  const [err, res] = await queryPredictSkuPromise(params)
  if (!err) {
    // 更新物流服务信息
    logisticsServicesInfo.value = {
      ...logisticsServicesInfo.value,
      ...res
    }
    return
  }

  // 如果查询失败，使用默认配送时间
  logisticsServicesInfo.value = {
    ...logisticsServicesInfo.value,
    predictContent: '预计48小时之内发货'
  }
}

// ==================== SKU销售状态检查 ====================
// 检查SKU是否可售，主要用于京东商品
const checkIsSkuSale = async () => {
  const sku = currentSku.value
  if (!sku || !sku.supplierCode || !sku.supplierSkuId) return

  // 构建查询参数
  const params = {
    supplierCode: sku.supplierCode,
    supplierSkuId: sku.supplierSkuId
  }

  // 调用SKU销售状态检查API
  const [err, res] = await checkSkuSale(params)
  if (!err) {
    // 更新物流服务信息
    logisticsServicesInfo.value = {
      ...logisticsServicesInfo.value,
      ...res[0]
    }
    return
  }

  // 如果查询失败，使用默认值
  logisticsServicesInfo.value = {
    ...logisticsServicesInfo.value,
    logisticsType: 0,
    returnRuleStr: ''
  }
}

// ==================== SKU工具函数 ====================
// 获取SKU的显示名称，用于规格选择组件
const getSkuDisplayName = (sku) => {
  const specs = []
  if (sku.param) specs.push(sku.param)
  if (sku.param1) specs.push(sku.param1)
  if (sku.param2) specs.push(sku.param2)
  if (sku.param3) specs.push(sku.param3)
  return specs.join('，') || '默认'
}

// ==================== 用户权限和限制检查 ====================
// 检查白名单用户限制，用于特殊商品的购买权限控制
const checkWhiteUserLimit = async () => {
  const goodsDetail = spu.value
  // 如果商品需要检查白名单用户
  if (goodsDetail && goodsDetail.isCheckWhiteUser && goodsDetail.isCheckWhiteUser === '1') {
    // 如果用户已登录，查询用户是否在白名单内
    if (userStore.isLogin) {
      const [err, json] = await isWhiteUserLimitCheck(goodsId)
      if (!err) {
        limitState.value = json
      }
    }
  }
}

// ==================== 区域销售限制查询 ====================
// 查询商品在当前地址是否支持销售
const querySale = async () => {
  const info = userStore.curAddressInfo
  // 构建区域查询参数
  const params = {
    area: JSON.stringify({
      provinceId: info.provinceId,
      cityId: info.cityId,
      countyId: info.countyId,
      townId: info.townId
    }),
    goodsIdList: goodsId
  }

  // 只有在商品数据正常时才进行区域限购查询
  if (!detailErr.value) {
    if (userStore.isLogin) {
      const [err, json] = await getLimitAreaList(params)
      if (!err && json) {
        // 如果返回的限制列表为空，说明该区域支持销售
        regionalSalesState.value = json.length <= 0
      }
    }
    isDataGet.value = true
  } else {
    isDataGet.value = false
  }
}

// ==================== 地址检查功能 ====================
// 地址检查方法，主要用于京东商品的地址验证
const addressCheck = async () => {
  showLoadingToast()
  const [err, json] = await jdAddressCheck()
  closeToast()

  // 检查API调用是否出错
  if (err) {
    showToast(err.msg)
    return false
  }

  // 如果地址验证失败，提示用户修改地址
  if (!json) {
    $alert({
      title: '',
      message: '由于物流配送地址库规则升级，收货地址需要精确到街道，请您重新设置地址，按指引操作及保存!',
      confirmButtonText: '修改地址',
      cancelButtonText: '取消',
      showCancelButton: true,
      onConfirmCallback: () => {
        // 确认修改地址，跳转到地址编辑页面
        router.push({
          name: 'address-edit',
          query: {
            addrId: userStore.curAddressInfo.addressId,
            isInvalid: '1'  // 标记为无效地址
          }
        })
      }
    })
    return false
  }

  return true
}

// ==================== 营销活动处理 ====================
// 处理营销活动模板数据，分类不同类型的营销活动
const processMarketingTemplates = () => {
  const goodsDetail = spu.value
  if (!goodsDetail || !goodsDetail.marketTemplates) {
    return
  }

  // 按营销位类型分类营销活动
  // 营销位类型1：通常为优惠券等营销活动
  marketTemplatesType1.value = goodsDetail.marketTemplates.filter(
    (item) => item.templateType === '1'
  )
  // 营销位类型4：通常为促销活动等
  marketTemplatesType4.value = goodsDetail.marketTemplates.filter(
    (item) => item.templateType === '4'
  )

  // 如果用户已登录且有营销位类型1，获取电子券信息
  if (userStore.isLogin && marketTemplatesType1.value.length > 0) {
    getElectronic()
  }
}

// 获取电子券信息，用于特定营销活动
const getElectronic = async () => {
  // 检查用户登录状态和营销活动数据
  if (userStore.isLogin && marketTemplatesType1.value && marketTemplatesType1.value.length > 0) {
    // 检查是否为特定的保证金活动
    if (
      marketTemplatesType1.value?.[0] &&
      marketTemplatesType1.value[0].reqType === '1' &&
      marketTemplatesType1.value[0].templateNo === 'wxy618'
    ) {
      // 获取活动列表信息
      const [err, json] = await getActiveList({ templateNo: marketTemplatesType1.value[0].templateNo || '' })
      if (!err) {
        reducePrice.value = json
      }
    }
  }
}

// ==================== 营销活动点击处理 ====================
// 处理营销活动按钮点击事件
const marketingBtn = () => {
  // 检查是否有营销活动数据
  if (!marketTemplatesType1.value || marketTemplatesType1.value.length === 0) {
    return
  }

  const { reqType, reqUrl, templateNo } = marketTemplatesType1.value[0]
  // reqType=1 表示跳转链接形式的营销活动
  if (reqType === '1') {
    if (templateNo === 'wxy618') {
      // 保证金活动特殊处理，需要拼接商品信息和回调地址
      const host = window.location.origin
      const path = import.meta.env.VITE_BASE_URL
      const callbackUrl = host + path + `/goodsdetail/${goodsId}/${currentSku.value.skuId}?distri_biz_code=ziying`

      // 检查用户是否已参与过该活动
      if (reducePrice.value > 0 && Number(goodsInfo.value.price) === 0) {
        showToast('您已经参与过该活动，请下次再试吧')
        return
      }

      // 跳转到保证金活动页面，携带商品信息和回调地址
      window.location.href = urlAppend(reqUrl, {
        goodsId: goodsId,
        skuId: currentSku.value.skuId,
        callback: callbackUrl
      })
    } else {
      // 其他营销活动直接跳转
      window.location.href = reqUrl
    }
  }
}

// ==================== 促销活动详情跳转 ====================
// 跳转到促销活动详情页面
const goToPromotionDetail = (item) => {
  const { reqUrl } = item
  // 解析目标URL
  const urlObj = new URL(reqUrl)
  const currentUrl = window.location.href
  // 更新或添加URL参数，携带商品信息和回调地址
  urlObj.searchParams.set('goodsId', goodsId)
  urlObj.searchParams.set('skuId', currentSku.value.skuId)
  urlObj.searchParams.set('callback', encodeURIComponent(currentUrl))

  // 获取完整的目标URL
  const url = urlObj.toString()

  // 判断当前商品是否有库存
  if (!stockState.value) {
    showToast('抱歉，所选商品暂时无货，请选择其他商品办理。')
  } else {
    try {
      // 跳转到促销活动页面
      window.location.href = url
    } catch (error) {
      console.error('跳转保证金页面失败:', error)
      showToast('跳转失败，请稍后再试')
    }
  }
}

// ==================== 规格选择处理 ====================
// 处理规格选择事件，支持多种规格选择方式
const selectSpec = (spec) => {
  console.log('选择规格:', spec)

  // 如果传入的是完整的规格对象（包含SKU数据）
  if (spec && spec.skuData) {
    // 更新当前SKU ID
    curSkuId.value = spec.skuData.skuId

    // 更新当前规格状态
    const { param, param1, param2, param3 } = spec.skuData
    curSpecs.value = ['_p0_' + param, '_p1_' + param1, '_p2_' + param2, '_p3_' + param3]
      .filter(p => p !== '_p0_undefined' && p !== '_p1_undefined' &&
        p !== '_p2_undefined' && p !== '_p3_undefined')

    // 切换规格时重置购买数量，但要考虑起购要求
    const lowestBuy = lowestBuyObj.value
    goodsNum.value = lowestBuy.isLowestBuy ? lowestBuy.lowestBuyNum : 1

  } else if (typeof spec === 'string') {
    // 处理字符串类型的规格选择
    if (spec === '默认规格') {
      // 对于默认规格，不需要特殊处理，保持当前状态
      // 如果没有规格数据，curSpecs应该为空数组
      if (!querySpecsList() || querySpecsList().length === 0 || querySpecsList()[0].length === 0) {
        curSpecs.value = []
      }
    } else {
      // 如果传入的是规格字符串，使用原有的setSpecs方法
      setSpecs(spec)
    }

    // 切换规格时重置购买数量，但要考虑起购要求
    const lowestBuy = lowestBuyObj.value
    goodsNum.value = lowestBuy.isLowestBuy ? lowestBuy.lowestBuyNum : 1
  }

  // 触发相关数据更新和滚动
  nextTick(() => {
    debouncedUpdateGoodsInfo()
  })

  // 延迟执行滚动，确保DOM完全更新
  setTimeout(() => {
    scrollToSelectedSpec()
  }, 100)
}

// ==================== 规格滚动定位 ====================
// 自动滚动到选中的规格，提升用户体验
const scrollToSelectedSpec = async () => {
  console.log('开始执行 scrollToSelectedSpec')

  // 等待多个tick确保DOM完全更新
  await nextTick()
  await nextTick()

  // 通过子组件引用获取规格选项容器
  const container = specSelectionRef.value?.specOptionsRef
  if (!container) {
    console.log('specOptionsRef 不存在')
    return
  }

  // 查找当前选中的规格元素
  const selectedSpecElement = container.querySelector('.spec-option.is-active')
  console.log('找到的选中元素:', selectedSpecElement)

  if (!selectedSpecElement) {
    console.log('未找到选中的规格元素，尝试查找所有规格元素')
    const allSpecs = container.querySelectorAll('.spec-option')
    console.log('所有规格元素:', allSpecs)
    return
  }

  // 获取滚动计算所需的尺寸信息
  const containerWidth = container.clientWidth
  const selectedElementLeft = selectedSpecElement.offsetLeft
  const selectedElementWidth = selectedSpecElement.offsetWidth

  console.log('滚动计算参数:', {
    containerWidth,
    selectedElementLeft,
    selectedElementWidth,
    scrollWidth: container.scrollWidth
  })

  // 计算需要滚动的距离，让选中的规格在容器中居中显示
  const targetScrollLeft = selectedElementLeft - (containerWidth / 2) + (selectedElementWidth / 2)

  // 确保滚动位置不会超出边界
  const maxScrollLeft = container.scrollWidth - containerWidth
  const finalScrollLeft = Math.max(0, Math.min(targetScrollLeft, maxScrollLeft))

  console.log('滚动目标位置:', {
    targetScrollLeft,
    maxScrollLeft,
    finalScrollLeft
  })

  // 使用requestAnimationFrame确保在正确的时机执行滚动
  requestAnimationFrame(() => {
    console.log('执行滚动到位置:', finalScrollLeft)
    container.scrollTo({
      left: finalScrollLeft,
      behavior: 'smooth'  // 平滑滚动效果
    })
  })
}

// ==================== 购买数量变化处理 ====================
// 处理购买数量变化，验证起购和限购要求
const handleQuantityChange = (quantity) => {
  const xg = xgObj.value
  const lowestBuy = lowestBuyObj.value

  // 验证起购要求，确保购买数量不低于起购数量
  if (lowestBuy.isLowestBuy && quantity < lowestBuy.lowestBuyNum) {
    console.warn(`最少购买${lowestBuy.lowestBuyNum}件哦！`)
    goodsNum.value = lowestBuy.lowestBuyNum
    return
  }

  // 验证限购要求，确保购买数量不超过限购数量
  if (xg.isXg && quantity > xg.limitNum) {
    console.warn(`超出限购数量：${xg.limitText}`)
    goodsNum.value = xg.limitNum
    return
  }

  // 数量验证通过，更新购买数量
  goodsNum.value = quantity
}

// ==================== 加购功能处理 ====================
// 处理添加商品到购物车的操作
const handleAddToCart = async () => {
  // 检查用户登录状态
  await userStore.queryLoginStatus()
  if (!userStore.isLogin) {
    // 未登录，引导用户登录
    const loginSuccess = await userStore.login()
    if (!loginSuccess) {
      // 用户取消登录或登录失败，终止操作
      return
    }
  }

  // 关闭规格选择弹窗
  showSpecPopup.value = false

  // 进行地址检查，确保地址有效
  const isPassed = await addressCheck()
  if (!isPassed) {
    return
  }

  // 检查规格是否选择完整
  if (!isSpecsComplete()) {
    showToast('请选择完整商品规格！')
    return
  }

  // 检查商品状态：0-不能购买，1-上架，2-下架，null-状态异常
  if (currentSku.value.state !== '1') {
    showToast('商品已下架，看看其他商品吧')
    return
  }

  // 检查库存状态
  if (!currentSku.value.stock || currentSku.value.stock <= 0) {
    showToast('商品暂时无货，看看其他商品吧')
    return
  }

  // 构建地址信息JSON字符串
  const info = userStore.curAddressInfo
  const addressInfo = JSON.stringify({
    provinceId: info.provinceId,
    provinceName: info.provinceName,
    cityId: info.cityId,
    cityName: info.cityName,
    countyId: info.countyId,
    countyName: info.countyName,
    townId: info.townId,
    townName: info.townName
  })

  // 显示加载提示
  showLoadingToast()

  try {
    // 调用购物车store的添加方法
    const err = await newCartStore.add({
      goodsId: currentSku.value.goodsId,
      skuId: currentSku.value.skuId,
      goodsNum: goodsNum.value,
      addressInfo
    })
    closeToast()

    if (err) {
      // 添加购物车失败，显示错误信息
      showToast(err.msg)
    } else {
      // 添加购物车成功
      setTimeout(() => {
        showToast('加入购物车成功')
        // 重置购买数量，考虑起购要求
        if (lowestBuyObj.value.isLowestBuy) {
          goodsNum.value = +lowestBuyObj.value.lowestBuyNum
        } else {
          goodsNum.value = 1
        }
      }, 0)
    }
  } catch (error) {
    showToast('添加购物车失败，请重试')
  }
}

// ==================== 立即购买功能处理 ====================
// 处理立即购买操作
const handleBuyNow = async () => {
  // 检查用户登录状态
  await userStore.queryLoginStatus()
  if (!userStore.isLogin) {
    // 未登录，引导用户登录
    const loginSuccess = await userStore.login()
    if (!loginSuccess) {
      // 用户取消登录或登录失败，终止操作
      return
    }
  }

  // 关闭规格选择弹窗
  showSpecPopup.value = false

  // 进行地址检查，确保地址有效
  const isPassed = await addressCheck()
  if (!isPassed) {
    return
  }

  // 检查规格是否选择完整
  if (!isSpecsComplete()) {
    showToast('请选择完整商品规格！')
    return
  }

  // 检查商品状态：0-不能购买，1-上架，2-下架，null-状态异常
  if (currentSku.value.state !== '1') {
    showToast('商品已下架，看看其他商品吧')
    return
  }

  // 检查库存状态
  if (!currentSku.value.stock || currentSku.value.stock <= 0) {
    showToast('商品暂时无货，看看其他商品吧')
    return
  }

  // 构建地址信息JSON字符串
  const info = userStore.curAddressInfo
  const addressInfo = JSON.stringify({
    provinceId: info.provinceId,
    provinceName: info.provinceName,
    cityId: info.cityId,
    cityName: info.cityName,
    countyId: info.countyId,
    countyName: info.countyName,
    townId: info.townId,
    townName: info.townName
  })

  // 显示加载提示
  showLoadingToast()

  try {
    // 调用立即购买API
    const [res] = await getBuyNowGoods({
      goodsId: currentSku.value.goodsId,
      skuId: currentSku.value.skuId,
      goodsNum: goodsNum.value,
      addressInfo,
      bizCode: getBizCode('ORDER')
    })
    closeToast()

    // 检查API响应状态
    if (res?.code !== '0000') {
      if (res?.code === '1003') {
        // 地址不符合要求，提示用户修改地址
        $alert({
          title: '',
          message: '由于物流配送地址库规则升级，收货地址需要精确到街道，请您重新设置地址，按指引操作及保存!',
          confirmButtonText: '修改地址',
          cancelButtonText: '取消',
          showCancelButton: true,
          confirmButtonColor: '#007EE6',
          cancelButtonColor: '#007EE6',
          onConfirmCallback: () => {
            // 跳转到地址编辑页面
            router.push({
              name: 'address-edit',
              query: {
                addrId: info.addressId,
                isInvalid: '1'  // 标记为无效地址
              }
            })
          }
        })
      } else {
        // 其他错误，显示错误信息
        showToast(res?.msg || '购买失败，请重试')
      }
    } else {
      // 购买成功，处理后续流程

      // 缓存立即购买的数据到本地存储
      buyProductNow.set(res)
      buyProductNowSession.set(res)

      // 调用前端缓存接口，将数据缓存到服务器
      showLoadingToast()
      try {
        await setFrontCache({
          content: JSON.stringify(res)
        })
      } catch (cacheError) {
        console.error('缓存数据失败:', cacheError)
      } finally {
        closeToast()
      }

      // 构建订单确认页面的查询参数
      const query = {
        goodsId: currentSku.value.goodsId,
        skuId: currentSku.value.skuId,
        goodsNum: goodsNum.value,
        supplierCode: currentSku.value.supplierCode
      }

      // 如果当前页面URL中包含特殊参数，则传递给订单页面
      if (route.query.curSelectedMoney) {
        query.curSelectedMoney = route.query.curSelectedMoney
      }
      if (route.query.curSelectedTime) {
        query.curSelectedTime = route.query.curSelectedTime
      }
      if (route.query.orderNo) {
        query.orderNo = route.query.orderNo
      }

      // 跳转到订单确认页面
      router.push({
        path: '/orderconfirm',
        query
      })
      // 重置商品数量，考虑起购要求
      if (lowestBuyObj.value.isLowestBuy) {
        goodsNum.value = +lowestBuyObj.value.lowestBuyNum
      } else {
        goodsNum.value = 1
      }
    }
  } catch (error) {
    showToast('购买失败，请重试')
  }
}

// ==================== 底部操作栏事件处理 ====================
// 处理加购按钮点击事件
const addToCart = () => {
  // 检查按钮是否被禁用
  if (cartButtonDisabledStatus.value) {
    return
  }
  // 设置规格操作类型为加购（1）
  specActionType.value = 1
  // 显示规格选择弹窗
  showSpecPopup.value = true
}

// 处理跳转购物车事件
const goToCart = () => {
  router.push({ name: 'cart' })
}

// 处理立即购买按钮点击事件
const buyNow = () => {
  // 检查按钮是否被禁用
  if (cartButtonDisabledStatus.value) {
    return
  }
  // 设置规格操作类型为立即购买（2）
  specActionType.value = 2
  // 显示规格选择弹窗
  showSpecPopup.value = true
}

// ==================== 图片轮播事件处理 ====================
// 处理图片预览点击事件
const handleImagePreview = ({ item, index }) => {
  // 图片预览逻辑
  if (item.type === 'image') {
    previewImageIndex.value = index
    // 可以在这里实现图片预览功能
  }
}

// 处理轮播切换事件
const handleSlideChange = (index) => {
  // 轮播切换回调
  console.log('当前轮播索引:', index)
}

// ==================== 地址相关事件处理 ====================
// 处理配送地址点击事件
const handleAddressClick = () => {
  // 点击配送区域，打开地址选择弹窗
  showAddressPopup.value = true
}

// 处理地址选择完成事件
const handleAddressSelect = async (address) => {
  // 地址选择完成回调
  console.log('选择了新地址:', address)

  // 地址变更后重新查询商品相关信息
  await reloadGoodsInfoAfterAddressChange()
}

// 处理地址弹窗关闭事件
const handleAddressPopupClose = () => {
  // 地址弹窗关闭回调
  showAddressPopup.value = false
}

// ==================== 地址变更后数据更新 ====================
// 地址变更后重新查询商品信息
const reloadGoodsInfoAfterAddressChange = async () => {
  try {
    // 如果是京东商品，需要重新查询物流相关信息
    if (isJD.value) {
      // 重新查询物流配送时间
      await queryPredictSku()

      // 重新检查SKU是否可售
      await checkIsSkuSale()
    }

    // 重新检查白名单用户限制
    await checkWhiteUserLimit()

    // 重新查询商品限制销售区域
    await querySale()

    console.log('地址变更后商品信息更新完成')
  } catch (error) {
    console.error('地址变更后更新商品信息失败:', error)
  }

  // 如果用户已登录，进行地址检查
  if (userStore.isLogin) {
    await addressCheck()
  }
}

// ==================== 商品状态计算属性 ====================
// 商品上架状态计算属性
const onSaleState = computed(() => {
  // state状态说明：0-不能购买，1-上架，2-下架，null-状态异常
  return !(currentSku.value && currentSku.value.state === '2')
})

// 商品库存状态计算属性
const stockState = computed(() => {
  return currentSku.value && currentSku.value.stock > 0
})

// 用户购买资格状态计算属性
const userStatus = computed(() => {
  // 用户购买资格检查
  // 如果商品需要检查白名单用户且用户已登录，则返回limitState的值
  const goodsDetail = spu.value
  if (goodsDetail && goodsDetail.isCheckWhiteUser && goodsDetail.isCheckWhiteUser === '1' && userStore.isLogin) {
    return limitState.value
  }
  // 其他情况默认有购买资格
  return true
})

// ==================== 按钮状态控制 ====================
// 购物车按钮禁用状态计算属性
const cartButtonDisabledStatus = computed(() => {
  // 如果数据已获取，检查各种状态条件
  return isDataGet.value ?
    !onSaleState.value || !stockState.value || !userStatus.value || !regionalSalesState.value || !limitState.value :
    true  // 数据未获取时禁用按钮
})

// ==================== 响应式监听器 ====================
// 监听地址变化，重新查询物流信息
const watchAddress = computed(() => userStore.curAddressInfo)
watch(watchAddress, async (newAddr, oldAddr) => {
  // 检查地址是否发生实质性变化
  if (newAddr && oldAddr && (
    newAddr.provinceId !== oldAddr.provinceId ||
    newAddr.cityId !== oldAddr.cityId ||
    newAddr.countyId !== oldAddr.countyId ||
    newAddr.townId !== oldAddr.townId
  )) {
    // 如果是京东商品，重新查询物流预测信息
    if (isJD.value) {
      await queryPredictSku()
    }

    // 地址切换时更新购物车数据
    if (userStore.isLogin) {
      try {
        await newCartStore.query()
        console.log('地址切换后购物车数据已更新')
      } catch (error) {
        console.error('地址切换后更新购物车数据失败:', error)
      }
    }
  }
}, { deep: true })

// 监听规格数据变化
watch([curSpecs, () => queryDisabledSpecs()], ([newCurSpecs, newDisabledSpecs]) => {
  console.log('规格数据更新:', {
    curSpecs: newCurSpecs,
    disabledSpecs: newDisabledSpecs
  })
}, { deep: true })

// 监听当前SKU ID变化，触发滚动定位
watch(curSkuId, (newSkuId, oldSkuId) => {
  // 只有在初始加载完成后且SKU ID确实发生变化时才触发滚动
  if (newSkuId && newSkuId !== oldSkuId && initialLoadComplete.value) {
    console.log('SKU ID 变化，触发滚动:', { newSkuId, oldSkuId })
    setTimeout(() => {
      scrollToSelectedSpec()
    }, 150)
  }
})

// 监听登录状态变化，重新检查相关权限和数据
watch(() => userStore.isLogin, async (newLoginStatus, oldLoginStatus) => {
  // 只有在登录状态确实发生变化且SPU数据存在时才执行
  if (newLoginStatus !== oldLoginStatus && spu.value) {
    // 登录状态发生变化，重新检查白名单用户限制
    await checkWhiteUserLimit()

    // 如果用户登录了，重新获取营销活动信息
    if (newLoginStatus && marketTemplatesType1.value.length > 0) {
      await getElectronic()
    }

    // 登录状态变化时更新购物车数据
    if (newLoginStatus) {
      try {
        await newCartStore.query()
        console.log('登录后购物车数据已更新')
      } catch (error) {
        console.error('登录后更新购物车数据失败:', error)
      }
    }
  }
})

// ==================== 生命周期钩子 ====================
// 组件挂载时的初始化操作
onMounted(async () => {
  // 加载商品详情数据
  loadGoodsDetail()

  // 监听滚动事件，实时保存滚动位置，用于页面状态恢复
  window.addEventListener('scroll', saveScrollPosition, { passive: true })

  // 页面进入时判断登录状态，如果已登录则查询购物车数据
  if (userStore.isLogin) {
    try {
      await newCartStore.query()
      console.log('页面加载时购物车数据已更新')
    } catch (error) {
      console.error('页面加载时更新购物车数据失败:', error)
    }
  }
})

// 组件卸载时的清理操作
onUnmounted(() => {
  // 清理滚动事件监听器，避免内存泄漏
  window.removeEventListener('scroll', saveScrollPosition)
})

// ==================== 分享功能 ====================
// 分享功能初始化
const shareInit = async () => {
  const bizCode = getBizCode()

  // 根据业务代码生成不同的分享描述
  const intro = () => {
    const sku = spu.value?.skuList?.[0] || currentSku.value
    if (bizCode === 'ziying') {
      return sku.comment || '足不出户囤遍好物！购商品，来精选。'
    } else if (bizCode === 'fupin') {
      return sku.comment || '消费帮扶，共献爱心，乡村振兴，有我联通。'
    } else if (bizCode === 'fulihui') {
      return sku.comment || '足不出户囤遍好物！购商品，来福利汇。'
    } else if (bizCode === 'lnzx') {
      return sku.comment || '联农智选，好货甄选，品质可信。'
    } else {
      return sku.comment || sku.name || sku.merchantName || ''
    }
  }

  // 构建分享数据
  const firstSku = spu.value?.skuList?.[0] || currentSku.value
  shareData.title = firstSku.name || goodsInfo.value.name
  shareData.describe = intro()
  shareData.picUrl = goodsInfo.value.imageUrl || ''
  shareData.link = await getDefaultShareUrl()

  log('[GOODS-DETAIL] shareInfo', shareData)
  // 设置微信分享数据
  setWeiXinShareData(shareData)
}

// 处理右上角分享按钮点击事件
const onDropdownShare = (e) => {
  // 分享功能，不能写在异步函数中
  share(shareData, e)
}

// ==================== 营销活动状态计算 ====================
// 是否有营销位类型1的计算属性
const hasMarketingType1 = computed(() => {
  return marketTemplatesType1.value && marketTemplatesType1.value.length > 0
})

// 是否有营销位类型4的计算属性
const hasMarketingType4 = computed(() => {
  return marketTemplatesType4.value && marketTemplatesType4.value.length > 0
})
</script>

<style scoped lang="less">
.goods-detail {
  min-height: 100vh;
  background-color: #FFFFFF;
  padding-bottom: 55px;
}

.goods-content {
  background-color: #FFFFFF;
}

.image-section {
  background-color: #FFFFFF;
  padding: 0;
}
</style>
