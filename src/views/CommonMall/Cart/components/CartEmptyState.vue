<!--
/**
 * 购物车空状态组件
 *
 * 主要功能：
 * 1. 提供购物车空状态的统一展示界面，支持未登录和空购物车两种状态
 * 2. 未登录状态引导用户登录，同步购物车商品数据
 * 3. 空购物车状态引导用户去商城购物，提升用户活跃度
 * 4. 采用配置化设计，根据不同状态显示对应的图片、文案和按钮
 * 5. 集成商城名称动态获取，适配不同业务场景
 *
 * 技术特点：
 * - 使用计算属性实现配置化的状态管理
 * - 采用语义化的HTML结构，提升可访问性
 * - 支持图片懒加载，优化页面性能
 * - 使用CSS动画增强视觉效果
 * - 采用BEM命名规范，提高样式可维护性
 *
 * 使用场景：
 * - 用户未登录时的购物车页面
 * - 购物车无商品时的空状态展示
 * - 需要引导用户进行下一步操作的场景
 */
-->

<template>
  <!-- 购物车空状态容器，根据状态类型应用不同样式 -->
  <section :class="containerClass">
    <div :class="contentClass">
      <!-- 空状态插图，使用懒加载优化性能 -->
      <img
        :src="imageUrl"
        :alt="imageAlt"
        :class="imageClass"
        loading="lazy"
      />
      <!-- 状态描述文本 -->
      <p :class="textClass">{{ text }}</p>
      <!-- 操作按钮，根据状态类型显示不同文本和样式 -->
      <WoButton
        :type="buttonType"
        size="medium"
        :class="buttonClass"
        @click="handleButtonClick"
      >
        {{ buttonText }}
      </WoButton>
    </div>
  </section>
</template>

<script setup>
import { computed, toRefs } from 'vue'
import WoButton from '@components/WoElementCom/WoButton/WoButton.vue'
import { getMallName } from '@utils/curEnv.js'
import noGoodsImg from '../assets/no-goods.png'

// ==================== 组件属性定义 ====================
// 定义组件接收的props参数
const props = defineProps({
  // 空状态类型，支持登录引导和空购物车两种状态
  type: {
    type: String,
    required: true,
    validator: (value) => ['login', 'empty'].includes(value)
  }
})

// 使用toRefs解构props，保持响应性
const { type } = toRefs(props)

// ==================== 事件定义 ====================
// 定义组件向父组件发射的事件
const emit = defineEmits(['login', 'go-shopping'])

// ==================== 状态配置管理 ====================
// 根据状态类型计算对应的配置信息
const config = computed(() => {
  if (type.value === 'login') {
    // 未登录状态配置
    return {
      containerClass: 'cart-login',
      contentClass: 'cart-login__content',
      imageClass: 'cart-login__image',
      textClass: 'cart-login__text',
      buttonClass: 'cart-login__button',
      imageUrl: noGoodsImg,
      imageAlt: '购物车空空如也',
      text: '登录后可同步购物车中商品',
      buttonText: '立即登录',
      buttonType: 'gradient',
      event: 'login'
    }
  } else {
    // 空购物车状态配置
    return {
      containerClass: 'cart-empty',
      contentClass: 'cart-empty__content',
      imageClass: 'cart-empty__image',
      textClass: 'cart-empty__text',
      buttonClass: 'cart-empty__button',
      imageUrl: noGoodsImg,
      imageAlt: '购物车空空如也',
      text: '购物车空空如也，去逛逛吧~',
      buttonText: `去逛逛${getMallName()}`,
      buttonType: 'secondary',
      event: 'go-shopping'
    }
  }
})

// ==================== 样式和内容计算属性 ====================
// 从配置中提取各项属性，用于模板渲染
const containerClass = computed(() => config.value.containerClass)
const contentClass = computed(() => config.value.contentClass)
const imageClass = computed(() => config.value.imageClass)
const textClass = computed(() => config.value.textClass)
const buttonClass = computed(() => config.value.buttonClass)
const imageUrl = computed(() => config.value.imageUrl)
const imageAlt = computed(() => config.value.imageAlt)
const text = computed(() => config.value.text)
const buttonText = computed(() => config.value.buttonText)
const buttonType = computed(() => config.value.buttonType)

// ==================== 事件处理函数 ====================
// 处理按钮点击事件，根据配置发射对应的事件
const handleButtonClick = () => {
  emit(config.value.event)
}
</script>

<style scoped lang="less">
// 未登录状态样式
.cart-login {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 60vh;
  padding: 40px 20px;
  text-align: center;
  background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
  position: relative;
  contain: layout style paint;

  &__content {
    display: flex;
    flex-direction: column;
    align-items: center;
  }

  &__image {
    width: 180px;
    height: 160px;
    margin-bottom: 24px;
    opacity: 0.9;
    filter: drop-shadow(0 4px 12px rgba(0, 0, 0, 0.1));
    position: relative;
    z-index: 1;
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
    animation: cart-float 3s ease-in-out infinite;
    will-change: transform;
  }

  &__text {
    font-size: 13px;
    color: #4A5568;
    margin-bottom: 10px;
    line-height: 1.5;
    font-weight: 400;
    position: relative;
    z-index: 1;
  }

  &__button {
    width: 200px;
    //height: 44px;
    position: relative;
    z-index: 1;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }
}

// 空购物车状态样式
.cart-empty {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 60vh;
  contain: layout style paint;

  &__content {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
  }

  &__image {
    width: 180px;
    height: 160px;
    margin-bottom: 10px;
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
  }

  &__text {
    font-size: 14px;
    color: #718096;
    margin-bottom: 10px;
    line-height: 1.5;
  }

  &__button {
    width: 200px;
  }
}

// 浮动动画
@keyframes cart-float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-8px);
  }
}

// 性能优化
.cart-login,
.cart-empty {
  transform: translateZ(0);
  backface-visibility: hidden;
}
</style>
