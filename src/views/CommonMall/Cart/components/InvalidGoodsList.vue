<!--
/**
 * 失效商品列表组件
 *
 * 主要功能：
 * 1. 展示购物车中所有失效不可购买的商品列表
 * 2. 提供失效商品数量统计显示，让用户了解失效商品情况
 * 3. 支持一键清空所有失效商品功能，简化用户操作
 * 4. 集成失效商品项组件，提供基本的商品信息展示
 * 5. 支持查看相似商品功能，为用户提供替代选择
 * 6. 采用卡片式布局，与有效商品区分显示
 *
 * 技术特点：
 * - 使用条件渲染，只在有失效商品时显示
 * - 采用语义化的HTML结构，提升可访问性
 * - 使用事件透传模式，简化组件间通信
 * - 集成WoCard组件，保持界面风格统一
 * - 支持按钮点击反馈效果
 *
 * 使用场景：
 * - 购物车页面的失效商品展示
 * - 需要清理失效商品的场景
 * - 为用户提供商品替代选择的场景
 */
-->

<template>
  <!-- 失效商品卡片容器，仅在有失效商品时显示 -->
  <WoCard v-if="hasInvalidGoods" class="cart-goods__invalid">
    <!-- ===================== 失效商品头部区域 ===================== -->
    <header class="cart-goods__invalid-header">
      <!-- 失效商品标题，显示失效商品数量 -->
      <h3 class="cart-goods__invalid-title">{{ invalidGoodsCount }}件失效商品</h3>
      <!-- 一键清空按钮，支持批量删除所有失效商品 -->
      <button class="cart-goods__invalid-action" @click="handleClearInvalidGoods" type="button">
        一键清空
      </button>
    </header>

    <!-- ===================== 失效商品列表区域 ===================== -->
    <div class="cart-goods__invalid-list">
      <!-- 遍历失效商品列表，渲染每个失效商品项 -->
      <InvalidGoodsItem
        v-for="item in invalidGoodsList"
        :key="item.cartSkuId"
        :item="item"
        :invalid-count="invalidGoodsCount"
        @toggle-select="handleToggleItemSelect"
        @look-similar="handleLookSimilar"
      />
    </div>
  </WoCard>
</template>

<script setup>
import { toRefs } from 'vue'
import WoCard from '@components/WoElementCom/WoCard.vue'
import InvalidGoodsItem from './InvalidGoodsItem.vue'

// ==================== 组件属性定义 ====================
// 定义组件接收的props参数
const props = defineProps({
  // 是否存在失效商品的标识
  hasInvalidGoods: {
    type: Boolean,
    required: true
  },
  // 失效商品列表数据
  invalidGoodsList: {
    type: Array,
    required: true
  },
  // 失效商品总数量
  invalidGoodsCount: {
    type: Number,
    required: true
  }
})

// 使用toRefs解构props，保持响应性
const { hasInvalidGoods, invalidGoodsList, invalidGoodsCount } = toRefs(props)

// ==================== 事件定义 ====================
// 定义组件向父组件发射的事件
const emit = defineEmits([
  'clear-invalid-goods',    // 清空失效商品
  'toggle-item-select',     // 商品选择切换
  'look-similar'            // 查看相似商品
])

// ==================== 事件处理函数 ====================
// 处理一键清空失效商品操作
const handleClearInvalidGoods = () => {
  emit('clear-invalid-goods')
}

// 处理失效商品选择切换操作
const handleToggleItemSelect = (item) => {
  emit('toggle-item-select', item)
}

// 处理查看相似商品操作
const handleLookSimilar = (item) => {
  emit('look-similar', item)
}
</script>

<style scoped lang="less">
.cart-goods__invalid {
  margin-top: 10px;
  contain: layout style;
}

.cart-goods__invalid-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.cart-goods__invalid-title {
  font-size: 14px;
  font-weight: 600;
  color: #171E24;
  margin: 0;
}

.cart-goods__invalid-action {
  font-size: 14px;
  color: var(--wo-biz-theme-color);
  font-weight: 600;
  background: none;
  border: none;
  padding: 0;
  cursor: pointer;
  touch-action: manipulation;
  transition: opacity 0.2s ease;

  &:active {
    opacity: 0.7;
  }
}
</style>
