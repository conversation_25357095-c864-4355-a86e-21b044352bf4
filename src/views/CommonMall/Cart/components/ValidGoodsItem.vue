<!--
/**
 * 有效商品项组件
 *
 * 主要功能：
 * 1. 展示单个有效商品的完整信息，包括图片、名称、规格、价格等
 * 2. 支持商品选择功能，提供复选框交互
 * 3. 集成数量调整功能，支持步进器和直接点击修改
 * 4. 提供滑动删除功能，支持左滑显示删除按钮
 * 5. 支持长按操作菜单，提供更多操作选项
 * 6. 集成赠品展示功能，支持查看商品赠品详情
 * 7. 支持商品内容点击跳转到详情页
 * 8. 提供相似商品推荐功能
 *
 * 技术特点：
 * - 使用van-swipe-cell实现滑动删除功能
 * - 集成触摸事件处理，支持长按手势识别
 * - 采用响应式数据绑定，实时更新商品状态
 * - 支持图片懒加载和预加载优化
 * - 使用计算属性优化性能和代码可读性
 * - 集成业务逻辑判断，适配不同业务场景
 *
 * 使用场景：
 * - 购物车页面的商品项展示
 * - 需要支持商品操作的列表场景
 * - 商品管理和编辑功能
 */
-->

<template>
  <!-- 商品项滑动容器，支持左滑删除功能 -->
  <van-swipe-cell
    class="cart-item"
    :disabled="item.showLongPressMenu"
    @open="handleSwipeOpen"
    @close="handleSwipeClose"
  >
    <!-- 商品卡片容器，根据滑动状态调整样式 -->
    <WoCard
      :ref="handleSetRef"
      class="cart-item__card"
      :class="{ 'cart-item__card--no-radius': item.isSwipeOpen }"
    >
      <!-- ===================== 商品选择区域 ===================== -->
      <!-- 商品选择复选框，支持点击切换选中状态 -->
      <div class="cart-item__checkbox" @click="handleToggleSelect">
        <img :src="checkboxSrc" alt="选择商品" class="cart-item__checkbox-icon" loading="lazy" />
      </div>

      <!-- ===================== 商品内容区域 ===================== -->
      <!-- 商品主要内容区域，支持点击跳转和长按操作 -->
      <div
        class="cart-item__content"
        @click="handleContentClick"
        @touchstart="handleTouchStart"
        @touchend="handleTouchEnd"
        @touchcancel="handleTouchCancel"
        @touchmove="handleTouchMove"
      >
        <!-- 商品图片区域 -->
        <div class="cart-item__image">
          <!-- 商品主图，根据位置优化加载策略 -->
          <img
            :src="goodsInfo.imageUrl"
            :alt="goodsInfo.name"
            :loading="itemIndex < 3 ? 'eager' : 'lazy'"
            decoding="async"
            fetchpriority="high"
          />
        </div>

        <!-- 商品信息区域 -->
        <div class="cart-item__info">
          <!-- 商品头部信息，包含名称和数量 -->
          <div class="cart-item__header">
            <!-- 商品名称 -->
            <h3 class="cart-item__name">{{ goodsInfo.name }}</h3>
            <!-- 商品数量控制区域 -->
            <div class="cart-item__quantity">
              <!-- 数量显示模式，点击可切换到编辑模式 -->
              <template v-if="!item.stepperVisible">
                <span @click.stop="handleShowStepper" class="cart-item__quantity-text">x{{ item.skuNum }}</span>
              </template>
              <!-- 数量编辑模式，使用步进器组件 -->
              <van-stepper
                v-else
                v-model="localSkuNum"
                :min="skuNumMin"
                :max="skuNumMax"
                button-size="22px"
                @change="handleQuantityChange"
                @blur="handleQuantityBlur"
              />
            </div>
          </div>

          <!-- 商品标签区域 -->
          <div class="cart-item__tags">
            <!-- 规格标签和赠品标签 -->
            <div class="cart-item__spec-tags">
              <!-- 商品规格标签列表 -->
              <span
                v-for="(specItem, specIndex) in displaySpecs"
                :key="specIndex"
                class="cart-item__spec-tag"
              >
                {{ specItem }}
              </span>
              <!-- 赠品标签，点击可查看赠品详情 -->
              <span v-if="isGiftsAvailable" class="cart-item__gift-tag" @click="showGiftDetailList">
                赠品
              </span>
            </div>
            <!-- 限购信息标签 -->
            <div v-if="limitInfo.text" class="cart-item__limit-tag">
              {{ limitInfo.text }}
            </div>
          </div>

          <!-- 商品价格区域 -->
          <div class="cart-item__price">
            <!-- 政企商城价格显示逻辑 -->
            <template v-if="isZqBiz && (goodsInfo.highPrice || goodsInfo.lowPrice)">
              <!-- 政企商城显示价格区间 -->
              <PriceDisplay
                :high-price="goodsInfo.highPrice"
                :low-price="goodsInfo.lowPrice"
                range-label="参考价"
                size="small"
                color="orange"
              />
            </template>
            <!-- 普通商城显示单一价格 -->
            <template v-else>
              <PriceDisplay :price="item.nowPrice" size="small" color="orange" />
            </template>
          </div>
        </div>
      </div>

      <!-- ===================== 长按操作菜单 ===================== -->
      <!-- 长按触发的浮层菜单，提供快捷操作选项 -->
      <div v-if="item.showLongPressMenu" class="cart-item__menu" @click="handleCloseMenu">
        <!-- 菜单遮罩层，点击可关闭菜单 -->
        <div class="cart-item__menu-overlay"></div>
        <!-- 菜单操作按钮组 -->
        <div class="cart-item__menu-actions">
          <!-- 查看相似商品按钮 -->
          <button
            class="cart-item__menu-btn cart-item__menu-btn--similar"
            @click.stop="handleLookSimilarAndCloseMenu(goodsInfo.goodsId)"
          >
            看相似
          </button>
          <!-- 删除商品按钮 -->
          <button
            class="cart-item__menu-btn cart-item__menu-btn--delete"
            @click.stop="handleDeleteItemAndCloseMenu(goodsInfo)"
          >
            删除
          </button>
        </div>
      </div>
    </WoCard>

    <!-- ===================== 滑动操作区域 ===================== -->
    <!-- 左滑显示的操作按钮组 -->
    <template #right>
      <div class="cart-item__swipe-actions">
        <!-- 查看相似商品按钮 -->
        <button
          class="cart-item__swipe-btn cart-item__swipe-btn--similar"
          @click="handleLookSimilar(goodsInfo.goodsId)"
        >
          看相似
        </button>
        <!-- 删除商品按钮 -->
        <button
          class="cart-item__swipe-btn cart-item__swipe-btn--delete"
          @click="handleDeleteItem(goodsInfo)"
        >
          删除
        </button>
      </div>
    </template>
  </van-swipe-cell>
</template>

<script setup>
import { computed, toRefs, ref, watch, shallowRef, onBeforeUnmount } from 'vue'
import WoCard from '@components/WoElementCom/WoCard.vue'
import PriceDisplay from '@components/Common/PriceDisplay.vue'
import noSelectImg from '@/static/images/no-select.png'
import woSelectImg from '@/static/images/wo-select.png'
import jdSelectImg from '@/static/images/jd-select.png'
import { showToast } from 'vant'
import { getBizCode } from '@utils/curEnv.js'

// ==================== 组件属性定义 ====================
// 定义组件接收的props参数
const props = defineProps({
  // 商品项数据对象，包含商品的所有信息
  item: {
    type: Object,
    required: true
  },
  // 组件引用键值，用于父组件获取子组件实例
  refKey: {
    type: String,
    required: true
  },
  // 编辑模式状态，控制商品项的编辑功能显示
  isEditMode: {
    type: Boolean,
    default: false
  },
  // 编辑模式下是否被选中的状态
  isEditSelected: {
    type: Boolean,
    default: false
  },
  // 商品在列表中的索引位置，用于优化加载策略
  itemIndex: {
    type: Number,
    default: 0
  }
})

// 使用toRefs解构props，保持响应性
const { item, refKey, isEditMode, isEditSelected, itemIndex } = toRefs(props)

// ==================== 事件定义 ====================
// 定义组件向父组件发射的事件
const emit = defineEmits([
  'toggle-select',      // 商品选择切换
  'show-stepper',       // 显示数量步进器
  'quantity-change',    // 商品数量变更
  'look-similar',       // 查看相似商品
  'delete-item',        // 删除商品
  'close-menu',         // 关闭菜单
  'swipe-open',         // 滑动菜单打开
  'swipe-close',        // 滑动菜单关闭
  'set-ref',            // 设置组件引用
  'long-press',         // 长按操作
  'gift-click',         // 赠品点击
  'content-click'       // 内容点击
])

// ==================== 业务逻辑计算 ====================
// 判断是否为政企商城，用于控制价格显示逻辑
const isZqBiz = computed(() => getBizCode() === 'zq')

// ==================== 选择状态计算 ====================
// 根据编辑模式和业务类型计算复选框图标
const checkboxSrc = computed(() => {
  // 判断是否为有购京东业务，使用不同的选中图标
  const isYgjd = getBizCode() === 'ygjd'
  const selectedImg = isYgjd ? jdSelectImg : woSelectImg

  // 根据编辑模式使用不同的选中状态判断逻辑
  return isEditMode.value
    ? (isEditSelected.value ? selectedImg : noSelectImg)
    : (item.value.selected === 'true' ? selectedImg : noSelectImg)
})

// ==================== 商品信息计算 ====================
// 从商品数据中提取和计算显示所需的信息
const goodsInfo = computed(() => {
  const currentSku = item.value?.goods?.skuList?.[0] || {}
  const limitTemplate = item.value?.goods?.limitTemplate || {}

  return {
    goodsId: currentSku.goodsId,
    skuId: currentSku.skuId,
    name: currentSku.name || '',
    imageUrl: currentSku.listImageUrl || '',
    params: setNewParam(currentSku),
    lowestBuy: currentSku.lowestBuy || '1',
    limitTemplate,
    highPrice: currentSku?.highPrice || 0,
    lowPrice: currentSku?.lowPrice || 0
  }
})

// ==================== 规格标签计算 ====================
// 计算商品规格标签的显示内容
const displaySpecs = computed(() => {
  // 优先使用处理后的参数
  if (goodsInfo.value.params.length > 0) {
    return goodsInfo.value.params
  }
  // 降级使用原始规格数据
  if (Array.isArray(item.value.spec)) {
    return item.value.spec
  }
  return []
})

// ==================== 限购信息计算 ====================
// 计算商品的限购信息显示
const limitInfo = computed(() => {
  const goods = item.value

  // 检查是否为限购商品
  if (isXG(goods, '1')) {
    return {
      text: `每人每次限购${goodsInfo.value.limitTemplate.limitNum}件`,
      type: 'perTime'
    }
  }

  if (isXG(goods, '2')) {
    return {
      text: `每人限购${goodsInfo.value.limitTemplate.limitNum}件`,
      type: 'perPerson'
    }
  }

  if (isLowestBuy(goods)) {
    return {
      text: `${goodsInfo.value.lowestBuy}件起购`,
      type: 'minimum'
    }
  }

  return { text: '', type: '' }
})

// 数量相关状态和逻辑
const localSkuNum = ref(item.value.skuNum)

// 监听数量变化
watch(() => item.value.skuNum, (newVal) => {
  localSkuNum.value = newVal
})

// 监听滑动状态，确保滑动时清除长按定时器
watch(() => item.value.isSwipeOpen, (newVal) => {
  if (newVal && touchTimer.value) {
    clearTimeout(touchTimer.value)
    touchTimer.value = null
    isLongPressed.value = false
  }
  isSwiping.value = newVal
})

// 监听长按菜单状态，确保长按菜单显示时清除相关状态
watch(() => item.value.showLongPressMenu, (newVal) => {
  if (newVal) {
    // 长按菜单显示时，清除滑动相关状态
    isSwiping.value = false
    if (touchTimer.value) {
      clearTimeout(touchTimer.value)
      touchTimer.value = null
    }
  } else {
    // 长按菜单关闭时，重置长按状态
    isLongPressed.value = false
  }
})

// 步进器最小值
const skuNumMin = computed(() => {
  const goods = item.value
  return goods.goods.skuList[0]?.lowestBuy ? Number(goods.goods.skuList[0]?.lowestBuy) : 1
})

// 步进器最大值
const skuNumMax = computed(() => {
  const goods = item.value
  const skuNum = goods.skuNum
  const stock = goods.goods.skuList[0].stock

  if (isXG(goods, '1') || isXG(goods, '2')) {
    return +goods.goods.limitTemplate.limitNum
  }

  if (Number(skuNum) >= Number(stock)) {
    return +skuNum
  } else if (Number(skuNum) <= Number(stock)) {
    return +stock
  }

  return +stock
})

// 数量变化处理
const handleQuantityChange = (value) => {
  const diff = value - item.value.skuNum
  handleQuantityUpdate(value, diff, false)
}

// 数量失焦处理
const handleQuantityBlur = () => {
  if (localSkuNum.value !== item.value.skuNum) {
    handleQuantityUpdate(localSkuNum.value, 0, true)
  }
}

// 数量更新统一处理
const handleQuantityUpdate = (targetNum, diff, isInput) => {
  const goods = item.value
  let finalNum = targetNum
  let shouldUpdate = true
  let toastMessage = ''

  // 基础验证
  if (targetNum < 1) {
    toastMessage = '最少购买1件哦！'
    finalNum = 1
    shouldUpdate = false
  } else if (targetNum > goods.stock) {
    toastMessage = `最多购买${goods.stock}件哦！`
    finalNum = goods.stock
    shouldUpdate = false
  }

  // 起购数量验证
  if (shouldUpdate && isLowestBuy(goods)) {
    const lowestBuy = goods.goods.skuList[0]?.lowestBuy || 1

    if (isInput) {
      if (targetNum < lowestBuy) {
        toastMessage = `最少购买${lowestBuy}件哦！`
        finalNum = lowestBuy
      }
    } else {
      if (diff > 0) {
        if (goods.skuNum < lowestBuy) {
          finalNum = lowestBuy
          toastMessage = `最少购买${lowestBuy}件哦！`
        }
      } else if (diff < 0) {
        if (targetNum < lowestBuy) {
          toastMessage = `最少购买${lowestBuy}件哦！`
          shouldUpdate = false
        }
      }
    }
  }

  // 限购验证
  if (shouldUpdate && (isXG(goods, '1') || isXG(goods, '2'))) {
    const limitNum = goods.goods.limitTemplate?.limitNum || 0
    if (targetNum > limitNum) {
      toastMessage = `最多购买${limitNum}件哦！`
      finalNum = limitNum
      shouldUpdate = false
    }
  }

  // 显示提示
  if (toastMessage) {
    showToast(toastMessage)
  }

  // 更新本地显示
  if (finalNum !== localSkuNum.value) {
    localSkuNum.value = finalNum
  }

  // 触发更新事件
  if (shouldUpdate && finalNum !== goods.skuNum) {
    emit('quantity-change', { ...goods, skuNum: finalNum })
  }
}


// 赠品相关逻辑
const isGiftsAvailable = computed(() => {
  const giftList = item.value?.goods?.skuList?.[0]?.giftList
  if (!giftList || !Array.isArray(giftList) || giftList.length === 0) {
    return false
  }

  const giftBelongToSkuMinNum = giftList[0]?.belongToSkuMinNum || 99999
  const goodsNum = item.value?.skuNum || 0

  return goodsNum >= giftBelongToSkuMinNum
})

const showGiftDetailList = () => {
  const giftList = item.value?.goods?.skuList?.[0]?.giftList || []
  emit('gift-click', {
    goods: props.item,
    giftList: giftList
  })
}

// 工具方法
const setNewParam = (sku) => {
  const params = []
  if (sku.param) params.push(sku.param)
  if (sku.param1) params.push(sku.param1)
  if (sku.param2) params.push(sku.param2)
  if (sku.param3) params.push(sku.param3)
  if (sku.param4) params.push(sku.param4)
  return params
}

const isXG = (skuInfo, fix) => {
  if (skuInfo.goods.isXg === '1' && fix === '1') {
    return skuInfo.goods.limitTemplate?.limitCountType === '1' && skuInfo.goods.limitTemplate?.limitNum
  } else if (skuInfo.goods.isXg === '1' && fix === '2') {
    return skuInfo.goods.limitTemplate?.limitCountType === '2' && skuInfo.goods.limitTemplate?.limitNum
  }
  return false
}

const isLowestBuy = (skuInfo) => {
  return skuInfo.goods.skuList.some(item => item?.lowestBuy > '1')
}

// 事件处理方法
// ==================== 基础事件处理函数 ====================
// 处理商品选择状态切换
const handleToggleSelect = () => {
  emit('toggle-select', props.item)
}

// 处理显示数量步进器
const handleShowStepper = () => {
  emit('show-stepper', props.item)
}

// 处理查看相似商品操作
const handleLookSimilar = (id) => {
  emit('look-similar', id)
}

// 处理删除商品操作
const handleDeleteItem = (goodsInfo) => {
  emit('delete-item', [{
    goodsId: goodsInfo.goodsId,
    skuId: goodsInfo.skuId,
  }])
}

// 处理关闭长按菜单
const handleCloseMenu = () => {
  emit('close-menu')
}

// ==================== 滑动操作处理函数 ====================
// 处理滑动菜单打开
const handleSwipeOpen = () => {
  // 清除长按定时器和状态，避免滑动时触发长按
  if (touchTimer.value) {
    clearTimeout(touchTimer.value)
    touchTimer.value = null
  }
  isLongPressed.value = false
  isSwiping.value = true
  emit('swipe-open', props.item)
}

// 处理滑动菜单关闭
const handleSwipeClose = () => {
  // 延迟重置滑动状态，避免立即触发点击事件
  setTimeout(() => {
    isSwiping.value = false
  }, 100)
  emit('swipe-close', props.item)
}

// 处理设置组件引用
const handleSetRef = (el) => {
  emit('set-ref', el, refKey.value)
}

// ==================== 组合操作处理函数 ====================
// 处理长按菜单中的查看相似商品并关闭菜单
const handleLookSimilarAndCloseMenu = (id) => {
  emit('look-similar', id)
  emit('close-menu')
}

// 处理长按菜单中的删除商品并关闭菜单
const handleDeleteItemAndCloseMenu = (goodsInfo) => {
  emit('delete-item', [{
    goodsId: goodsInfo.goodsId,
    skuId: goodsInfo.skuId,
  }])
  emit('close-menu')
}

// 长按事件处理
const touchTimer = shallowRef(null)
const isLongPressed = ref(false)
const isSwiping = ref(false)

const handleTouchStart = () => {
  // 如果正在滑动或已经滑动打开，不触发长按
  if (isSwiping.value || item.value.isSwipeOpen) return

  isLongPressed.value = false
  touchTimer.value = setTimeout(() => {
    isLongPressed.value = true
    emit('long-press', props.item)
  }, 500)
}

const handleTouchEnd = () => {
  if (touchTimer.value) {
    clearTimeout(touchTimer.value)
    touchTimer.value = null
  }
}

const handleTouchCancel = () => {
  if (touchTimer.value) {
    clearTimeout(touchTimer.value)
    touchTimer.value = null
  }
}

const handleTouchMove = () => {
  // 触摸移动时取消长按定时器，防止滑动时触发长按
  if (touchTimer.value) {
    clearTimeout(touchTimer.value)
    touchTimer.value = null
  }
}

// 点击商品内容区域跳转到详情页
const handleContentClick = (event) => {
  // 防止事件冒泡
  event.stopPropagation()

  // 如果刚刚长按过、正在滑动、滑动已打开或显示长按菜单，不触发点击跳转
  if (isLongPressed.value || isSwiping.value || item.value.isSwipeOpen || item.value.showLongPressMenu) {
    isLongPressed.value = false
    return
  }

  // 如果点击的是数量区域或赠品标签，不触发跳转
  const target = event.target
  if (target.closest('.cart-item__quantity') ||
    target.closest('.cart-item__gift-tag') ||
    target.closest('.van-stepper')) {
    return
  }

  emit('content-click', {
    goodsId: goodsInfo.value.goodsId,
    skuId: goodsInfo.value.skuId
  })
}

// 组件卸载时清理定时器
onBeforeUnmount(() => {
  if (touchTimer.value) {
    clearTimeout(touchTimer.value)
    touchTimer.value = null
  }
})
</script>

<style scoped lang="less">
// 购物车商品项组件样式 - 采用BEM命名规范
.cart-item {
  border-radius: 10px;
  overflow: hidden; // 确保圆角效果
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1); // 添加丝滑的过渡效果

  // 优化 van-swipe-cell 的样式
  :deep(.van-swipe-cell__wrapper) {
    border-radius: 10px;
    transition: border-radius 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  // 当滑动被禁用时的样式
  &:deep(.van-swipe-cell--disabled) {
    .van-swipe-cell__wrapper {
      transform: translateX(0) !important;
    }
  }

  // 卡片容器
  &__card {
    display: flex;
    align-items: center;
    position: relative;
    margin-bottom: 10px;
    transition: border-radius 0.3s cubic-bezier(0.4, 0, 0.2, 1); // 添加丝滑的圆角过渡

    :deep(.card-content) {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    // 滑动时去除右侧圆角
    &--no-radius {
      border-top-right-radius: 0;
      border-bottom-right-radius: 0;
    }
  }

  // 选择框区域
  &__checkbox {
    margin-right: 5px;
    display: flex;
    align-items: center;
    justify-content: center;

    &-icon {
      width: 18px;
      height: 18px;
      // 优化图片渲染
      image-rendering: -webkit-optimize-contrast;
      image-rendering: crisp-edges;
    }
  }

  // 商品内容区域
  &__content {
    display: flex;
    flex: 1;
    overflow: hidden;
  }

  // 商品图片
  &__image {
    width: 75px;
    height: 75px;
    border-radius: 4px;
    overflow: hidden;
    margin-right: 10px;
    flex-shrink: 0; // 防止图片被压缩

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
      // 优化图片渲染性能
      image-rendering: -webkit-optimize-contrast;
      image-rendering: crisp-edges;
      // 使用GPU加速
      transform: translateZ(0);
      will-change: transform;
      // 优化图片加载性能
      content-visibility: auto;
      contain-intrinsic-size: 75px 75px;
    }
  }

  // 商品信息区域
  &__info {
    flex: 1;
    overflow: hidden;
    min-width: 0; // 确保flex子元素可以收缩
  }

  // 商品头部（名称和数量）
  &__header {
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    margin-bottom: 5px;
    gap: 10px;
  }

  // 商品名称
  &__name {
    flex: 1;
    font-size: 13px;
    color: #171E24;
    line-height: 1.5;
    margin: 0; // 重置h3默认margin
    font-weight: normal; // 重置h3默认font-weight
    display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
        word-break: break-all;
  }

  // 商品数量区域
  &__quantity {
    flex-shrink: 0;
    display: flex;
    align-items: center;
    justify-content: flex-end;

    &-text {
      font-size: 14px;
      color: #718096;
      line-height: 1.5;
      cursor: pointer;
      user-select: none;
    }
  }

  // 标签区域
  &__tags {
    margin-bottom: 5px;
  }

  // 规格标签容器
  &__spec-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 5px 5px; // 使用gap替代margin
    margin-bottom: 5px;
  }

  // 规格标签
  &__spec-tag {
    display: inline-block;
    font-size: 11px;
    padding: 0 5px;
    background-color: #FFFFFF;
    border: 1px solid rgba(232, 232, 232, 1);
    border-radius: 2px;
    height: 17px;
    min-width: 16px;
    line-height: 17px;
    text-align: center;
    color: #718096;
    overflow: hidden; text-overflow: ellipsis; white-space: nowrap;;
  }

  // 赠品标签
  &__gift-tag {
    display: inline-block;
    font-size: 11px;
    padding: 0 5px;
    background-color: #FFFFFF;
    border: 1px solid #FF4141;
    border-radius: 2px;
    height: 17px;
    min-width: 16px;
    line-height: 17px;
    text-align: center;
    color: #FF4141;
    cursor: pointer;
    user-select: none;
    transition: opacity 0.2s ease;
    overflow: hidden; text-overflow: ellipsis; white-space: nowrap;;

    &:hover {
      opacity: 0.8;
    }

    &:active {
      transform: scale(0.95);
    }
  }

  // 限制标签
  &__limit-tag {
    display: inline-block;
    font-size: 11px;
    color: #FF4141;
    overflow: hidden; text-overflow: ellipsis; white-space: nowrap;;
  }

  // 价格区域
  &__price {
    margin-top: auto; // 价格区域置底
  }

  // 长按菜单
  &__menu {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 999;

    &-overlay {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background-color: rgba(0, 0, 0, 0.5);
      border-radius: 10px;
      backdrop-filter: blur(2px); // 添加模糊效果
    }

    &-actions {
      position: absolute;
      left: 50%;
      top: 50%;
      transform: translate(-50%, -50%);
      display: flex;
      gap: 50px;
      z-index: 1000;
    }

    &-btn {
      width: 58px;
      height: 58px;
      border-radius: 50%;
      border: none;
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      font-size: 13px;
      cursor: pointer;
      user-select: none;
      transition: transform 0.2s ease;

      &:active {
        transform: scale(0.95);
      }

      &--similar {
        background-color: #FF780A;
      }

      &--delete {
        background-color: #FF0F0F;
      }
    }
  }

  // 滑动操作按钮
  &__swipe-actions {
    display: flex;
    height: 100%;
    border-radius: 0 10px 10px 0; // 确保右侧圆角
    overflow: hidden; // 确保子元素不会超出圆角
  }

  &__swipe-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 65px;
    height: 100%;
    color: white;
    font-size: 14px;
    border: none;
    cursor: pointer;
    user-select: none;
    transition: all 0.2s ease;

    &:active {
      opacity: 0.8;
      transform: scale(0.98);
    }

    &--similar {
      background-color: #FF780A;
    }

    &--delete {
      background-color: #FF0F0F;
    }
  }
}

// 性能优化：使用contain属性
.cart-item__card {
  contain: layout style paint;
  // 优化渲染性能
  content-visibility: auto;
  contain-intrinsic-size: auto 95px;
}
</style>
