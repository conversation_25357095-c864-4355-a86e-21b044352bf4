<!--
/**
 * 购物车加载骨架屏组件
 *
 * 主要功能：
 * 1. 提供购物车页面的加载状态占位效果，提升用户体验
 * 2. 支持头部区域的骨架屏显示，包含地址选择和编辑按钮占位
 * 3. 提供商品列表的骨架屏效果，模拟真实商品卡片布局
 * 4. 使用流光动画效果，增强加载状态的视觉反馈
 * 5. 支持可配置的头部显示控制，适应不同场景需求
 *
 * 技术特点：
 * - 使用CSS动画实现流光效果，提供良好的视觉反馈
 * - 采用语义化的HTML结构，提升可访问性
 * - 优化骨架屏数量，平衡性能和用户体验
 * - 使用BEM命名规范，提高样式可维护性
 *
 * 使用场景：
 * - 购物车数据加载中的占位显示
 * - 页面初始化时的加载状态
 * - 网络请求过程中的用户体验优化
 */
-->

<template>
  <!-- 购物车加载骨架屏容器 -->
  <section class="cart-loading">
    <!-- ===================== 头部骨架屏区域 ===================== -->
    <!-- 根据showHeader属性控制头部骨架屏的显示 -->
    <header v-if="showHeader" class="cart-loading__header">
      <!-- 地址选择区域骨架屏 -->
      <div class="cart-loading__address">
        <!-- 位置图标占位 -->
        <div class="cart-loading__location-icon"></div>
        <!-- 地址文本占位 -->
        <div class="cart-loading__address-text"></div>
        <!-- 箭头图标占位 -->
        <div class="cart-loading__arrow-icon"></div>
      </div>
      <!-- 编辑按钮占位 -->
      <div class="cart-loading__edit-btn"></div>
    </header>

    <!-- ===================== 商品列表骨架屏区域 ===================== -->
    <!-- 显示3个商品卡片骨架屏，平衡性能和视觉效果 -->
    <WoCard class="cart-loading__item" v-for="n in 3" :key="n">
      <div class="cart-loading__content">
        <!-- 商品选择框占位 -->
        <div class="cart-loading__checkbox"></div>
        <!-- 商品图片占位 -->
        <div class="cart-loading__image"></div>
        <!-- 商品信息区域占位 -->
        <div class="cart-loading__info">
          <!-- 商品头部信息，包含名称和数量 -->
          <div class="cart-loading__goods-header">
            <!-- 商品名称占位 -->
            <div class="cart-loading__name"></div>
            <!-- 商品数量占位 -->
            <div class="cart-loading__quantity"></div>
          </div>
          <!-- 商品规格标签占位 -->
          <div class="cart-loading__tags">
            <div class="cart-loading__spec-tag"></div>
            <div class="cart-loading__spec-tag"></div>
          </div>
          <!-- 商品价格占位 -->
          <div class="cart-loading__price"></div>
        </div>
      </div>
    </WoCard>
  </section>
</template>

<script setup>
import { toRefs } from 'vue'
import WoCard from '@components/WoElementCom/WoCard.vue'

// ==================== 组件属性定义 ====================
// 定义组件接收的props参数
const props = defineProps({
  // 是否显示头部骨架屏，用于控制不同场景下的显示需求
  showHeader: {
    type: Boolean,
    default: true
  }
})

// 使用toRefs解构props，保持响应性
const { showHeader } = toRefs(props)
</script>

<style scoped lang="less">
.cart-loading {
  // 头部骨架屏样式
  &__header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
    gap: 15px;
  }

  &__address {
    display: flex;
    align-items: center;
    flex: 1;
    overflow: hidden;
  }

  &__location-icon {
    width: 11px;
    height: 12px;
    margin-right: 5px;
    flex-shrink: 0;
    border-radius: 2px;
    background: linear-gradient(90deg, #f2f2f2 25%, rgba(255, 255, 255, 0.5) 37%, #f2f2f2 63%);
    background-size: 400% 100%;
    animation: skeleton-loading 1.4s ease-in-out infinite;
  }

  &__address-text {
    flex: 1;
    height: 12px;
    border-radius: 4px;
    background: linear-gradient(90deg, #f2f2f2 25%, rgba(255, 255, 255, 0.5) 37%, #f2f2f2 63%);
    background-size: 400% 100%;
    animation: skeleton-loading 1.4s ease-in-out infinite;
  }

  &__arrow-icon {
    width: 5px;
    height: 9px;
    margin-left: 5px;
    flex-shrink: 0;
    border-radius: 2px;
    background: linear-gradient(90deg, #f2f2f2 25%, rgba(255, 255, 255, 0.5) 37%, #f2f2f2 63%);
    background-size: 400% 100%;
    animation: skeleton-loading 1.4s ease-in-out infinite;
  }

  &__edit-btn {
    width: 30px;
    height: 12px;
    flex-shrink: 0;
    border-radius: 4px;
    background: linear-gradient(90deg, #f2f2f2 25%, rgba(255, 255, 255, 0.5) 37%, #f2f2f2 63%);
    background-size: 400% 100%;
    animation: skeleton-loading 1.4s ease-in-out infinite;
  }

  // 商品列表骨架屏样式
  &__item {
    margin-bottom: 10px;
  }

  &__content {
    display: flex;
    align-items: center;
    position: relative;
  }

  &__checkbox {
    width: 18px;
    height: 18px;
    border-radius: 50%;
    margin-right: 5px;
    flex-shrink: 0;
    background: linear-gradient(90deg, #f2f2f2 25%, rgba(255, 255, 255, 0.5) 37%, #f2f2f2 63%);
    background-size: 400% 100%;
    animation: skeleton-loading 1.4s ease-in-out infinite;
  }

  &__image {
    width: 75px;
    height: 75px;
    border-radius: 4px;
    margin-right: 10px;
    flex-shrink: 0;
    background: linear-gradient(90deg, #f2f2f2 25%, rgba(255, 255, 255, 0.5) 37%, #f2f2f2 63%);
    background-size: 400% 100%;
    animation: skeleton-loading 1.4s ease-in-out infinite;
  }

  &__info {
    flex: 1;
    overflow: hidden;
    min-width: 0;
  }

  &__goods-header {
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    margin-bottom: 5px;
    gap: 10px;
  }

  &__name {
    flex: 1;
    height: 16px;
    border-radius: 4px;
    background: linear-gradient(90deg, #f2f2f2 25%, rgba(255, 255, 255, 0.5) 37%, #f2f2f2 63%);
    background-size: 400% 100%;
    animation: skeleton-loading 1.4s ease-in-out infinite;
  }

  &__quantity {
    width: 25px;
    height: 16px;
    border-radius: 4px;
    flex-shrink: 0;
    background: linear-gradient(90deg, #f2f2f2 25%, rgba(255, 255, 255, 0.5) 37%, #f2f2f2 63%);
    background-size: 400% 100%;
    animation: skeleton-loading 1.4s ease-in-out infinite;
  }

  &__tags {
    display: flex;
    gap: 5px;
    margin-bottom: 5px;
  }

  &__spec-tag {
    width: 40px;
    height: 17px;
    border-radius: 2px;
    background: linear-gradient(90deg, #f2f2f2 25%, rgba(255, 255, 255, 0.5) 37%, #f2f2f2 63%);
    background-size: 400% 100%;
    animation: skeleton-loading 1.4s ease-in-out infinite;
  }

  &__price {
    width: 60px;
    height: 18px;
    border-radius: 4px;
    background: linear-gradient(90deg, #f2f2f2 25%, rgba(255, 255, 255, 0.5) 37%, #f2f2f2 63%);
    background-size: 400% 100%;
    animation: skeleton-loading 1.4s ease-in-out infinite;
  }
}

@keyframes skeleton-loading {
  0% {
    background-position: 100% 50%;
  }

  100% {
    background-position: 0% 50%;
  }
}
</style>
