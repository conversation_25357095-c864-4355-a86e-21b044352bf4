<!--
/**
 * 购物车底部操作栏组件
 *
 * 主要功能：
 * 1. 提供全选/取消全选功能，支持一键选择所有商品
 * 2. 显示已选商品数量和总价信息，实时更新购物车统计
 * 3. 支持编辑模式和正常模式两种状态切换
 * 4. 编辑模式下提供批量删除功能
 * 5. 正常模式下提供结算功能，跳转到订单确认页
 * 6. 根据不同业务类型显示不同的按钮文本
 *
 * 技术特点：
 * - 使用条件渲染实现两种模式的界面切换
 * - 集成价格显示组件，统一价格展示样式
 * - 支持按钮禁用状态，防止无效操作
 * - 采用响应式设计，适配不同屏幕尺寸
 *
 * 使用场景：
 * - 购物车页面底部固定操作区域
 * - 需要提供商品选择和结算功能的场景
 */
-->

<template>
  <!-- 购物车底部操作栏容器 -->
  <footer class="cart-footer">
    <!-- ===================== 编辑模式界面 ===================== -->
    <!-- 编辑模式下显示全选、已选数量和删除按钮 -->
    <template v-if="isEditMode">
      <!-- 编辑模式全选按钮 -->
      <button class="cart-footer__select-all" @click="$emit('edit-toggle-all')" type="button">
        <!-- 全选状态图标，根据选中状态切换图片 -->
        <img
          :src="isEditAllSelected ? woSelectImg : noSelectImg"
          alt="全选"
          class="cart-footer__checkbox-icon"
          loading="eager"
          decoding="async"
        />
        <span class="cart-footer__select-text">全选</span>
      </button>

      <!-- 编辑模式信息显示区域，显示已选商品数量 -->
      <div class="cart-footer__edit-info">
        已选{{ editSelectedCount }}件
      </div>

      <!-- 批量删除按钮，无选中商品时禁用 -->
      <WoButton
        type="gradient"
        size="medium"
        class="cart-footer__action-btn"
        :disabled="editSelectedCount === 0"
        @click="$emit('edit-delete')"
      >
        删除
      </WoButton>
    </template>

    <!-- ===================== 正常模式界面 ===================== -->
    <!-- 正常模式下显示全选、价格信息和结算按钮 -->
    <template v-else>
      <!-- 正常模式全选按钮 -->
      <button class="cart-footer__select-all" @click="$emit('toggle-all-select')" type="button">
        <!-- 全选状态图标，根据选中状态切换图片 -->
        <img
          :src="isAllSelected ? woSelectImg : noSelectImg"
          alt="全选"
          class="cart-footer__checkbox-icon"
          loading="eager"
          decoding="async"
        />
        <span class="cart-footer__select-text">全选</span>
      </button>

      <!-- 价格信息显示区域，包含已选数量和总价 -->
      <div class="cart-footer__price-info">
        已选{{ selectedCount }}件
        <!-- 非政企业务显示价格信息 -->
        <template v-if="!isZqBiz">，合计
          <PriceDisplay :price="totalPrice" size="medium" color="orange" />
        </template>
      </div>

      <!-- 结算按钮，无选中商品时禁用，根据业务类型显示不同文本 -->
      <WoButton
        type="gradient"
        size="medium"
        class="cart-footer__action-btn"
        :disabled="!hasSelectedGoods"
        @click="$emit('checkout')"
      >
        {{ isZqBiz ? '提交' : '结算' }}
      </WoButton>
    </template>
  </footer>
</template>

<script setup>
import { toRefs, computed } from 'vue'
import WoButton from '@components/WoElementCom/WoButton/WoButton.vue'
import PriceDisplay from '@components/Common/PriceDisplay.vue'
import noSelectImg from '@/static/images/no-select.png'
import woSelectImg from '@/static/images/wo-select.png'
import { getBizCode } from '@utils/curEnv.js'

// ==================== 组件属性定义 ====================
// 定义组件接收的props参数
const props = defineProps({
  // 编辑模式状态，控制界面显示模式
  isEditMode: {
    type: Boolean,
    default: false
  },
  // 编辑模式下选中的商品数量
  editSelectedCount: {
    type: Number,
    default: 0
  },
  // 编辑模式下是否全选状态
  isEditAllSelected: {
    type: Boolean,
    default: false
  },
  // 正常模式下选中的商品数量
  selectedCount: {
    type: Number,
    default: 0
  },
  // 选中商品的总价格
  totalPrice: {
    type: [String, Number],
    default: '0'
  },
  // 正常模式下是否全选状态
  isAllSelected: {
    type: Boolean,
    default: false
  },
  // 是否有选中的商品，用于控制结算按钮状态
  hasSelectedGoods: {
    type: Boolean,
    default: false
  }
})

// 使用toRefs解构props，保持响应性
const {
  isEditMode,
  editSelectedCount,
  isEditAllSelected,
  selectedCount,
  totalPrice,
  isAllSelected,
  hasSelectedGoods
} = toRefs(props)

// ==================== 事件定义 ====================
// 定义组件向父组件发射的事件
defineEmits(['toggle-all-select', 'edit-toggle-all', 'edit-delete', 'checkout'])

// ==================== 业务逻辑计算 ====================
// 判断是否为政企业务，用于控制价格显示和按钮文本
const isZqBiz = computed(() => getBizCode() === 'zq')
</script>

<style scoped lang="less">
.cart-footer {
  display: flex;
  align-items: center;
  width: 100%;
  gap: 10px;

  &__select-all {
    display: flex;
    align-items: center;
    flex-shrink: 0;
    background: none;
    border: none;
    padding: 0;
    cursor: pointer;
    touch-action: manipulation;
    transition: opacity 0.2s ease;

    &:active {
      opacity: 0.7;
    }
  }

  &__checkbox-icon {
    width: 18px;
    height: 18px;
    margin-right: 5px;
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
  }

  &__select-text {
    font-size: 13px;
    color: #171E24;
  }

  &__price-info {
    flex: 1;
    font-size: 14px;
    color: #171E24;
    display: flex;
    align-items: center;
    flex-wrap: wrap;
    gap: 4px;

    .price {
      font-size: 16px;
      font-weight: 600;
      color: var(--wo-biz-theme-color);
    }
  }

  &__edit-info {
    flex: 1;
    font-size: 14px;
    color: #171E24;
    display: flex;
    align-items: center;
  }

  &__action-btn {
    flex-shrink: 0;
  }
}
</style>
