<!--
/**
 * 购物车头部组件
 *
 * 主要功能：
 * 1. 显示当前选择的收货地址信息，支持地址文本的省略显示
 * 2. 提供地址选择功能，用户点击可打开地址选择弹窗
 * 3. 提供编辑模式切换功能，支持购物车商品的批量编辑操作
 * 4. 采用响应式设计，适配不同屏幕尺寸
 *
 * 技术特点：
 * - 使用语义化的header标签提升可访问性
 * - 采用BEM命名规范，提高样式可维护性
 * - 支持图片预加载和优化渲染
 * - 实现按钮点击反馈效果
 *
 * 使用场景：
 * - 购物车页面顶部区域
 * - 需要显示收货地址和编辑功能的场景
 */
-->

<template>
  <!-- 购物车头部容器 -->
  <header class="cart-header">
    <!-- 收货地址选择按钮区域 -->
    <!-- 点击触发地址选择弹窗，显示当前选中的收货地址 -->
    <button class="cart-header__address" @click="$emit('select-address')" type="button">
      <!-- 地址位置图标，使用预加载优化首屏性能 -->
      <img
        src="../../../../static/images/location.png"
        alt="地址标识"
        class="cart-header__location-icon"
        loading="eager"
        decoding="async"
        fetchpriority="high"
      />
      <!-- 地址文本显示，支持长文本省略 -->
      <span class="cart-header__address-text">{{ addressDisplay }}</span>
      <!-- 右箭头图标，提示用户可点击选择 -->
      <img
        src="../../../../static/images/arrow-right-black.png"
        alt="选择地址"
        class="cart-header__arrow-icon"
        loading="eager"
        decoding="async"
      />
    </button>

    <!-- 编辑模式切换按钮 -->
    <!-- 根据当前编辑状态显示"编辑"或"完成"文本 -->
    <button class="cart-header__edit" @click="$emit('toggle-edit')" type="button">
      {{ isEditMode ? '完成' : '编辑' }}
    </button>
  </header>
</template>

<script setup>
import { toRefs } from 'vue'

// ==================== 组件属性定义 ====================
// 定义组件接收的props参数
const props = defineProps({
  // 地址显示文本，用于显示当前选中的收货地址
  addressDisplay: {
    type: String,
    required: true
  },
  // 编辑模式状态，控制编辑按钮的文本显示
  isEditMode: {
    type: Boolean,
    default: false
  }
})

// 使用toRefs解构props，保持响应性
const { addressDisplay, isEditMode } = toRefs(props)

// ==================== 事件定义 ====================
// 定义组件向父组件发射的事件
defineEmits(['select-address', 'toggle-edit'])
</script>

<style scoped lang="less">
.cart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
  gap: 15px;

  &__address {
    display: flex;
    align-items: center;
    flex: 1;
    overflow: hidden;
    background: none;
    border: none;
    padding: 0;
    cursor: pointer;
    touch-action: manipulation;
  }

  &__location-icon {
    margin-right: 5px;
    width: 11px;
    height: 12px;
    flex-shrink: 0;
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
  }

  &__address-text {
    font-size: 12px;
    color: #171E24;
    font-weight: 400;
    text-align: left;
    overflow: hidden; text-overflow: ellipsis; white-space: nowrap;;
  }

  &__arrow-icon {
    margin-left: 5px;
    width: 5px;
    height: 9px;
    flex-shrink: 0;
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
  }

  &__edit {
    flex-shrink: 0;
    font-size: 12px;
    font-weight: 600;
    color: var(--wo-biz-theme-color);
    background: none;
    border: none;
    padding: 0;
    cursor: pointer;
    touch-action: manipulation;
    transition: opacity 0.2s ease;

    &:active {
      opacity: 0.7;
    }
  }
}
</style>
