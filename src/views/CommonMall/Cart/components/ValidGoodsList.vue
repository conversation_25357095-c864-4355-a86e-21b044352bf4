<!--
/**
 * 有效商品列表组件
 *
 * 主要功能：
 * 1. 展示购物车中所有有效可购买的商品列表
 * 2. 支持商品分组显示，政企商城支持分组头部和分组选择功能
 * 3. 集成商品项组件，提供完整的商品操作功能
 * 4. 支持编辑模式，提供批量选择和操作功能
 * 5. 实现事件透传机制，将子组件事件传递给父组件
 * 6. 支持性能优化，使用content-visibility优化长列表渲染
 *
 * 技术特点：
 * - 使用v-for渲染商品分组和商品项
 * - 采用事件透传模式，简化组件间通信
 * - 集成业务逻辑判断，支持不同业务场景
 * - 使用CSS contain属性优化渲染性能
 * - 支持条件渲染，根据业务类型显示不同界面
 *
 * 使用场景：
 * - 购物车页面的有效商品展示
 * - 需要支持商品分组和批量操作的场景
 * - 商品列表的统一管理和展示
 */
-->

<template>
  <!-- 有效商品分组容器，根据业务类型应用不同样式 -->
  <div
    class="cart-goods__valid"
    :class="{ 'cart-goods__valid--group': isZQBiz }"
    v-for="(group, groupIndex) in validGoodsList"
    :key="groupIndex"
  >
    <!-- ===================== 商品分组头部 ===================== -->
    <!-- 仅政企商城显示分组头部，支持分组选择功能 -->
    <CartGroupHeader
      v-if="isZQBiz"
      :group="group"
      :is-edit-mode="isEditMode"
      :temp-selected-items="tempSelectedItems"
      @toggle-select="handleToggleGroupSelect"
    />

    <!-- ===================== 商品项列表 ===================== -->
    <!-- 遍历分组内的商品列表，渲染每个商品项 -->
    <ValidGoodsItem
      v-for="(item, itemIndex) in group.goodsList"
      :key="item.cartSkuId"
      :item="item"
      :ref-key="`goodsItem_${groupIndex}_${itemIndex}`"
      :item-index="itemIndex"
      :is-edit-mode="isEditMode"
      :is-edit-selected="isEditModeItemSelected(item)"
      @toggle-select="handleToggleItemSelect"
      @show-stepper="showStepper"
      @quantity-change="handleQuantityChange"
      @look-similar="handleLookSimilar"
      @delete-item="handleDeleteItem"
      @close-menu="closeLongPressMenu"
      @swipe-open="handleSwipeOpen"
      @swipe-close="handleSwipeClose"
      @set-ref="setGoodsItemRef"
      @long-press="handleLongPress"
      @gift-click="handleGiftClick"
      @content-click="handleContentClick"
      :style="{ contentVisibility: itemIndex > 5 ? 'auto' : 'visible' }"
    />
  </div>
</template>

<script setup>
import { computed, toRefs } from 'vue'
import CartGroupHeader from './CartGroupHeader.vue'
import ValidGoodsItem from './ValidGoodsItem.vue'
import { getBizCode } from '@utils/curEnv.js'

// ==================== 组件属性定义 ====================
// 定义组件接收的props参数
const props = defineProps({
  // 有效商品列表数据，包含分组信息
  validGoodsList: {
    type: Array,
    required: true
  },
  // 编辑模式状态，控制商品项的编辑功能显示
  isEditMode: {
    type: Boolean,
    default: false
  },
  // 编辑模式下临时选中的商品ID集合
  tempSelectedItems: {
    type: Set,
    required: true
  }
})

// 使用toRefs解构props，保持响应性
const { validGoodsList, isEditMode, tempSelectedItems } = toRefs(props)

// ==================== 事件定义 ====================
// 定义组件向父组件发射的事件，采用透传模式
const emit = defineEmits([
  'toggle-group-select',    // 分组选择切换
  'toggle-item-select',     // 商品选择切换
  'show-stepper',          // 显示数量步进器
  'quantity-change',       // 商品数量变更
  'look-similar',          // 查看相似商品
  'delete-item',           // 删除商品
  'close-menu',            // 关闭菜单
  'swipe-open',            // 滑动菜单打开
  'swipe-close',           // 滑动菜单关闭
  'set-ref',               // 设置组件引用
  'long-press',            // 长按操作
  'gift-click',            // 赠品点击
  'content-click'          // 内容点击
])

// ==================== 业务逻辑计算 ====================
// 判断是否为政企商城，用于控制分组功能显示
const isZQBiz = computed(() => getBizCode() === 'zq')

// ==================== 事件处理函数 ====================
// 分组选择切换处理
const handleToggleGroupSelect = (group) => {
  emit('toggle-group-select', group)
}

// 商品选择切换处理
const handleToggleItemSelect = (item) => {
  emit('toggle-item-select', item)
}

// 显示数量步进器处理
const showStepper = (item) => {
  emit('show-stepper', item)
}

// 商品数量变更处理
const handleQuantityChange = (item) => {
  emit('quantity-change', item)
}

// 查看相似商品处理
const handleLookSimilar = (item) => {
  emit('look-similar', item)
}

// 删除商品处理
const handleDeleteItem = (item) => {
  emit('delete-item', item)
}

// 关闭长按菜单处理
const closeLongPressMenu = () => {
  emit('close-menu')
}

// 滑动菜单打开处理
const handleSwipeOpen = (item) => {
  emit('swipe-open', item)
}

// 滑动菜单关闭处理
const handleSwipeClose = (item) => {
  emit('swipe-close', item)
}

// 设置商品组件引用处理
const setGoodsItemRef = (el, key) => {
  emit('set-ref', el, key)
}

// 长按操作处理
const handleLongPress = (item) => {
  emit('long-press', item)
}

// 赠品点击处理
const handleGiftClick = (data) => {
  emit('gift-click', data)
}

// 内容点击处理
const handleContentClick = (data) => {
  emit('content-click', data)
}

// ==================== 辅助函数 ====================
// 判断编辑模式下商品是否被选中
const isEditModeItemSelected = (item) => {
  if (!isEditMode.value) return false
  const itemId = `${item.cartGoodsId}_${item.cartSkuId}`
  return tempSelectedItems.value.has(itemId)
}
</script>

<style scoped lang="less">
.cart-goods__valid {
  margin-bottom: 10px;
  contain: layout style;
}

// 政企商城分组卡片化样式
.cart-goods__valid--group {
  background: #FFFFFF;
  border-radius: 4px;
  padding: 8px;
  margin-bottom: 12px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);
  border: 1px solid #f0f0f0;
}
</style>
