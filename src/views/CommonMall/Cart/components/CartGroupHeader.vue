<!--
/**
 * 购物车商品分组头部组件
 *
 * 主要功能：
 * 1. 展示商品分组信息，包括分组名称和选择状态
 * 2. 提供分组级别的全选/取消全选功能
 * 3. 显示分组内商品的选择统计信息
 * 4. 支持编辑模式和正常模式的不同选择逻辑
 * 5. 提供半选状态的视觉反馈，清晰显示选择状态
 * 6. 支持无障碍访问，提供适当的ARIA标签
 *
 * 技术特点：
 * - 使用计算属性实现选择状态的实时计算
 * - 支持不同业务场景的图标切换
 * - 采用语义化的HTML结构，提升可访问性
 * - 实现半选状态的CSS伪元素效果
 * - 支持点击反馈和用户交互优化
 *
 * 使用场景：
 * - 政企商城的商品分组展示
 * - 需要分组级别操作的购物车场景
 * - 批量选择和管理商品的场景
 */
-->

<template>
  <!-- 商品分组头部容器 -->
  <header class="cart-group-header">
    <!-- 分组选择复选框按钮 -->
    <!-- 支持半选状态显示，提供无障碍访问标签 -->
    <button
      class="group-checkbox"
      type="button"
      :class="{ 'group-checkbox--indeterminate': isIndeterminate }"
      :aria-label="isIndeterminate ? '分组部分选择，点击切换' : '分组选择，点击切换'"
      @click="handleToggleSelect"
    >
      <!-- 复选框图标，根据选择状态和业务类型切换 -->
      <img :src="checkboxSrc" alt="分组选择" class="group-checkbox__icon" />
    </button>

    <!-- 分组标题，支持点击切换选择状态 -->
    <h3 class="cart-group-header__title clickable" @click="handleToggleSelect">
      {{ group.groupName }}
    </h3>

    <!-- 分组选择统计信息，显示已选数量和总数量 -->
    <span class="cart-group-header__meta" aria-hidden="true">
      已选{{ selectedCount }}/{{ totalCount }}
    </span>
  </header>
</template>

<script setup>
import { computed, toRefs } from 'vue'
import noSelectImg from '@/static/images/no-select.png'
import woSelectImg from '@/static/images/wo-select.png'
import jdSelectImg from '@/static/images/jd-select.png'
import { getBizCode } from '@utils/curEnv.js'

// ==================== 组件属性定义 ====================
// 定义组件接收的props参数
const props = defineProps({
  // 商品分组数据对象，包含分组名称和商品列表
  group: {
    type: Object,
    required: true
  },
  // 编辑模式状态，影响选择逻辑的计算方式
  isEditMode: {
    type: Boolean,
    default: false
  },
  // 编辑模式下临时选中的商品ID集合
  tempSelectedItems: {
    type: Set,
    default: () => new Set()
  }
})

// 使用toRefs解构props，保持响应性
const { group, isEditMode, tempSelectedItems } = toRefs(props)

// ==================== 事件定义 ====================
// 定义组件向父组件发射的事件
const emit = defineEmits(['toggle-select'])

// ==================== 选择状态计算 ====================
// 计算分组内商品总数量
const totalCount = computed(() => group.value.goodsList?.length || 0)

// 计算分组内已选商品数量，根据编辑模式使用不同的计算逻辑
const selectedCount = computed(() => {
  if (!group.value || !Array.isArray(group.value.goodsList)) return 0

  return isEditMode.value
    ? // 编辑模式：从临时选中集合中计算
      group.value.goodsList.reduce((sum, item) =>
        sum + (tempSelectedItems.value.has(`${item.cartGoodsId}_${item.cartSkuId}`) ? 1 : 0), 0)
    : // 正常模式：从商品的selected属性计算
      group.value.goodsList.reduce((sum, item) =>
        sum + (item.selected === 'true' ? 1 : 0), 0)
})

// 计算是否为半选状态，当部分商品被选中时显示半选效果
const isIndeterminate = computed(() => {
  const total = totalCount.value
  const selected = selectedCount.value
  return total > 0 && selected > 0 && selected < total
})

// ==================== 图标状态计算 ====================
// 根据业务类型和选择状态计算复选框图标
const checkboxSrc = computed(() => {
  // 判断是否为有购京东业务，使用不同的选中图标
  const isYgjd = getBizCode() === 'ygjd'
  const checkedImg = isYgjd ? jdSelectImg : woSelectImg

  const total = totalCount.value
  const selected = selectedCount.value

  // 全选状态显示选中图标，否则显示未选中图标
  return total > 0 && selected === total ? checkedImg : noSelectImg
})

// ==================== 事件处理函数 ====================
// 处理分组选择切换操作
const handleToggleSelect = () => {
  emit('toggle-select', group.value)
}
</script>

<style scoped lang="less">
.cart-group-header {
  display: flex;
  align-items: center;
  gap: 6px;
  margin: 6px 0 8px;
  padding: 0 0 6px 15px;
  border-bottom: 1px dashed #ededed;

  &__title {
    font-size: 14px;
    font-weight: 600;
    color: #171E24;
    margin: 0;

    &.clickable {
      cursor: pointer;
      user-select: none;
      &:active {
        opacity: 0.7;
      }
    }
  }

  &__meta {
    margin-left: auto;
    font-size: 12px;
    color: #718096;
  }
}

.group-checkbox {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 18px;
  height: 18px;
  background: none;
  border: none;
  padding: 0;
  cursor: pointer;
  position: relative;

  &__icon {
    width: 18px;
    height: 18px;
    display: block;
  }

  &--indeterminate::after {
    content: '';
    position: absolute;
    left: 3px;
    right: 3px;
    top: 8px;
    height: 2px;
    background-color: var(--wo-biz-theme-color);
    border-radius: 2px;
    pointer-events: none;
  }
}
</style>
