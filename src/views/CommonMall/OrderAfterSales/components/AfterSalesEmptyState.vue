<!--
/**
 * 售后订单空状态组件
 *
 * 主要功能：
 * 1. 展示售后订单列表为空时的友好提示界面
 * 2. 提供统一的空状态视觉设计和用户体验
 * 3. 支持响应式布局，适配不同屏幕尺寸
 * 4. 实现简洁明了的空状态信息展示
 *
 * 技术特点：
 * - 采用纯展示组件设计，无业务逻辑
 * - 使用Flexbox布局实现居中对齐
 * - 支持BEM命名规范的CSS类名
 * - 实现轻量级的空状态展示
 *
 * 使用场景：
 * - 用户暂无售后订单时的空状态展示
 * - 售后订单列表加载完成但无数据时显示
 * - 提供友好的用户体验和视觉反馈
 */
-->

<template>
  <!-- 售后订单空状态主容器 -->
  <!-- 使用Flexbox布局实现内容居中显示 -->
  <div class="after-sales-empty">
    <!-- 空状态内容区域 -->
    <div class="after-sales-empty__content">
      <!-- 空状态提示文本 -->
      <div class="after-sales-empty__text">暂无售后订单</div>
    </div>
  </div>
</template>

<script setup>
// 纯展示组件，无需任何逻辑处理
</script>

<style scoped lang="less">
.after-sales-empty {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 300px;

  &__content {
    text-align: center;
  }

  &__text {
    font-size: 14px;
    color: #4A5568;
    margin: 0;
  }
}
</style>
