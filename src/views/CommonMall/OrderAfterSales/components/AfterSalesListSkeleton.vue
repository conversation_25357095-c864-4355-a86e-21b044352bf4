<!--
/**
 * 售后订单列表骨架屏组件
 *
 * 主要功能：
 * 1. 提供售后订单列表加载时的骨架屏效果，优化用户体验
 * 2. 支持自定义骨架屏数量，适应不同的加载场景
 * 3. 模拟真实订单项的布局结构，包括头部、商品信息和操作按钮
 * 4. 集成脉冲动画效果，提供生动的加载视觉反馈
 * 5. 支持无障碍访问，包含适当的ARIA标签
 * 6. 实现响应式设计，适配不同屏幕尺寸
 *
 * 技术特点：
 * - 使用CSS动画实现脉冲效果
 * - 采用BEM命名规范的CSS类名
 * - 集成无障碍访问支持
 * - 支持组件属性配置
 * - 实现高性能的动画渲染
 *
 * 使用场景：
 * - 售后订单列表数据加载中的占位显示
 * - 网络请求等待期间的用户体验优化
 * - 页面初始化加载时的视觉反馈
 */
-->

<template>
  <!-- 售后订单骨架屏主容器 -->
  <!-- 添加无障碍访问支持，标识为加载状态 -->
  <div class="after-sales-skeleton" role="status" aria-label="加载中">
    <!-- 骨架屏项目循环渲染 -->
    <!-- 根据count属性生成指定数量的骨架屏项目 -->
    <article
      v-for="i in count"
      :key="`skeleton-${i}`"
      class="after-sales-skeleton__item"
    >
      <!-- 使用WoCard组件包装，保持与真实订单项一致的外观 -->
      <WoCard>
        <!-- 骨架屏内容区域 -->
        <div class="after-sales-skeleton__content">
          <!-- 骨架屏头部区域 -->
          <!-- 模拟订单号和状态的布局 -->
          <header class="after-sales-skeleton__header">
            <!-- 订单号占位块 -->
            <div class="after-sales-skeleton__order-number" aria-hidden="true"></div>
            <!-- 状态占位块 -->
            <div class="after-sales-skeleton__status" aria-hidden="true"></div>
          </header>

          <!-- 骨架屏商品信息区域 -->
          <!-- 模拟商品图片和详细信息的布局 -->
          <section class="after-sales-skeleton__goods">
            <!-- 商品图片占位块 -->
            <div class="after-sales-skeleton__image" aria-hidden="true"></div>
            <!-- 商品信息占位区域 -->
            <div class="after-sales-skeleton__info">
              <!-- 商品标题占位块 -->
              <div class="after-sales-skeleton__title" aria-hidden="true"></div>
              <!-- 商品副标题占位块 -->
              <div class="after-sales-skeleton__subtitle" aria-hidden="true"></div>
              <!-- 商品价格占位块 -->
              <div class="after-sales-skeleton__price" aria-hidden="true"></div>
            </div>
          </section>

          <!-- 骨架屏操作按钮区域 -->
          <!-- 模拟操作按钮的布局 -->
          <footer class="after-sales-skeleton__actions">
            <!-- 操作按钮占位块 -->
            <div class="after-sales-skeleton__button" aria-hidden="true"></div>
            <div class="after-sales-skeleton__button" aria-hidden="true"></div>
          </footer>
        </div>
      </WoCard>
    </article>
  </div>
</template>

<script setup>
import WoCard from '@components/WoElementCom/WoCard.vue'

// ==================== 组件属性定义 ====================
// 定义组件接收的props参数
defineProps({
  // 骨架屏显示数量，默认显示3个
  count: {
    type: Number,
    default: 3
  }
})
</script>

<style scoped lang="less">
.after-sales-skeleton {
  &__item {
    margin-bottom: 10px;
  }

  &__content {
    padding: 0;
  }

  &__header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 11px;
  }

  &__order-number {
    width: 120px;
    height: 12px;
    background: #f0f0f0;
    border-radius: 6px;
    animation: skeleton-pulse 1.5s ease-in-out infinite;
    will-change: opacity;
  }

  &__status {
    width: 60px;
    height: 16px;
    background: #f0f0f0;
    border-radius: 8px;
    animation: skeleton-pulse 1.5s ease-in-out infinite;
    will-change: opacity;
  }

  &__goods {
    display: flex;
    align-items: flex-start;
    margin-bottom: 15px;
  }

  &__image {
    width: 75px;
    height: 75px;
    background: #f0f0f0;
    border-radius: 8px;
    margin-right: 12px;
    flex-shrink: 0;
    animation: skeleton-pulse 1.5s ease-in-out infinite;
    will-change: opacity;
  }

  &__info {
    flex: 1;
    min-height: 75px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
  }

  &__title {
    width: 80%;
    height: 16px;
    background: #f0f0f0;
    border-radius: 8px;
    margin-bottom: 8px;
    animation: skeleton-pulse 1.5s ease-in-out infinite;
    will-change: opacity;
  }

  &__subtitle {
    width: 60%;
    height: 12px;
    background: #f0f0f0;
    border-radius: 6px;
    margin-bottom: 8px;
    animation: skeleton-pulse 1.5s ease-in-out infinite;
    will-change: opacity;
  }

  &__price {
    width: 40%;
    height: 14px;
    background: #f0f0f0;
    border-radius: 6px;
    animation: skeleton-pulse 1.5s ease-in-out infinite;
    will-change: opacity;
  }

  &__actions {
    display: flex;
    justify-content: flex-end;
    gap: 8px;
  }

  &__button {
    width: 60px;
    height: 28px;
    background: #f0f0f0;
    border-radius: 4px15;
    animation: skeleton-pulse 1.5s ease-in-out infinite;
    will-change: opacity;
  }
}

@keyframes skeleton-pulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.6;
  }
  100% {
    opacity: 1;
  }
}
</style>
