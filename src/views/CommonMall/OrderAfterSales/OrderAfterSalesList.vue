<!--
/**
 * 订单售后列表页面组件
 *
 * 主要功能：
 * 1. 展示用户的售后订单列表，支持分页加载和下拉刷新功能
 * 2. 提供骨架屏加载状态，优化用户体验
 * 3. 支持空状态展示，当无售后订单时显示友好提示
 * 4. 集成售后操作按钮，支持申请退款、申请售后、查看详情等操作
 * 5. 实现滚动位置记忆功能，支持页面返回时恢复滚动位置
 * 6. 处理售后申请时效过期提醒，引导用户联系客服
 *
 * 技术特点：
 * - 使用van-list组件实现无限滚动加载
 * - 采用van-pull-refresh组件支持下拉刷新
 * - 集成useOrderAfterSalesActions组合式函数处理售后业务逻辑
 * - 支持响应式数据管理和状态同步
 * - 实现防抖滚动监听，优化性能
 *
 * 使用场景：
 * - 用户查看已申请的售后订单列表
 * - 用户对订单进行售后相关操作
 * - 售后订单状态跟踪和管理
 */
-->

<template>
  <!-- 售后订单列表主容器 -->
  <!-- 绑定ref用于滚动位置控制和事件监听 -->
  <div class="after-sales-list" ref="contentRef">
    <!-- 骨架屏加载状态 -->
    <!-- 当正在加载且订单列表为空且无错误时显示 -->
    <AfterSalesListSkeleton v-if="showSkeleton" :count="3" />

    <!-- 空状态展示 -->
    <!-- 当加载完成且订单列表为空且已完成加载时显示 -->
    <AfterSalesEmptyState v-else-if="!loading && isEmptyState && finished" />

    <!-- 下拉刷新容器 -->
    <!-- 包含无限滚动列表，支持下拉刷新功能 -->
    <van-pull-refresh v-else v-model="refreshing" @refresh="onRefresh">
      <!-- 无限滚动列表 -->
      <!-- 自动加载更多数据，显示加载状态和完成状态提示 -->
      <van-list
        v-model:loading="loading"
        :finished="finished"
        loading-text="加载中..."
        finished-text="没有更多了"
        @load="onLoad"
        :immediate-check="false"
      >
        <!-- 售后订单项组件 -->
        <!-- 遍历订单列表，为每个订单生成对应的操作按钮 -->
        <AfterSalesOrderItem
          v-for="order in orderList"
          :key="order.id"
          :order-data="order"
          :action-buttons="generateActionButtons(order, actionOptions)"
        />
      </van-list>
    </van-pull-refresh>

    <!-- 售后申请时效过期提醒弹窗 -->
    <!-- 当用户尝试对过期订单进行售后操作时显示 -->
    <ExpirationPopup
      v-model:visible="expirationPopupVisible"
      title=""
      main-text="抱歉，订单已过售后申请时效"
      sub-text="商品已超过售后期限，如需售后可联系客服处理"
      confirm-text="确定"
      @close="expirationPopupVisible = false"
      @confirm="expirationPopupVisible = false"
    />
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, nextTick, toRefs } from 'vue'
import { isEmpty, debounce } from 'lodash-es'
import { closeToast, showLoadingToast } from 'vant'
import { getAfterSalesInfo } from '@api/interface/order.js'
import { getBizCode } from '@utils/curEnv.js'
import ExpirationPopup from '@components/Common/ExpirationPopup/ExpirationPopup.vue'
import { useOrderAfterSalesActions } from '@/composables/useOrderAfterSalesActions.js'
import AfterSalesListSkeleton from './components/AfterSalesListSkeleton.vue'
import AfterSalesEmptyState from './components/AfterSalesEmptyState.vue'
import AfterSalesOrderItem from './components/AfterSalesOrderItem.vue'

// ==================== 组件属性定义 ====================
// 定义组件接收的props参数
const props = defineProps({
  // 滚动位置，用于页面返回时恢复滚动状态
  scrollPosition: {
    type: Number,
    default: 0
  }
})

// 使用toRefs解构props，保持响应性
const { scrollPosition } = toRefs(props)

// 定义组件向父组件发射的事件
const emit = defineEmits(['update-scroll'])

// ==================== 售后操作功能集成 ====================
// 集成售后操作相关功能，包括过期弹窗和按钮生成
const {
  expirationPopupVisible,
  generateActionButtons
} = useOrderAfterSalesActions()

// ==================== 列表状态管理 ====================
// DOM引用，用于滚动监听和位置控制
const contentRef = ref(null)

// 加载状态控制
const loading = ref(false)
const finished = ref(false)

// 订单列表数据
const orderList = ref([])

// 分页参数管理
const currentPage = ref(1)
const pageSize = ref(10)
const totalPage = ref(0)

// 错误状态和刷新状态
const error = ref(false)
const isRefreshing = ref(false)
const refreshing = ref(false)

// ==================== 计算属性和状态判断 ====================
// 售后操作按钮配置选项
// 配置不显示加购物车按钮，不使用子订单数据
const actionOptions = computed(() => ({
  showAddToCart: false,
  useSubOrderData: false
}))

// 骨架屏显示条件判断
// 当正在加载且订单列表为空且无错误时显示骨架屏
const showSkeleton = computed(() => {
  return loading.value && isEmpty(orderList.value) && !error.value
})

// 空状态显示条件判断
// 当订单列表为空时显示空状态组件
const isEmptyState = computed(() => {
  return isEmpty(orderList.value)
})

// ==================== 滚动位置管理 ====================
// 防抖处理的滚动事件监听器
// 监听滚动位置变化，向父组件发射滚动位置更新事件
const handleScroll = debounce(() => {
  if (contentRef.value) {
    // 获取当前滚动位置，兼容不同的滚动容器
    const scrollTop = contentRef.value.scrollTop || document.documentElement.scrollTop || document.body.scrollTop
    // 向父组件发射滚动位置更新事件
    emit('update-scroll', scrollTop)
  }
}, 16)

// ==================== 数据加载处理 ====================
// 加载售后订单列表数据的异步函数
// 支持分页加载，处理数据转换和状态更新
const onLoad = async () => {
  try {
    // 构建请求参数，包含业务代码、页码和页面大小
    const params = {
      bizCode: getBizCode('ORDER'),
      pageNum: currentPage.value,
      pageSize: pageSize.value
    }

    // 显示加载提示
    showLoadingToast()
    // 调用API获取售后订单信息
    const [err, json] = await getAfterSalesInfo(params)
    // 关闭加载提示
    closeToast()

    // 处理API请求错误
    if (err) {
      error.value = true
      loading.value = false
      finished.value = true
      return
    }

    // 更新页码，准备下次加载
    currentPage.value++
    loading.value = false

    // 检查是否有数据返回，无数据时标记加载完成
    if (!json?.afterSaleList?.length) {
      finished.value = true
      return
    }

    // 展开嵌套的订单数据结构，转换为平铺的订单列表
    // 每个订单项包含SKU信息、价格等必要字段
    const expandedOrders = json.afterSaleList.flatMap(order =>
      order.map(item => ({
        ...item,
        skuNumInfoList: item.skuNumInfoList,
        price: item.orderPrice,
        totalPrice: item.orderPrice
      }))
    )

    // 根据不同场景更新订单列表数据
    if (isRefreshing.value) {
      // 下拉刷新时，替换整个列表
      orderList.value = expandedOrders
      isRefreshing.value = false
    } else if (currentPage.value === 2) {
      // 首次加载时，设置初始列表
      orderList.value = expandedOrders
    } else {
      // 分页加载时，追加到现有列表
      orderList.value.push(...expandedOrders)
    }

    // 更新总页数，判断是否已加载完所有数据
    totalPage.value = json.totalPage
    if (currentPage.value > totalPage.value) {
      finished.value = true
    }

    // 恢复滚动位置（用于页面返回场景）
    if (currentPage.value === 2 && scrollPosition.value > 0) {
      nextTick(() => {
        if (contentRef.value) {
          contentRef.value.scrollTop = scrollPosition.value
        }
      })
    }
  } catch (err) {
    console.error('加载数据失败:', err)
    error.value = true
    loading.value = false
    finished.value = true
    closeToast()
  }
}
// ==================== 刷新功能处理 ====================
// 刷新数据的核心函数
// 重置所有状态并重新加载第一页数据
const refreshData = async () => {
  try {
    // 设置刷新状态标志
    isRefreshing.value = true
    // 清空现有订单列表
    orderList.value = []
    // 重置分页参数
    currentPage.value = 1
    finished.value = false
    error.value = false
    loading.value = true
    // 重新加载数据
    await onLoad()
  } catch (err) {
    console.error('刷新数据失败:', err)
    isRefreshing.value = false
    throw err
  }
}

// 下拉刷新事件处理函数
// 响应用户下拉刷新操作，确保刷新状态正确重置
const onRefresh = async () => {
  try {
    await refreshData()
  } catch (error) {
    console.error('下拉刷新失败:', error)
  } finally {
    // 无论成功失败都要重置刷新状态
    refreshing.value = false
  }
}

// ==================== 生命周期和事件监听 ====================
// 组件挂载时的初始化操作
onMounted(() => {
  // 添加滚动事件监听器，支持被动监听优化性能
  const target = contentRef.value || window
  target.addEventListener('scroll', handleScroll, { passive: true })

  // 开始加载初始数据
  loading.value = true
  onLoad()
})

// 组件卸载时的清理操作
onUnmounted(() => {
  // 移除滚动事件监听器，防止内存泄漏
  const target = contentRef.value || window
  target.removeEventListener('scroll', handleScroll)
})

// ==================== 组件暴露接口 ====================
// 向父组件暴露刷新数据的方法
defineExpose({
  refreshData
})

</script>

<style scoped lang="less">
.after-sales-list {
  min-height: 100vh;
  padding: 10px;
  background: #F8F9FA;
}

:deep(.van-pull-refresh) {
  height: 100vh;
  overflow: auto;
}
</style>
