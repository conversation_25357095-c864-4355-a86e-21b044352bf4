<!--
/**
 * 售后快递物流信息页面组件
 *
 * 主要功能：
 * 1. 展示售后订单的物流跟踪信息，包括订单号、承运商和运单号
 * 2. 提供物流跟踪详情查看，显示物流状态更新记录
 * 3. 支持运单号复制功能，方便用户在其他平台查询
 * 4. 处理不同的物流查询状态，提供相应的用户提示
 * 5. 集成第三方物流查询链接，提供备用查询方式
 * 6. 实现空状态和错误状态的友好展示
 *
 * 技术特点：
 * - 使用响应式数据管理物流信息状态
 * - 集成剪贴板功能支持运单号复制
 * - 采用条件渲染展示不同状态内容
 * - 支持物流信息的实时更新和轮询
 * - 实现友好的错误处理和用户提示
 *
 * 使用场景：
 * - 用户查看售后订单的物流配送状态
 * - 物流信息跟踪和状态更新
 * - 运单号查询和第三方平台跳转
 */
-->

<template>
  <!-- 快递物流信息主容器 -->
  <div class="express-page">
    <!-- 物流基本信息头部 -->
    <!-- 显示订单号、承运商和运单号等基础信息 -->
    <header>
      <!-- 订单号信息行 -->
      <div class="line">
        <p class="title">订单号：</p>
        <p class="content">{{ orderId }}</p>
      </div>

      <!-- 承运商信息行 -->
      <div class="line">
        <p class="title">承运商：</p>
        <p class="content">{{ expressName || '--' }}</p>
      </div>

      <!-- 运单号信息行 -->
      <div class="line">
        <p class="title">运单号：</p>
        <p class="content">
          <span>{{ expressNo || '--' }}</span>
          <!-- 运单号复制按钮（当运单号存在时显示） -->
          <i
            v-if="expressNo"
            class="copy-btn"
            v-clipboard:copy="expressNo"
            v-clipboard:success="onClipboardCopy"
            v-clipboard:error="onClipboardError">
            复制
          </i>
        </p>
      </div>
    </header>

    <!-- 物流跟踪详情主体区域 -->
    <!-- 状态1：有物流跟踪信息时显示详细记录 -->
    <main v-if="orderTrackStatus===1">
      <!-- 遍历显示物流跟踪记录 -->
      <p class="info" v-for="(item, index) in orderTrack" :key="index">
        <!-- 物流状态描述 -->
        <span class="content">{{ item.content || item.context }}</span>
        <!-- 物流状态更新时间 -->
        <span class="time">{{ item.msgTime || item.time }}</span>
      </p>
    </main>

    <!-- 状态2：物流信息查询失败时显示备用查询方式 -->
    <main v-if="orderTrackStatus===2" style="padding: 10px 20px">
      <!-- 备用查询提示信息 -->
      <p class="no-info" style="padding-bottom: 10px">
        您可通过复制物流单号，前往物流公司官网查询物流情况，也可快速访问"快递100"进行查询。快递100：
        <a href="https://www.kuaidi100.com">https://www.kuaidi100.com</a>
      </p>
      <!-- 查询建议提示 -->
      <p class="prompt">(建议前往官方网站查询，当查询失效时，可检查单号是否填写正确)</p>
    </main>

    <!-- 状态0：暂无物流信息时显示空状态 -->
    <main v-if="orderTrackStatus===0" style="padding-left: 17px">
      <!-- 空状态图标 -->
      <div class="empty"/>
      <!-- 空状态提示文本 -->
      <p class="prompt" style="text-align: center">暂无消息~</p>
    </main>
  </div>
</template>

<script setup>
import { ref, onBeforeUnmount, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import { useClipboard } from '@vueuse/core'
import { showToast, showLoadingToast, closeToast } from 'vant'
import { getAfterSalesExpress } from '@api/interface/order.js'

// ==================== 路由和工具集成 ====================
// 获取路由实例，用于获取查询参数
const route = useRoute()

// ==================== 响应式状态定义 ====================
// 订单ID，用于显示和查询
const orderId = ref('')

// 售后申请ID，用于API查询
const applySaleApplyId = ref('')

// 快递公司名称
const expressName = ref('')

// 快递运单号
const expressNo = ref('')

// 完整的快递信息对象
const orderExpress = ref({})

// 当前订单信息
const curOrder = ref({})

// 物流跟踪记录列表
const orderTrack = ref([])

// 物流跟踪状态
// 1: 有快递记录, 2: 有快递但无记录, 0: 无快递信息, -1: 数据读取中
const orderTrackStatus = ref(-1)

// 获取售后快递信息
const getExpress = async (applySaleApplyId) => {
  showLoadingToast()
  try {
    const [err, json] = await getAfterSalesExpress(applySaleApplyId)
    if (!err) return json
    return {}
  } finally {
    closeToast()
  }
}

// ==================== 剪贴板功能处理 ====================
// 获取剪贴板功能实例
const { copy } = useClipboard()

// 复制快递单号的异步函数
// 将运单号复制到剪贴板并显示操作结果提示
const copyExpressNo = async () => {
  try {
    // 执行复制操作
    await copy(expressNo.value)
    // 显示复制成功提示
    showToast('复制成功')
  } catch (e) {
    // 记录错误日志并显示失败提示
    console.error('[ORDER-EXPRESS] clipboard error', e)
    showToast('复制失败')
  }
}

// ==================== 生命周期钩子函数 ====================
// 组件挂载时的初始化操作
onMounted(async () => {
  // 从路由参数获取订单ID和售后申请ID
  orderId.value = route.query.orderId
  applySaleApplyId.value = route.query.applySaleApplyId

  // 获取快递配送信息
  const deliverInfo = await getExpress(applySaleApplyId.value)

  // 设置快递公司名称和运单号
  expressName.value = deliverInfo.expressName
  expressNo.value = deliverInfo.expressNo

  // 设置物流跟踪记录，确保为数组类型
  orderTrack.value = deliverInfo.orderTrack || []

  // 根据获取到的信息设置物流跟踪状态
  if (deliverInfo) {
    if (orderTrack.value && orderTrack.value.length > 0) {
      // 状态1：有快递记录
      orderTrackStatus.value = 1
    } else {
      // 状态2：有快递信息但暂无跟踪记录
      orderTrackStatus.value = 2
    }
  } else {
    // 状态0：无快递信息
    orderTrackStatus.value = 0
  }
})

// 组件卸载前的清理操作
onBeforeUnmount(() => {
  // 关闭可能存在的加载提示
  closeToast()
})
</script>

<style lang="less" scoped>
.express-page {
  width: 100%;
  min-height: 100vh;
  background: #FFFFFF;

  header {
    padding: 10px;
    width: 100%;
    margin-bottom: 10px;
    line-height: 15px;
    border-bottom: 9px solid #F8F9FA;
    font-size: 13px;
    color: #171E24;
    box-sizing: border-box;
    .line {
      display: flex;
      justify-content: flex-start;
      align-items: center;
      margin-bottom: 10px;
      line-height: 1.5;

      &:last-child {
        margin-bottom: 0;
      }
    }

    .title {
      color: #171E24;
    }

    .content {
      color: #171E24;
    }

    .copy-btn {
      display: inline-block;
      margin-left: 10px;
      width: 48px;
      height: 23px;
      line-height: 23px;
      border: 1px solid var(--wo-biz-theme-color);
      border-radius: 2px;
      font-size: 13px;
      font-style: normal;
      text-align: center;
      color: var(--wo-biz-theme-color);
    }
  }

  main {
    padding: 15px 15px 15px 56px;
    width: 100%;
    background: #FFFFFF;
    box-sizing: border-box;
    .info {
      position: relative;
      padding-bottom: 22px;
      font-size: 14px;
      line-height: 20px;
      text-align: left;
      color: #718096;

      &:before {
        content: '';
        position: absolute;
        z-index: 2;
        left: -32px;
        display: block;
        width: 8px;
        height: 8px;
        background-color: #CBD5E0;
        background-size: 100% 100%;
        border: 2px solid #FFFFFF;
        border-radius: 50%;
      }

      &:after {
        content: '';
        position: absolute;
        z-index: 1;
        left: -26px;
        top: 0;
        display: block;
        width: 1px;
        height: 100%;
        background: #E2E8EE;
      }

      span {
        display: block;

        &.time {
          color: #718096;
        }
      }
    }

    .info:first-child {
      color: #171E24;

      &:before {
        left: -34px;
        width: 12px;
        height: 12px;
        background-color: var(--wo-biz-theme-color);
        border: 3px solid #ffd6b5;
      }
    }

    .info:last-child {
      &:after {
        display: none;
      }
    }

    .no-info {
      position: relative;
      padding-bottom: 22px;
      font-size: 14px;
      line-height: 20px;
      text-align: left;
      color: #171E24;

      &:before {
        content: '';
        position: absolute;
        z-index: 2;
        left: -32px;
        display: block;
        width: 20px;
        height: 20px;
        background-size: 100% 100%;
      }

      a,
      a:visited,
      a:active {
        text-decoration: underline;
        color: #171E24;
      }
    }

    .prompt {
      line-height: 1.3;
      font-size: 13px;
      color: #718096;
    }

    .empty {
      margin: 31px auto;
      width: 208px;
      height: 161px;
      background: #FFFFFF url(./assets/empty.png) no-repeat 0 0;
      background-size: 100% 100%;
    }
  }
}
</style>
