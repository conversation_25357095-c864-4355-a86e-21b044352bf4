<!--
/**
 * 订单售后退款申请页面组件
 *
 * 主要功能：
 * 1. 提供完整的退款申请表单，包括货物状态、退款原因、退款件数和金额设置
 * 2. 支持退款金额的自动计算和手动编辑，根据货物状态动态调整可退金额
 * 3. 集成图片上传功能，支持上传退款凭证，包括拍照和相册选择功能
 * 4. 提供补充描述功能，用户可添加详细的退款说明和备注信息
 * 5. 实现表单验证和数据提交，确保退款申请信息完整有效
 * 6. 支持多种货物状态和退款原因的选择，适应不同退款场景需求
 * 7. 集成图片压缩和上传状态管理，优化用户体验和性能
 * 8. 支持环境检测和拍照功能，适配不同设备和浏览器
 *
 * 技术特点：
 * - 使用响应式表单数据管理和验证，实时更新表单状态
 * - 集成图片压缩和上传功能，支持多种图片格式和大小限制
 * - 采用弹窗选择器提供友好的选择体验，提升用户操作便利性
 * - 支持动态金额计算和编辑模式切换，灵活处理退款金额
 * - 实现文件上传状态管理和错误处理，保证上传过程稳定性
 * - 集成环境检测和设备适配，支持多平台兼容性
 *
 * 使用场景：
 * - 用户申请商品退款服务，填写退款申请表单
 * - 退款信息填写和凭证上传，提供退款依据
 * - 退款申请提交和状态跟踪，完成退款流程
 */
-->

<template>
  <!-- 退款申请主容器 -->
  <div class="refund-application">
    <!-- 基础信息填写区域 -->
    <!-- 包含货物状态、退款原因和退款件数的设置 -->
    <section class="refund-basic-info">
      <!-- 货物状态选择 -->
      <!-- 点击弹出货物状态选择弹窗 -->
      <WoCell left-title="货物状态" :right-title="refundData.goodStatus ? refundData.goodStatus.title : '请选择'"
        :is-center="true" :is-border="true" :is-require="true" :show-arrow="true"
        @right-click="goodsStatusPopupShow = true" />

      <!-- 退款原因选择 -->
      <!-- 点击弹出退款原因选择弹窗 -->
      <WoCell left-title="退款原因" :right-title="refundData.reasonRefund ? refundData.reasonRefund.title : '请选择'"
        :is-center="true" :is-border="true" :is-require="true" :show-arrow="true"
        @right-click="setReasonRefundPopupShow" />

      <!-- 退款件数设置 -->
      <!-- 使用步进器组件控制退款商品数量 -->
      <WoCell left-title="退款件数" :is-center="true" :is-border="false">
        <template #right>
          <div class="quantity-stepper">
            <!-- 数量步进器，支持加减操作和直接输入 -->
            <van-stepper @blur="blurRefundsNumber" @minus="minusRefundsNumber" @plus="plusRefundsNumber"
              @change="changeRefundsNumber" integer :disabled="isGoodsNumInput" :max="goodsNum"
              v-model="refundData.refundNum" />
          </div>
        </template>
      </WoCell>
    </section>

    <!-- 区域分隔线 -->
    <div class="section-divider"></div>

    <!-- 退款金额设置区域 -->
    <!-- 支持自动计算和手动编辑退款金额 -->
    <section class="refund-amount-section">
      <WoCell left-title="退款金额" :is-center="true" :is-border="false" :is-require="true" :is-vertical="true">
        <template #right>
          <!-- 金额输入包装器 -->
          <div class="amount-input-wrapper">
            <!-- 金额显示区域 -->
            <div class="amount-display">
              <!-- 非编辑模式：显示格式化的金额 -->
              <span @click="setIsRefundAmountEdit" v-if="!isRefundAmountEdit" :style="inputStyle" class="amount-value">
                {{ getNewSplitMoney }}
              </span>
              <!-- 编辑模式：显示数字输入框 -->
              <input ref="inputMoneyRef" :style="inputStyle" v-else type="number" class="amount-input"
                v-model.number.trim="refundData.refundAmount" @blur="blurRefundAmount" @input="inputRefundAmount" />
            </div>
            <!-- 编辑按钮 -->
            <img @click="setIsRefundAmountEdit" class="edit-icon" src="./assets/edit.png" alt="编辑">
          </div>

          <!-- 金额提示信息 -->
          <!-- 根据货物状态显示不同的金额限制提示 -->
          <div class="amount-tips" v-if="refundData.goodStatus && refundData.goodStatus.id === '0'">
            不可修改，商品总金额 ¥{{ goodPrice }} 元
          </div>
          <div class="amount-tips" v-if="refundData.goodStatus && refundData.goodStatus.id === '1'">
            最多可退款 ¥{{ goodPrice }} 元
          </div>
        </template>
      </WoCell>
    </section>

    <!-- 区域分隔线 -->
    <div class="section-divider"></div>

    <!-- 补充信息区域 -->
    <!-- 包含凭证上传和补充描述功能 -->
    <section class="refund-supplement-section">
      <!-- 上传凭证卡片 -->
      <div class="info-card">
        <!-- 卡片头部 -->
        <header class="card-header">
          <!-- 根据是否必需显示不同样式的标题 -->
          <h3 class="card-title" :class="{ 'required': isRequiredVoucher }">
            上传凭证
          </h3>
        </header>

        <!-- 卡片内容区域 -->
        <div class="card-body">
          <!-- 空状态上传区域 -->
          <!-- 当没有上传文件时显示的初始上传界面 -->
          <div class="upload-empty-state" v-if="fileList.length < 1">
            <!-- 主要上传组件 -->
            <van-uploader ref="uploaderRef" accept="image/*" :after-read="afterRead" :before-read="handleBeforeRead"
              class="primary-uploader" preview-size="0" :deletable="false" v-model="fileList" multiple :max-count="4"
              :preview-image="false">
              <!-- 上传占位符界面 -->
              <div class="upload-placeholder">
                <img class="upload-icon" src="./assets/相机icon.png" alt="上传">
                <p class="upload-title">上传凭证</p>
                <p class="upload-description">（最多4张，图片大小不能超过10M）</p>
              </div>
            </van-uploader>
            <!-- 环境特定的上传遮罩层 -->
            <div class="upload-mask" @click="captureShow = true" v-if="isEnv"></div>
          </div>

          <!-- 已上传图片展示区域 -->
          <!-- 当已有上传文件时显示的图片管理界面 -->
          <div class="uploaded-images" v-if="fileList.length >= 1">
            <!-- 继续上传按钮 -->
            <!-- 当文件数量未达到上限时显示 -->
            <div class="add-more-upload" v-if="fileList.length <= 3">
              <!-- 次要上传组件 -->
              <van-uploader ref="uploaderRef" accept="image/*" :after-read="afterRead" :before-read="handleBeforeRead"
                class="secondary-uploader" preview-size="0" :deletable="false" v-model="fileList" multiple
                :max-count="4" :preview-image="false">
                <img class="upload-icon" src="./assets/相机icon.png" alt="继续上传">
                <!-- 显示当前上传进度 -->
                <p class="upload-counter">（{{ fileList.length }}/4）</p>
              </van-uploader>
              <!-- 环境特定的上传遮罩层 -->
              <div class="upload-mask" @click="captureShow = true" v-if="isEnv"></div>
            </div>

            <!-- 图片列表展示 -->
            <!-- 遍历显示所有已上传的图片 -->
            <div class="image-item" v-for="item in fileList" :key="item.content">
              <!-- 图片预览 -->
              <img class="image-preview" :src="item.content" alt="上传的图片" />
              <!-- 删除按钮 -->
              <button @click="deleteUploadImg(item)" class="delete-btn" type="button">
                <img src="./assets/close.png" alt="删除">
              </button>

              <!-- 上传状态遮罩层 -->
              <!-- 上传中状态 -->
              <div class="upload-status-overlay" v-if="item.status === 'uploading'">
                <van-loading size="24" />
                <p class="status-text">上传中...</p>
              </div>
              <!-- 上传失败状态 -->
              <div class="upload-status-overlay" v-if="item.status === 'failed'">
                <van-icon name="close" size="24" />
                <p class="status-text">上传失败</p>
              </div>
            </div>

            <!-- 占位元素保持布局 -->
            <!-- 当图片数量少于3张时添加占位元素保持网格布局 -->
            <template v-if="fileList.length <= 2">
              <div class="image-placeholder" v-for="item in (3 - fileList.length)" :key="item + '_placeholder'"></div>
            </template>
          </div>
        </div>
      </div>

      <!-- 补充描述卡片 -->
      <div class="info-card">
        <!-- 卡片头部 -->
        <header class="card-header">
          <h3 class="card-title">补充描述</h3>
        </header>
        <!-- 卡片内容 -->
        <div class="card-body">
          <!-- 多行文本输入框，支持字数限制和提示 -->
          <van-field v-model="refundData.additionalRefundMsg" rows="3" label="" type="textarea" maxlength="200"
            show-word-limit placeholder="补充描述，有助于商家更好地处理售后问题" />
        </div>
      </div>
    </section>

    <!-- 底部操作区域 -->
    <!-- 固定在页面底部的提交按钮 -->
    <footer class="action-footer">
      <button class="submit-btn" @click="submitApplication" type="button">
        提交申请
      </button>
    </footer>

    <!-- 货物状态选择弹窗 -->
    <!-- 从底部弹出的货物状态选择器 -->
    <van-popup class="selection-popup goods-status-popup" :style="{ minHeight: '100px' }" safe-area-inset-bottom
      lock-scroll round position="bottom" v-model:show="goodsStatusPopupShow">
      <!-- 弹窗头部 -->
      <header class="popup-header">
        <h3 class="popup-title">货物状态</h3>
        <!-- 关闭按钮 -->
        <button @click="popupClose" class="close-btn" type="button">
          <img src="./assets/popupClose.png" alt="关闭">
        </button>
      </header>

      <!-- 弹窗内容区域 -->
      <div class="popup-body">
        <!-- 货物状态选项列表 -->
        <ul class="option-list">
          <!-- 遍历货物状态枚举列表 -->
          <li class="option-item" @click="goodStatusItemSelect(item)" v-for="item in goodStatusEnumList"
            :key="item.id + item.title">
            <!-- 选项文本 -->
            <span class="option-text">{{ item.title }}</span>
            <!-- 选择状态图标 -->
            <img v-if="item.isSelect" class="selection-icon" src="./assets/selectNode.png" alt="已选择">
            <img v-else class="selection-icon" src="./assets/noSelect.png" alt="未选择">
          </li>
        </ul>
      </div>
    </van-popup>

    <!-- 退款原因选择弹窗 -->
    <!-- 从底部弹出的退款原因选择器 -->
    <van-popup class="selection-popup refund-reason-popup" :style="{ minHeight: '100px' }" safe-area-inset-bottom
      lock-scroll round position="bottom" v-model:show="reasonRefundPopupShow">
      <!-- 弹窗头部 -->
      <header class="popup-header">
        <h3 class="popup-title">选择退款原因</h3>
        <!-- 关闭按钮 -->
        <button @click="popupClose" class="close-btn" type="button">
          <img src="./assets/popupClose.png" alt="关闭">
        </button>
      </header>

      <!-- 弹窗内容区域 -->
      <div class="popup-body">
        <!-- 退款原因选项列表 -->
        <ul class="option-list">
          <!-- 遍历退款原因枚举列表 -->
          <li class="option-item" @click="reasonRefundItemSelect(item)" v-for="item in reasonRefundEnumList"
            :key="item.id + item.title">
            <!-- 选项文本 -->
            <span class="option-text">{{ item.title }}</span>
            <!-- 选择状态图标 -->
            <img v-if="item.isSelect" class="selection-icon" src="./assets/selectNode.png" alt="已选择">
            <img v-else class="selection-icon" src="./assets/noSelect.png" alt="未选择">
          </li>
        </ul>
      </div>
    </van-popup>

    <!-- 拍照选择弹窗 -->
    <!-- 提供拍照和相册选择的操作表单 -->
    <van-action-sheet @select="captureActionSheetSelect" v-model:show="captureShow" :actions="captureOption"
      cancel-text="取消" description="选择方式" close-on-click-action />
  </div>
</template>

<script setup>
import { ref, computed, onMounted, nextTick, shallowRef, markRaw } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { showToast, showLoadingToast, closeToast } from 'vant'
import { isAndroid, isWopay } from 'commonkit'
import { fenToYuan } from '@utils/amount.js'
import { applyAfterSaleForRefund } from '@api/interface/afterSales.js'
import { map, some } from 'lodash-es'
import axios from 'axios'
import { useImageCompressor } from '@/composables/index.js'

// ===================== 路由管理 =====================
// 获取路由实例，用于页面跳转和参数获取
const router = useRouter()
const route = useRoute()

// ===================== 退款原因枚举定义 =====================
// 退款原因枚举列表1 - 标准退款原因选项
// 使用markRaw标记为非响应式数据，提升性能
const reasonRefundEnumList1 = markRaw([
  {
    title: '与商家协商一致退款',
    id: '1',
    isSelect: false
  },
  {
    title: '商品质量不好',
    id: '7',
    isSelect: false
  },
  {
    title: '商品与描述不符',
    id: '8',
    isSelect: false
  },
  {
    title: '商品破损/包装问题',
    id: '9',
    isSelect: false
  },
  {
    title: '少发/漏件',
    id: '6',
    isSelect: false
  },
  {
    title: '商家发错货',
    id: '10',
    isSelect: false
  },
  {
    title: '其他',
    id: '20',
    isSelect: false
  }
])

// 退款原因枚举列表2 - 另一种退款原因选项
// 针对不同场景提供的退款原因选择
const reasonRefundEnumList2 = markRaw([
  {
    title: '与商家协商一致退款',
    id: '1',
    isSelect: false
  },
  {
    title: '商品买贵了或降价',
    id: '2',
    isSelect: false
  },
  {
    title: '多拍/拍错/不想要',
    id: '3',
    isSelect: false
  },
  {
    title: '快递一直未送达',
    id: '4',
    isSelect: false
  },
  {
    title: '地址填错/不方便收货',
    id: '5',
    isSelect: false
  },
  {
    title: '少发/漏发',
    id: '6',
    isSelect: false
  },
  {
    title: '其他',
    id: '20',
    isSelect: false
  }
])

// ===================== 退款申请表单数据管理 =====================
// 退款申请表单数据
const refundData = ref({
  goodStatus: '',              // 货物状态
  reasonRefund: '',            // 退款原因
  refundAmount: 0,             // 退款金额
  refundNum: 1,                // 退款件数
  additionalRefundMsg: '',     // 补充描述
  supplierSubOrderId: ''       // 供应商子订单ID
})

// 商品总数量
const goodsNum = ref(1)

// 商品总价格
const goodPrice = ref(0)

// ===================== 弹窗状态控制 =====================
// 货物状态弹窗显示控制
const goodsStatusPopupShow = ref(false)

// 退款原因弹窗显示控制
const reasonRefundPopupShow = ref(false)

// ===================== 货物状态和退款原因选项 =====================
// 货物状态枚举列表
// 使用shallowRef和markRaw优化性能，避免深度响应式
const goodStatusEnumList = shallowRef(markRaw([
  {
    title: '已收货',
    id: '1',
    isSelect: false
  },
  {
    title: '未收到货/已拒收',
    id: '0',
    isSelect: false
  }
]))

// 当前使用的退款原因枚举列表
const reasonRefundEnumList = shallowRef([])

// ===================== 金额编辑功能 =====================
// 退款金额编辑状态控制
const isRefundAmountEdit = ref(false)

// 金额输入框引用
const inputMoneyRef = ref(null)

// ===================== 图片上传功能 =====================
// 上传文件列表
const fileList = shallowRef([])

// ===================== 拍照功能 =====================
// 拍照功能相关状态
const capture = ref('')
const captureShow = ref(false)

// 拍照选择选项配置
const captureOption = shallowRef(markRaw([
  { name: '相册', type: 1 },
  { name: '相机', type: 2 }
]))
const uploaderRef = ref(null)

// 初始化数据
const initPageData = () => {
  const { supplierSubOrderId, orderPrice, skuNum } = route.query
  goodsNum.value = Number(skuNum) || 1
  goodPrice.value = orderPrice ? fenToYuan(orderPrice) : 0
  refundData.value.refundNum = goodsNum.value
  refundData.value.refundAmount = goodPrice.value
  refundData.value.supplierSubOrderId = supplierSubOrderId
}

initPageData()

// 计算属性
const isGoodsNumInput = computed(() => {
  if (!refundData.value.goodStatus) {
    return true
  }
  return refundData.value.goodStatus.id === '0'
})

const isRequiredVoucher = computed(() => {
  const isRequiredList = ['7', '8', '9', '6', '10']
  const goodStatus = refundData.value.goodStatus
  const reasonRefund = refundData.value.reasonRefund

  return goodStatus?.id === '1' && reasonRefund && some(isRequiredList, id => id === reasonRefund.id)
})

const isEnv = computed(() => {
  return isAndroid && isWopay
})

const getNewSplitMoney = computed(() => {
  return refundData.value.refundAmount
})

const inputStyle = computed(() => {
  const length = String(refundData.value.refundAmount).length
  return {
    width: `${Math.max(length * 10 + 20, 30)}px`,
    padding: '0 5px'
  }
})

// 方法
const setReasonRefundPopupShow = () => {
  if (!refundData.value.goodStatus) {
    showToast('请先选择货物状态！')
    return
  }
  reasonRefundPopupShow.value = true
}

const captureActionSheetSelect = (item) => {
  if (item.type === 2) {
    capture.value = 'camera'
    nextTick(() => {
      uploaderRef.value.$refs.input.setAttribute('capture', 'camera')
    })
  } else {
    capture.value = ''
    nextTick(() => {
      uploaderRef.value.$refs.input.removeAttribute('capture')
    })
  }
  nextTick(() => {
    uploaderRef.value.chooseFile()
  })
}

const setIsRefundAmountEdit = () => {
  if (!refundData.value.goodStatus) {
    showToast('请先选择货物状态！')
    return
  }
  if (refundData.value.goodStatus.id === '0') {
    showToast('退款金额不可修改！')
    isRefundAmountEdit.value = false
    return
  }
  isRefundAmountEdit.value = true
  nextTick(() => {
    inputMoneyRef.value.focus()
  })
}

const submitApplication = async () => {
  const { goodStatus, reasonRefund, refundAmount, refundNum, additionalRefundMsg, supplierSubOrderId } = refundData.value

  if (!goodStatus) {
    showToast('请选择货物状态')
    return
  }
  if (!reasonRefund) {
    showToast('请选择退货原因')
    return
  }
  if (isRequiredVoucher.value && fileList.value.length === 0) {
    showToast('请上传凭证')
    return
  }

  const files = fileList.value
  if (files.length > 0) {
    const uploadingFiles = filter(files, { status: 'uploading' })
    if (uploadingFiles.length > 0) {
      showToast('图片上传中，请稍后提交')
      return
    }

    const failedFiles = filter(files, { status: 'failed' })
    if (failedFiles.length > 0) {
      showToast('有图片上传失败，请重新上传')
      return
    }
  }

  const voucherImageUrlList = map(files, 'imgUrl')
  const params = {
    supplierSubOrderId,
    applyRefundMoney: refundAmount * 100,
    additionalRefundMsg,
    isRecived: goodStatus.id,
    refundReasonNum: reasonRefund.id,
    applyRefundSkuNum: refundNum,
    voucherImageUrl: voucherImageUrlList.join(',')
  }

  showLoadingToast()
  try {
    const [err, res] = await applyAfterSaleForRefund({
      AfterSaleRefundOrderVo: JSON.stringify(params)
    })
    closeToast()
    if (!err) {
      router.replace({
        path: '/wo-after-sales-detail',
        query: {
          afterSaleId: res,
          type: 1
        }
      })
    } else {
      showToast(err.msg)
    }
  } catch (error) {
    closeToast()
    showToast('提交失败，请重试')
  }
}

const updateRefundAmount = () => {
  const averagePrice = goodPrice.value / goodsNum.value
  refundData.value.refundAmount = Number((averagePrice * refundData.value.refundNum).toFixed(2))
}

const changeRefundsNumber = () => {
  updateRefundAmount()
}

const minusRefundsNumber = () => {
  updateRefundAmount()
}

const plusRefundsNumber = () => {
  // 原代码为空函数，保持不变
}

const blurRefundsNumber = () => {
  if (refundData.value.refundNum > goodsNum.value) {
    showToast(`当前最多可退${goodsNum.value}件`)
  }
}

const { beforeRead } = useImageCompressor()

const handleBeforeRead = async (file) => {
  return await beforeRead(file, {
    maxSize: 10,
    showLoadingMessage: false,
    showToast,
    showLoadingToast,
    closeToast
  })
}

const afterRead = async (file) => {
  file.status = 'uploading'
  file.message = '上传中...'

  const formData = new FormData()
  formData.append('file', file.file)

  try {
    const res = await axios.post('/ps-ccms-core-front/v2/afterSale/uploadVoucherImageUrl', formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
    if (res.data.code === '0000') {
      file.status = 'success'
      file.message = '上传成功'
      file.imgUrl = res.data.data.imgUrl
    } else {
      file.status = 'failed'
      file.message = '上传失败'
    }
  } catch (error) {
    file.status = 'failed'
    file.message = '上传失败'
  }
}

const deleteUploadImg = (file) => {
  fileList.value = filter(fileList.value, item => item.content !== file.content)
}

const popupClose = () => {
  reasonRefundPopupShow.value = false
  goodsStatusPopupShow.value = false
}

const inputRefundAmount = (e) => {
  refundData.value.refundAmount = e.target.value
}

const blurRefundAmount = () => {
  const amount = Number(refundData.value.refundAmount)
  const maxAmount = goodPrice.value

  if (!amount || amount <= 0) {
    showToast('退款金额必须大于0元')
    refundData.value.refundAmount = maxAmount
    isRefundAmountEdit.value = false
    return
  }

  if (amount < 0.01) {
    refundData.value.refundAmount = 0.01
  } else if (amount > maxAmount) {
    showToast(`最多退款¥${maxAmount}`)
    refundData.value.refundAmount = maxAmount
  } else {
    refundData.value.refundAmount = Number(amount.toFixed(2))
  }

  isRefundAmountEdit.value = false
}

const goodStatusItemSelect = (item) => {
  goodStatusEnumList.value.forEach(i => {
    i.isSelect = false
  })
  item.isSelect = true
  refundData.value.goodStatus = item
  goodsStatusPopupShow.value = false

  if (item.id === '1') {
    refundData.value.refundAmount = goodPrice.value
    refundData.value.reasonRefund = ''
    reasonRefundEnumList1.forEach(i => {
      i.isSelect = false
    })
    reasonRefundEnumList.value = reasonRefundEnumList1
  } else {
    refundData.value.refundAmount = goodPrice.value
    refundData.value.reasonRefund = ''
    reasonRefundEnumList2.forEach(i => {
      i.isSelect = false
    })
    reasonRefundEnumList.value = reasonRefundEnumList2
  }
}

const reasonRefundItemSelect = (item) => {
  reasonRefundEnumList.value.forEach(i => {
    i.isSelect = false
  })
  item.isSelect = true
  refundData.value.reasonRefund = item
  reasonRefundPopupShow.value = false
}

// 生命周期钩子
onMounted(() => {
  goodsStatusPopupShow.value = true
})
</script>

<style scoped lang="less">
.refund-application {
  width: 100vw;
  padding-bottom: 100px;
  overflow: hidden;

  // 区域分隔线
  .section-divider {
    width: 100%;
    height: 10px;
    background: #F8F9FA;
  }

  // 基础信息区域
  .refund-basic-info {
    box-sizing: border-box;
    padding: 0 10px;

    .quantity-stepper {
      :deep(.van-stepper__input) {
        margin: 0;
        border-top: 1px solid rgba(232, 232, 232, 1);
        border-bottom: 1px solid rgba(232, 232, 232, 1);
        background-color: #FFFFFF;
      }

      :deep(.van-stepper__minus) {
        color: #000000;
        border: 1px solid rgba(232, 232, 232, 1);
        background-color: #FFFFFF;
        border-radius: 15px 0 0 15px;

        &.van-stepper__minus--disabled {
          color: #c8c9cc;
          background-color: #f7f8fa;
          cursor: not-allowed;
        }
      }

      :deep(.van-stepper__plus) {
        color: #000000;
        border: 1px solid rgba(232, 232, 232, 1);
        background-color: #FFFFFF;
        border-radius: 0 15px 15px 0;

        &.van-stepper__plus--disabled {
          color: #c8c9cc;
          background-color: #f7f8fa;
          cursor: not-allowed;
        }
      }
    }
  }

  // 退款金额区域
  .refund-amount-section {
    box-sizing: border-box;
    padding: 0 10px;

    .amount-input-wrapper {
      width: 100%;
      text-align: right;
      display: flex;
      align-items: center;
      justify-content: flex-end;

      .amount-display {
        display: flex;
        align-items: baseline;
        font-size: 18px;
        color: #F97316;
        font-weight: 400;
        height: 22px;
        line-height: 20px;

        .amount-value,
        .amount-input {
          display: flex;
          font-weight: 600;
          outline: none;
          border: none;
          background: transparent;
          box-shadow: none;

          &:focus {
            outline: none;
            border: none;
            box-shadow: none;
            transform: none;
          }
        }

        &:before {
          content: '￥';
          font-size: 13px;
        }
      }

      .edit-icon {
        margin-left: 8px;
        width: 14px;
        height: 14px;
        cursor: pointer;
      }
    }

    .amount-tips {
      margin-top: 8px;
      font-size: 12px;
      color: #718096;
      text-align: right;
      line-height: 1;
      font-weight: 400;
    }
  }

  // 补充信息区域
  .refund-supplement-section {
    box-sizing: border-box;
    padding: 0 10px;

    .info-card {
      padding: 17px 0 0 0;

      .card-header {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-bottom: 10px;

        .card-title {
          font-size: 16px;
          color: #171E24;
          line-height: 1;
          font-weight: 400;
          margin: 0;

          &.required {
            position: relative;

            &:after {
              content: '*';
              position: absolute;
              top: -3px;
              right: -10px;
              color: #EF4444;
              font-size: 25px;
            }
          }
        }
      }

      .card-body {
        width: 100%;
        min-height: 100px;
        border: 1px solid #E2E8EE;
        border-radius: 10px;
        box-sizing: border-box;
        padding: 10px;

        :deep(.van-cell.van-field) {
          padding: 0;
        }

        // 空状态上传区域
        .upload-empty-state {
          width: 100%;
          height: 100%;
          position: relative;

          .primary-uploader {
            width: 100%;
            display: flex;
            justify-content: center;
          }

          .upload-placeholder {
            width: 100%;
            height: 100%;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;

            .upload-icon {
              width: 40px;
              height: 40px;
            }

            .upload-title {
              margin-top: 6px;
              font-size: 12px;
              color: #718096;
              text-align: center;
              line-height: 1;
              font-weight: 400;
            }

            .upload-description {
              margin-top: 6px;
              font-size: 12px;
              color: #718096;
              text-align: center;
              line-height: 1;
              font-weight: 400;
            }
          }

          .upload-mask {
            position: absolute;
            top: 0;
            left: 0;
            background-color: transparent;
            width: 100%;
            height: 100%;
          }
        }

        // 已上传图片展示区域
        .uploaded-images {
          width: 100%;
          display: flex;
          justify-content: space-between;
          flex-wrap: wrap;
          gap: 2%;

          .add-more-upload {
            position: relative;
            width: 23%;
            height: 75px;

            .secondary-uploader {
              width: 100%;
              height: 75px;
              background: #FFFFFF;
              border: 1px dashed #E2E8EE;
              border-radius: 4px;
              padding: 5px;
              display: flex;
              flex-direction: column;
              align-items: center;
              justify-content: space-around;
              box-sizing: border-box;

              .upload-icon {
                width: 40px;
                height: 40px;
              }

              .upload-counter {
                font-size: 12px;
                color: #718096;
                text-align: center;
                line-height: 1;
                font-weight: 400;
                margin: 0;
              }
            }

            .upload-mask {
              position: absolute;
              top: 0;
              left: 0;
              background-color: transparent;
              width: 100%;
              height: 75px;
            }
          }

          .image-item {
            width: 23%;
            height: 75px;
            background: #FFFFFF;
            border-radius: 4px;
            position: relative;

            .image-preview {
              width: 100%;
              height: 75px;
              border-radius: 4px;
              object-fit: cover;
            }

            .delete-btn {
              z-index: 55;
              position: absolute;
              top: -7px;
              right: -7px;
              background: none;
              border: none;
              padding: 0;
              cursor: pointer;

              img {
                width: 15px;
                height: 15px;
              }
            }

            .upload-status-overlay {
              z-index: 54;
              width: 100%;
              height: 100%;
              position: absolute;
              top: 0;
              left: 0;
              color: #FFFFFF;
              background-color: rgba(50, 50, 51, 0.88);
              display: flex;
              flex-direction: column;
              align-items: center;
              justify-content: center;
              border-radius: 4px;

              .status-text {
                margin-top: 6px;
                padding: 0 4px;
                font-size: 12px;
                line-height: 14px;
              }
            }
          }

          .image-placeholder {
            width: 23%;
            height: 75px;
          }
        }
      }
    }
  }

  // 底部操作区域
  .action-footer {
    position: fixed;
    bottom: 0;
    left: 0;
    box-sizing: border-box;
    padding: 12px 10px;
    width: 100%;
    height: 90px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #FFFFFF;

    .submit-btn {
      width: 100%;
      height: 40px;
      background-image: var(--wo-biz-theme-gradient-1);
      border-radius: 22px;
      text-align: center;
      line-height: 40px;
      font-size: 16px;
      color: #FFFFFF;
      font-weight: 500;
      border: none;
      cursor: pointer;

      &:active {
        opacity: 0.8;
      }
    }
  }
}

// 选择弹窗样式
.selection-popup {
  box-sizing: border-box;
  padding: 20px;

  .popup-header {
    display: flex;
    align-items: center;
    margin-bottom: 15px;

    .popup-title {
      flex: 1;
      font-size: 17px;
      color: #171E24;
      text-align: center;
      line-height: 1;
      font-weight: 400;
      margin: 0;
    }

    .close-btn {
      background: none;
      border: none;
      padding: 0;
      cursor: pointer;

      img {
        width: 14px;
        height: 14px;
      }
    }
  }

  .popup-body {
    .option-list {
      list-style: none;
      padding: 0;
      margin: 0;

      .option-item {
        display: flex;
        align-items: center;
        justify-content: space-between;
        height: 45px;
        cursor: pointer;

        &:hover {
          background-color: #F8F9FA;
        }

        .option-text {
          margin-right: 10px;
          font-size: 15px;
          color: #171E24;
          line-height: 1;
          font-weight: 400;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }

        .selection-icon {
          width: 18px;
          height: 18px;
          flex-shrink: 0;
        }
      }
    }
  }
}
</style>
