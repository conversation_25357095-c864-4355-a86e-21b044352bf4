<!--
/**
 * 地址新增/编辑页面组件
 *
 * 主要功能：
 * 1. 提供用户收货地址的新增和编辑功能，支持完整的地址信息录入
 * 2. 集成地址表单组件，实现收货人姓名、手机号、详细地址等信息的输入和验证
 * 3. 支持省市区三级联动选择，确保地址信息的准确性和完整性
 * 4. 实现表单实时验证，只有在所有必填项都正确填写后才允许提交
 * 5. 提供防抖保存机制，避免用户重复点击导致的重复提交
 * 6. 支持编辑模式数据回显，可以修改已有的地址信息
 * 7. 集成Toast提示，为用户提供清晰的操作反馈
 *
 * 技术特点：
 * - 使用Vue 3 Composition API实现响应式状态管理
 * - 采用路由参数判断新增/编辑模式，提供不同的业务逻辑
 * - 实现姓名信息的base64编码处理，保护用户隐私数据
 * - 集成防抖机制，优化用户体验和接口调用
 * - 支持多种数据获取方式，优先使用路由传参，兜底API查询
 *
 * 使用场景：
 * - 用户需要添加新的收货地址时
 * - 用户需要修改已有的收货地址信息时
 * - 需要完整地址信息录入和验证的业务场景
 */
-->

<template>
  <!-- 地址表单页面主容器 -->
  <div class="address-form">
    <!-- 页面头部区域，显示页面标题和说明 -->
    <div class="address-form__header">
      <!-- 动态显示页面标题，根据编辑模式切换显示内容 -->
      <h2 class="address-form__title">{{ isEditMode ? '编辑地址' : '新增地址' }}</h2>
      <!-- 页面说明文字，提示用户填写准确信息 -->
      <p class="address-form__subtitle">请填写准确的收货信息</p>
    </div>

    <!-- 表单内容区域 -->
    <div class="address-form__content">
      <!-- 表单容器包装器，提供样式和布局 -->
      <div class="address-form__wrapper">
        <!-- 地址表单组件，处理具体的表单输入和验证逻辑 -->
        <!-- 传入初始数据用于编辑模式的数据回显 -->
        <!-- 监听表单验证状态变化，控制保存按钮的可用性 -->
        <!-- 监听地区选择变化，可用于后续的业务逻辑扩展 -->
        <AddressForm
          ref="addressFormRef"
          :initial-data="initialAddressData"
          @validate="onFormValidate"
          @region-change="onRegionChange"
        />
      </div>
    </div>

    <!-- 底部操作栏，包含保存按钮 -->
    <AddressActionBar size="xlarge" @add="saveAddress" class="address-form__actions">
      <!-- 保存按钮，支持加载状态和禁用状态 -->
      <!-- 根据表单验证状态控制按钮的可用性 -->
      <!-- 显示加载状态，防止用户重复点击 -->
      <WoButton
        size="xlarge"
        type="primary"
        block
        :loading="isSubmitting"
        :disabled="!isFormValid"
        @click="saveAddress"
      >
        {{ isSubmitting ? '保存中...' : '保存地址' }}
      </WoButton>
    </AddressActionBar>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { debounce } from 'lodash-es'
import WoButton from '@components/WoElementCom/WoButton/WoButton.vue'
import AddressActionBar from '@components/WoElementCom/WoActionBar.vue'
import AddressForm from '@components/Common/Address/AddressForm.vue'
import { closeToast, showLoadingToast, showToast } from 'vant'
import { addAddr, editAddr, queryUserAddrList } from '@api/interface/address.js'
import base64 from '@utils/base64.js'

// ==================== 路由和导航管理 ====================
// 路由实例，用于页面导航和参数获取
const router = useRouter()
// 当前路由信息，用于获取页面参数和判断操作模式
const route = useRoute()

// 计算属性：判断当前是否为编辑模式
// 通过检查路由查询参数中是否存在addrId来判断
const isEditMode = computed(() => !!route.query.addrId)

// ==================== 表单状态管理 ====================
// 表单提交状态，用于控制按钮加载状态和防止重复提交
const isSubmitting = ref(false)
// 表单验证状态，用于控制保存按钮的可用性
const isFormValid = ref(false)
// 初始地址数据，用于编辑模式的数据回显
const initialAddressData = ref({})

// 地址表单组件的引用，用于调用子组件的方法
const addressFormRef = ref(null)

// 表单验证状态变化的回调函数
// 当子组件表单验证状态发生变化时触发
const onFormValidate = ({ isValid }) => {
  // 更新表单验证状态，控制保存按钮的可用性
  isFormValid.value = isValid
}

// 地区选择变化的回调函数
// 当用户选择省市区信息发生变化时触发
const onRegionChange = (regionData) => {
  // 预留的地区变化处理逻辑，可用于后续业务扩展
  console.log('地区变化:', regionData)
}

// ==================== 数据处理和API调用 ====================
// 构建API请求参数的工具函数
// 从表单组件获取数据并进行必要的数据处理
const buildRequestParams = () => {
  // 检查表单组件引用是否存在
  if (!addressFormRef.value) return {}

  // 从表单组件获取原始参数
  const params = addressFormRef.value.buildRequestParams()
  // 对收货人姓名进行base64编码处理，保护用户隐私信息
  return {
    ...params,
    recName: base64(params.recName)
  }
}

// 保存地址信息的主要业务函数
// 使用防抖机制避免用户重复点击导致的重复提交
const saveAddress = debounce(async () => {
  // 如果正在提交中，直接返回，避免重复提交
  if (isSubmitting.value) return

  // 执行表单验证，如果验证失败则不继续执行
  if (!addressFormRef.value?.validateForm()) return

  // 设置提交状态为true，显示加载状态
  isSubmitting.value = true
  // 显示加载提示
  showLoadingToast()

  try {
    // 构建请求参数
    const baseParams = buildRequestParams()
    // 获取地址ID，用于判断是编辑还是新增
    const addrId = route.query.addrId

    let result
    if (isEditMode.value) {
      // 编辑模式：调用编辑地址接口
      result = await editAddr({
        ...baseParams,
        addressId: addrId
      })
    } else {
      // 新增模式：调用新增地址接口
      result = await addAddr(baseParams)
    }

    // 解构API返回结果，获取错误信息
    const [err] = result

    if (err) {
      // 如果有错误，显示错误提示并返回
      showToast(err.msg || `${isEditMode.value ? '保存' : '新增'}失败`)
      return
    }

    // 操作成功，显示成功提示
    showToast(`${isEditMode.value ? '保存' : '新增'}成功`)

    // 延迟返回上一页，让用户看到成功提示
    setTimeout(() => {
      router.back()
    }, 1500)

  } catch (error) {
    // 捕获异常，显示错误提示
    console.error('保存地址失败:', error)
    showToast(error.message || `${isEditMode.value ? '保存' : '新增'}失败`)
  } finally {
    // 无论成功还是失败，都要关闭加载提示并重置提交状态
    closeToast()
    isSubmitting.value = false
  }
}, 300)

// ==================== 编辑模式数据初始化 ====================
// 初始化编辑模式数据的异步函数
// 负责获取要编辑的地址信息并设置到表单中
const initEditModeData = async () => {
  // 获取地址ID，如果没有则不是编辑模式，直接返回
  const addrId = route.query.addrId
  if (!addrId) return

  // 优先使用路由参数中传递的地址数据
  // 这种方式可以避免额外的API调用，提高性能
  if (route.params.address) {
    initialAddressData.value = route.params.address
    return
  }

  // 如果路由参数中没有地址数据，则通过API获取
  const [err, addrList] = await queryUserAddrList()
  if (err || !addrList?.length) {
    // 获取失败或没有数据，显示错误提示
    showToast(err?.msg || '获取地址信息失败')
    return
  }

  // 在地址列表中查找匹配的地址
  const matchedAddr = addrList.find(item => item.addressId === addrId)
  if (matchedAddr) {
    // 找到匹配的地址，设置为初始数据
    initialAddressData.value = matchedAddr
  } else {
    // 没有找到匹配的地址，显示错误提示
    showToast('未找到对应的地址信息')
  }
}

// ==================== 生命周期管理 ====================
// 组件挂载时的初始化操作
onMounted(async () => {
  try {
    // 如果是编辑模式，初始化编辑数据
    await initEditModeData()
  } catch (error) {
    // 捕获初始化过程中的异常
    console.error('页面初始化失败:', error)
    showToast('页面初始化失败')
  }
})
</script>

<style scoped lang="less">
.address-form {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  background: linear-gradient(180deg, #F8F9FA 0%, #FFFFFF 100%);

  &__header {
    padding: 24px 16px 16px;
    text-align: center;
    background: #FFFFFF;
    border-bottom: 1px solid #F0F2F5;
  }

  &__title {
    font-size: 20px;
    font-weight: 600;
    color: #171E24;
    margin: 0 0 8px 0;
    letter-spacing: 0.3px;
  }

  &__subtitle {
    font-size: 13px;
    color: #718096;
    margin: 0;
    letter-spacing: 0.1px;
  }

  &__content {
    flex: 1;
    padding: 10px;
    background-color: #F8F9FA;
  }

  &__wrapper {
    background: #FFFFFF;
    margin: 0;
    border-radius: 12px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.04);
    border: 1px solid rgba(0, 0, 0, 0.02);
    padding: 4px 10px;
  }
}
</style>
