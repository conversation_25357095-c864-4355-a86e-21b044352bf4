<!--
/**
 * 地址新增/编辑页面组件（级联选择器版本）
 *
 * 主要功能：
 * 1. 提供用户收货地址的新增和编辑功能，采用级联选择器实现地区选择
 * 2. 集成自定义表单组件，实现收货人姓名、手机号、详细地址等信息的输入和验证
 * 3. 支持省市区街道四级联动选择，通过级联选择器提供流畅的地区选择体验
 * 4. 实现动态数据加载，按需获取下级地区数据，优化性能和用户体验
 * 5. 提供完整的表单验证机制，确保数据的准确性和完整性
 * 6. 支持编辑模式数据回显，包括地区层级数据的完整重建
 * 7. 集成防抖保存机制和Toast提示，提供良好的用户交互体验
 *
 * 技术特点：
 * - 使用Vue 3 Composition API实现响应式状态管理
 * - 采用Vant级联选择器组件，提供原生级联选择体验
 * - 实现智能数据缓存，避免重复请求相同的地区数据
 * - 支持地区数据的动态加载和树形结构构建
 * - 集成姓名信息的base64编码处理，保护用户隐私
 * - 实现复杂的地区数据回显逻辑，支持编辑模式的完整数据重建
 *
 * 使用场景：
 * - 需要精确地区选择的地址录入场景
 * - 要求流畅用户体验的移动端地址管理
 * - 需要支持四级地区联动的业务场景
 */
-->

<template>
  <!-- 地址表单页面主容器 -->
  <div class="address-form">
    <!-- 表单内容区域 -->
    <div class="address-form__content">
      <!-- 收货人姓名输入框 -->
      <WoFormItem2 label="收货人" v-model="addressForm.recName" placeholder="请填写收货人姓名" />
      
      <!-- 手机号输入框，限制最大长度为11位 -->
      <WoFormItem2 label="手机号" v-model="addressForm.recPhone" maxlength="11" placeholder="请填写手机号" />
      
      <!-- 地区选择器，点击触发级联选择弹窗 -->
      <WoFormItem2 label="所在地区" @click="onSelectRegion">
        <!-- 自定义输入区域，显示已选择的地区信息 -->
        <template #input>
          <!-- 地区显示文本，根据是否有值切换样式 -->
          <div class="address-form__region-value"
            :class="{ 'address-form__region-value--placeholder': !addressForm.region }">
            {{ addressForm.region || '请选择地址' }}
          </div>
        </template>
        <!-- 右侧箭头图标，点击触发地区选择 -->
        <template #rightIcon>
          <div class="address-form__arrow-icon" @click="onSelectRegion"></div>
        </template>
      </WoFormItem2>
      
      <!-- 详细地址输入框，支持多行文本和自动调整高度 -->
      <WoFormItem2 label="详细地址" rows="1" type="textarea" autosize v-model="addressForm.addrDetail"
        placeholder="请填写详细地址" />
    </div>
    
    <!-- 底部操作按钮区域 -->
    <div class="address-form__actions">
      <!-- 保存按钮，支持加载状态显示 -->
      <WoButton size="xlarge" type="primary" block :loading="isSubmitting" @click="saveAddress">
        保存
      </WoButton>
    </div>
  </div>
  
  <!-- 地区选择级联弹窗 -->
  <!-- 设置为底部弹出，圆角样式，禁用懒加载确保数据及时显示 -->
  <van-popup v-model:show="showCascader" round position="bottom" :lazy-render="false">
    <!-- 级联选择器组件，支持多级地区选择 -->
    <!-- 绑定选中值、选项数据和字段映射配置 -->
    <!-- 监听选择变化、完成选择和关闭事件 -->
    <van-cascader class="address-form__cascader" v-model="cascaderValue" title="所选地区" :options="cascaderOptions"
      :field-names="cascaderFieldNames" @close="showCascader = false" @change="onCascaderChange"
      @finish="onCascaderFinish" />
  </van-popup>
</template>

<script setup>
import { reactive, ref, onMounted, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { debounce } from 'lodash-es'
import WoFormItem2 from '@components/WoElementCom/WoFormItem2.vue'
import WoButton from '@components/WoElementCom/WoButton/WoButton.vue'
import { closeToast, showLoadingToast, showToast } from 'vant'
import { addAddr, editAddr, queryAddrArea, queryUserAddrList } from '@api/interface/address.js'
import base64 from '@utils/base64.js'

// ==================== 路由和导航管理 ====================
// 路由实例，用于页面导航和参数获取
const router = useRouter()
// 当前路由信息，用于获取页面参数和判断操作模式
const route = useRoute()

// 计算属性：判断当前是否为编辑模式
// 通过检查路由查询参数中是否存在addrId来判断
const isEditMode = computed(() => !!route.query.addrId)

// ==================== 表单状态和数据管理 ====================
// 表单提交状态，用于控制按钮加载状态和防止重复提交
const isSubmitting = ref(false)

// 地址表单数据对象，包含所有需要提交的字段
const addressForm = reactive({
  recName: '',    // 收货人姓名
  recPhone: '',   // 收货人手机号码
  region: '',     // 地区文本描述，用于显示选中的完整地址
  addrDetail: '', // 详细地址信息
  areaId: '',     // 最后一级地区的唯一标识ID
  areaType: ''    // 最后一级地区的类型（1省2市3区4街道）
})

// 手机号码格式验证正则表达式
// 匹配1开头的11位数字
const phoneRegex = /^1\d{10}$/

// ==================== 级联选择器状态管理 ====================
// 控制级联选择器弹窗的显示状态
const showCascader = ref(false)
// 当前选中的地区ID值（级联选择器的单值模式）
const cascaderValue = ref('')
// 当前选中的完整地区对象数组，包含从省到最后一级的完整路径
const cascaderValueDetails = ref([])
// 级联选择器的数据源，树形结构存储所有地区数据
const cascaderOptions = ref([])

// 级联选择器字段映射配置
// 定义数据对象中各字段与组件期望字段的映射关系
const cascaderFieldNames = {
  text: 'areaName',     // 显示文本对应的字段名
  value: 'areaId',      // 值对应的字段名
  children: 'children'  // 子选项对应的字段名
}

// ==================== 地址数据处理工具函数 ====================
// 构建地址数组 - 将后端地址对象转换为级联选择器需要的标准数据格式
// 处理省市区街道四级地址数据，过滤空值并统一数据结构
const buildAddressArray = (address) => {
  // 定义四级地址层级数据
  const levels = [
    { id: address.provinceId, name: address.provinceName, type: '1' }, // 省级
    { id: address.cityId, name: address.cityName, type: '2' },         // 市级
    { id: address.countyId, name: address.countyName, type: '3' },     // 区县级
    { id: address.townId, name: address.townName, type: '4' }          // 街道级
  ]

  // 过滤掉名称为空的层级，并转换为级联选择器需要的数据格式
  return levels
    .filter(level => level.name) // 只保留有名称的地区层级
    .map(level => ({
      areaId: level.id,      // 地区唯一标识
      areaName: level.name,  // 地区显示名称
      areaType: level.type   // 地区类型（1省2市3区4街道）
    }))
}

// 填充地址表单数据的核心函数
// 用于编辑模式下将已有地址数据回显到表单中
const fillAddressForm = (address) => {
  // 填充基础表单字段数据
  Object.assign(addressForm, {
    recName: address.recName || '',     // 收货人姓名
    recPhone: address.recPhone || '',   // 收货人手机号
    // 拼接完整地区显示文本，用斜杠分隔各级地区名称
    region: [address.provinceName, address.cityName, address.countyName, address.townName]
      .filter(Boolean).join('/') || '',
    addrDetail: address.addrDetail || '', // 详细地址
    areaId: address.areaId || '',         // 最后一级地区ID
    areaType: address.areaType || ''      // 最后一级地区类型
  })

  // 构建级联选择器需要的地址层级数组
  const addrArr = buildAddressArray(address)
  cascaderValueDetails.value = addrArr

  // 设置级联选择器的当前选中值为最后一级地区的ID
  if (addrArr.length > 0) {
    cascaderValue.value = addrArr[addrArr.length - 1].areaId
  }
}

// 查询已选择地址的详细层级信息
// 用于编辑模式下重建完整的地址层级数据，确保级联选择器能正确显示
const querySelectedAddrInfo = async () => {
  // 如果没有已选中的地址数据，直接返回
  if (cascaderValueDetails.value.length === 0) return

  // 加载指定层级的地址数据的内部函数
  // 根据当前层级和父级对象，查询并设置子级数据
  const loadAddressLevel = async (index, parentObj) => {
    // 获取当前层级的地址信息
    const current = cascaderValueDetails.value[index]
    if (!current) return null

    // 构建API查询参数，包含地区ID和类型
    const area = `{"areaId":"${current.areaId}","areaType":"${current.areaType}"}`
    const [err, json] = await queryAddrArea(area)
    // 如果查询失败或没有数据，返回null
    if (err || json.length === 0) return null

    // 第一级（省级）的特殊处理逻辑
    if (index === 0) {
      // 在已有的省级数据中查找匹配项
      const obj = cascaderOptions.value.find(item => item.areaId === current.areaId)
      if (obj) {
        // 设置省级的子级数据（市级数据）
        obj.children = json
        return obj
      }
      return null
    }

    // 其他级别（市、区、街道）的处理逻辑
    if (parentObj && parentObj.children) {
      // 在父级的子数据中查找当前层级的匹配项
      const obj = parentObj.children.find(item => item.areaId === current.areaId)
      if (obj) {
        // 设置当前层级的子级数据
        obj.children = json
        return obj
      }
    }
    return null
  }

  // 按层级顺序依次加载地址数据
  // 先加载省级数据的子级（市级数据）
  const provinceObj = await loadAddressLevel(0)
  if (!provinceObj) return

  // 加载市级数据的子级（区县数据）
  const cityObj = await loadAddressLevel(1, provinceObj)
  if (!cityObj) return

  // 加载区县数据的子级（街道数据）
  await loadAddressLevel(2, cityObj)
  // 街道级别是最后一级，不需要继续加载子级数据
}

// ==================== 级联选择器交互处理 ====================
// 地区选择按钮点击处理函数
// 显示级联选择器弹窗
const onSelectRegion = () => {
  showCascader.value = true
}

// 级联选择器选项变化处理函数
// 当用户在级联选择器中选择某一级地区时触发，动态加载下级数据
const onCascaderChange = debounce(async ({ selectedOptions, tabIndex }) => {
  // 获取当前选中的地区对象
  const selected = selectedOptions[tabIndex]

  // 如果当前节点已经加载过子节点数据，则不需要重复请求
  if (selected.children && selected.children.length > 0) {
    return
  }

  // 构建API查询参数，包含地区ID和类型信息
  const area = JSON.stringify({
    areaId: selected.areaId,
    areaType: selected.areaType
  })

  try {
    // 显示加载提示
    showLoadingToast()
    // 调用API查询下一级地区数据
    const [err, json] = await queryAddrArea(area)
    // 关闭加载提示
    closeToast()

    if (err) {
      // 查询失败，显示错误提示
      showToast(err.msg || '查询失败')
      return
    }

    // 如果没有下一级数据，说明当前就是最后一级，直接完成选择
    if (!json || json.length === 0) {
      // 标记为叶子节点，表示没有子级数据
      selected.children = null
      // 通过创建新引用触发Vue的响应式更新
      cascaderOptions.value = [...cascaderOptions.value]
      // 直接完成选择流程
      onCascaderFinish(selectedOptions)
      return
    }

    // 更新树形数据结构中指定节点的子数据
    // 根据选择路径找到目标节点并设置其子数据
    const updateNodeChildren = (options, path, newChildren) => {
      let current = options
      // 遍历路径，逐级找到目标节点的父节点
      for (let i = 0; i < path.length - 1; i++) {
        const node = current.find(item => item.areaId === path[i].areaId)
        if (node && node.children) {
          current = node.children
        } else {
          return false // 路径中断，更新失败
        }
      }

      // 找到目标节点并设置其子数据
      const targetNode = current.find(item => item.areaId === path[path.length - 1].areaId)
      if (targetNode) {
        targetNode.children = newChildren
        return true // 更新成功
      }
      return false // 未找到目标节点
    }

    // 为新的子数据添加空的children数组，为后续可能的子级加载做准备
    const childrenData = json.map(item => ({ ...item, children: [] }))
    // 执行数据更新操作
    const success = updateNodeChildren(cascaderOptions.value, selectedOptions.slice(0, tabIndex + 1), childrenData)

    if (success) {
      // 更新成功，触发Vue的响应式更新
      cascaderOptions.value = [...cascaderOptions.value]
    }
  } catch (err) {
    // 捕获异常，记录错误日志并显示用户友好的错误提示
    console.error('查询下一级数据失败:', err)
    closeToast()
    showToast(err.message || err.msg || '查询失败')
  }
}, 300) // 300ms防抖，避免用户快速点击导致的重复请求

// 级联选择器完成选择的处理函数
// 当用户完成地区选择时触发，更新表单数据
const onCascaderFinish = (selectedOptions) => {
  // 关闭级联选择器弹窗
  showCascader.value = false
  // 保存完整的选择路径数据
  cascaderValueDetails.value = selectedOptions

  // 处理选中的地区数据
  if (selectedOptions && selectedOptions.length > 0) {
    // 拼接选中的地区名称，用斜杠分隔，形成完整的地区显示文本
    addressForm.region = selectedOptions.map(option => option.areaName).join('/')

    // 存储最后一级地区的ID和类型信息，用于表单提交
    const lastOption = selectedOptions[selectedOptions.length - 1]
    addressForm.areaId = lastOption.areaId     // 最后一级地区ID
    addressForm.areaType = lastOption.areaType // 最后一级地区类型
  }
}

// ==================== 表单验证和数据提交 ====================
// 表单数据验证函数
// 对所有必填字段进行完整性和格式验证
const validateForm = () => {
  // 验证收货人姓名是否为空
  if (!addressForm.recName.trim()) {
    showToast('请填写收货人姓名')
    return false
  }
  // 验证手机号是否为空
  if (!addressForm.recPhone.trim()) {
    showToast('请填写手机号')
    return false
  }
  // 验证手机号格式是否正确（1开头的11位数字）
  if (!phoneRegex.test(addressForm.recPhone)) {
    showToast('请填写正确的手机号')
    return false
  }
  // 验证是否已选择地区
  if (!addressForm.region) {
    showToast('请选择所在地区')
    return false
  }
  // 验证详细地址是否为空
  if (!addressForm.addrDetail.trim()) {
    showToast('请填写详细地址')
    return false
  }
  // 所有验证通过
  return true
}

// 构建API请求参数的函数
// 将表单数据和地区选择数据转换为后端接口需要的格式
const buildRequestParams = () => {
  // 获取完整的地区选择路径数据
  const addr = cascaderValueDetails.value
  return {
    // 收货人姓名进行base64编码处理，保护用户隐私
    recName: base64(addressForm.recName.trim()),
    // 收货人手机号，去除首尾空格
    recPhone: addressForm.recPhone.trim(),
    // 详细地址，去除首尾空格
    addrDetail: addressForm.addrDetail.trim(),
    // 省级地区信息（第0级）
    provinceId: addr[0]?.areaId || '',
    provinceName: addr[0]?.areaName || '',
    // 市级地区信息（第1级）
    cityId: addr[1]?.areaId || '',
    cityName: addr[1]?.areaName || '',
    // 区县级地区信息（第2级）
    countyId: addr[2]?.areaId || '',
    countyName: addr[2]?.areaName || '',
    // 街道级地区信息（第3级）
    townId: addr[3]?.areaId || '',
    townName: addr[3]?.areaName || ''
  }
}

// 保存地址信息的主要业务函数
// 使用防抖机制避免用户重复点击导致的重复提交
const saveAddress = debounce(async () => {
  // 如果正在提交中，直接返回，避免重复提交
  if (isSubmitting.value) return

  // 执行表单验证，如果验证失败则不继续执行
  if (!validateForm()) return

  // 设置提交状态为true，显示加载状态
  isSubmitting.value = true
  // 显示加载提示
  showLoadingToast()

  try {
    // 构建请求参数
    const baseParams = buildRequestParams()
    // 获取地址ID，用于判断是编辑还是新增
    const addrId = route.query.addrId

    let result
    if (isEditMode.value) {
      // 编辑模式：调用编辑地址接口
      result = await editAddr({
        ...baseParams,
        addressId: addrId
      })
    } else {
      // 新增模式：调用新增地址接口
      result = await addAddr(baseParams)
    }

    // 解构API返回结果，获取错误信息
    const [err] = result

    if (err) {
      // 如果有错误，显示错误提示并返回
      showToast(err.msg || `${isEditMode.value ? '保存' : '新增'}失败`)
      return
    }

    // 操作成功，显示成功提示
    showToast(`${isEditMode.value ? '保存' : '新增'}成功`)

    // 延迟返回上一页，让用户看到成功提示
    setTimeout(() => {
      router.back()
    }, 1500)

  } catch (error) {
    // 捕获异常，显示错误提示
    console.error('保存地址失败:', error)
    showToast(error.message || `${isEditMode.value ? '保存' : '新增'}失败`)
  } finally {
    // 无论成功还是失败，都要关闭加载提示并重置提交状态
    closeToast()
    isSubmitting.value = false
  }
}, 300) // 300ms防抖延迟

// ==================== 数据初始化和生命周期管理 ====================
// 初始化省级地区数据的函数
// 页面加载时首先获取所有省级数据，作为级联选择器的根数据
const initProvinceData = async () => {
  // 调用API获取省级地区数据（不传参数默认获取省级数据）
  const [err, data] = await queryAddrArea()
  if (err) {
    // 获取失败，显示错误提示
    showToast(err.msg)
    return false
  }

  // 为每个省级数据添加空的children数组，为后续子级数据加载做准备
  cascaderOptions.value = data.map(item => ({ ...item, children: [] }))
  return true // 初始化成功
}

// 初始化编辑模式数据的异步函数
// 负责获取要编辑的地址信息并设置到表单中
const initEditModeData = async () => {
  // 获取地址ID，如果没有则不是编辑模式，直接返回
  const addrId = route.query.addrId
  if (!addrId) return

  // 优先使用路由参数中传递的地址数据
  // 这种方式可以避免额外的API调用，提高性能
  if (route.params.address) {
    // 填充表单数据
    fillAddressForm(route.params.address)
    // 查询并重建地址的完整层级数据
    await querySelectedAddrInfo()
    return
  }

  // 如果路由参数中没有地址数据，则通过API获取
  const [err, addrList] = await queryUserAddrList()
  if (err || !addrList?.length) {
    // 获取失败或没有数据，显示错误提示
    showToast(err?.msg || '获取地址信息失败')
    return
  }

  // 在地址列表中查找匹配的地址
  const matchedAddr = addrList.find(item => item.addressId === addrId)
  if (matchedAddr) {
    // 找到匹配的地址，填充表单数据
    fillAddressForm(matchedAddr)
    // 查询并重建地址的完整层级数据
    await querySelectedAddrInfo()
  } else {
    // 没有找到匹配的地址，显示错误提示
    showToast('未找到对应的地址信息')
  }
}

// ==================== 生命周期钩子 ====================
// 组件挂载时的初始化操作
onMounted(async () => {
  try {
    // 首先初始化省级地区数据，这是级联选择器的基础数据
    await initProvinceData()
    // 如果是编辑模式，初始化编辑数据和地址层级结构
    await initEditModeData()
  } catch (error) {
    // 捕获初始化过程中的异常
    console.error('页面初始化失败:', error)
    showToast('页面初始化失败')
  }
})
</script>

<style scoped lang="less">
.address-form {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #FFFFFF;

  &__content {
    flex: 1;
    padding: 0 17px;
  }

  &__region-value {
    flex: 1;
    font-size: 14px;
    color: #171E24;

    &--placeholder {
      color: #718096;
    }
  }

  &__actions {
    padding: 40px 17px 20px;
    margin-top: auto;
  }

  &__arrow-icon {
    width: 6px;
    height: 11px;
    margin-left: 5px;
    background: url('@/static/images/arrow-right-black.png') no-repeat center;
    background-size: contain;
    cursor: pointer;
  }

  &__cascader {
    :deep(.van-tabs__line) {
      background: var(--wo-biz-theme-color);
    }

    :deep(.van-cascader__option.van-cascader__option--selected) {
      color: var(--wo-biz-theme-color);
    }
  }
}
</style>
