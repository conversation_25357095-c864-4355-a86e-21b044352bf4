<!--
/**
 * 商品列表骨架屏组件
 *
 * 主要功能：
 * 1. 在商品数据加载时显示骨架屏效果，提升用户体验
 * 2. 模拟商品列表的布局结构，包括商品图片、名称、规格、价格、数量等区域
 * 3. 提供流畅的加载动画效果，减少用户等待时的焦虑感
 * 4. 与真实商品列表保持一致的尺寸和布局，确保无缝切换
 *
 * 技术特点：
 * - 使用CSS动画实现流畅的骨架屏效果
 * - 采用渐变背景模拟内容加载状态
 * - 响应式设计，适配不同屏幕尺寸
 * - 无需JavaScript逻辑，纯CSS实现
 *
 * 使用场景：
 * - 订单确认页面商品信息加载时的占位显示
 * - 任何需要商品列表加载状态的页面
 */
-->

<template>
  <!-- 商品列表骨架屏主容器 -->
  <div class="goods-skeleton">
    <!-- 商品项骨架 -->
    <div class="goods-skeleton__item">
      <!-- 商品图片骨架 -->
      <div class="goods-skeleton__image"></div>
      <!-- 商品信息骨架区域 -->
      <div class="goods-skeleton__info">
        <!-- 商品名称骨架线 -->
        <div class="skeleton-line goods-skeleton__name"></div>
        <!-- 商品规格骨架线 -->
        <div class="skeleton-line goods-skeleton__spec"></div>
        <!-- 底部信息骨架区域 -->
        <div class="goods-skeleton__bottom">
          <!-- 商品价格骨架线 -->
          <div class="skeleton-line goods-skeleton__price"></div>
          <!-- 商品数量骨架线 -->
          <div class="skeleton-line goods-skeleton__quantity"></div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
// ==================== 商品列表骨架屏组件 ====================
// 纯展示组件，无需任何逻辑处理
</script>

<style scoped lang="less">
@keyframes skeleton-loading {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

.skeleton-line {
  background: linear-gradient(90deg, #f2f2f2 25%, #e6e6e6 50%, #f2f2f2 75%);
  background-size: 200px 100%;
  animation: skeleton-loading 1.5s infinite;
  border-radius: 4px;
}

.goods-skeleton {
  background-color: #FFFFFF;
  border-radius: 10px;
  padding: 12px;

  &__item {
    display: flex;
    padding: 12px 0;
    border-bottom: 1px solid #E2E8EE;

    &:last-child {
      border-bottom: none;
    }
  }

  &__image {
    width: 65px;
    height: 65px;
    background: linear-gradient(90deg, #f2f2f2 25%, #e6e6e6 50%, #f2f2f2 75%);
    background-size: 200px 100%;
    animation: skeleton-loading 1.5s infinite;
    border-radius: 6px;
    margin-right: 12px;
    flex-shrink: 0;
  }

  &__info {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
  }

  &__name {
    height: 18px;
    width: 80%;
    margin-bottom: 8px;
  }

  &__spec {
    height: 14px;
    width: 60%;
    margin-bottom: 12px;
  }

  &__bottom {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  &__price {
    height: 16px;
    width: 80px;
  }

  &__quantity {
    height: 14px;
    width: 40px;
  }
}
</style>
