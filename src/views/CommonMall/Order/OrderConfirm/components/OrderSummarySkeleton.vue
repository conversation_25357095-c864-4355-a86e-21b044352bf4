<!--
/**
 * 订单汇总骨架屏组件
 *
 * 主要功能：
 * 1. 在订单汇总数据加载时显示骨架屏效果，提升用户体验
 * 2. 模拟订单汇总信息的布局结构，包括标签和值的对应关系
 * 3. 提供流畅的加载动画效果，减少用户等待时的焦虑感
 * 4. 与真实订单汇总保持一致的尺寸和布局，确保无缝切换
 *
 * 技术特点：
 * - 使用CSS动画实现流畅的骨架屏效果
 * - 采用渐变背景模拟内容加载状态
 * - 响应式设计，适配不同屏幕尺寸
 * - 无需JavaScript逻辑，纯CSS实现
 *
 * 使用场景：
 * - 订单确认页面汇总信息加载时的占位显示
 * - 任何需要价格汇总加载状态的页面
 */
-->

<template>
  <!-- 订单汇总骨架屏卡片容器 -->
  <WoCard>
    <!-- 汇总信息骨架主容器 -->
    <div class="summary-skeleton">
      <!-- 汇总信息行骨架 -->
      <!-- 循环生成4行骨架，模拟商品总价、运费、活动、实付款等信息 -->
      <div class="summary-skeleton__row" v-for="n in 4" :key="n">
        <!-- 标签骨架线 -->
        <div class="skeleton-line summary-skeleton__label"></div>
        <!-- 值骨架线 -->
        <div class="skeleton-line summary-skeleton__value"></div>
      </div>
    </div>
  </WoCard>
</template>

<script setup>
import WoCard from '@components/WoElementCom/WoCard.vue'

// ==================== 订单汇总骨架屏组件 ====================
// 纯展示组件，无需任何逻辑处理
</script>

<style scoped lang="less">
@keyframes skeleton-loading {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

.skeleton-line {
  background: linear-gradient(90deg, #f2f2f2 25%, #e6e6e6 50%, #f2f2f2 75%);
  background-size: 200px 100%;
  animation: skeleton-loading 1.5s infinite;
  border-radius: 4px;
}

.summary-skeleton {
  &__row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 0;
    border-bottom: 1px solid #E2E8EE;

    &:last-child {
      border-bottom: none;
    }
  }

  &__label {
    height: 16px;
    width: 80px;
  }

  &__value {
    height: 16px;
    width: 100px;
  }
}
</style>
