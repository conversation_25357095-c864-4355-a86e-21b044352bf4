<!--
/**
 * 地址选择卡片组件
 *
 * 主要功能：
 * 1. 展示当前选中的收货地址信息，包括省市区、详细地址、收货人姓名和电话
 * 2. 支持地址选择功能，点击卡片触发地址选择弹窗
 * 3. 提供地址占位符状态，当未选择地址时显示提示信息
 * 4. 集成加载状态处理，在地址数据加载时显示骨架屏
 * 5. 响应式设计，适配不同屏幕尺寸的显示效果
 * 6. 支持地址信息完整性检查，确保必要信息齐全
 *
 * 技术特点：
 * - 使用条件渲染实现不同状态的界面切换
 * - 采用语义化HTML结构提升可访问性
 * - 集成图片懒加载优化性能
 * - 使用toRefs保持props响应性
 *
 * 使用场景：
 * - 订单确认页面的地址选择区域
 * - 需要展示和选择收货地址的业务场景
 */
-->

<template>
  <!-- 地址选择卡片主容器 -->
  <section class="address-selection">
    <!-- 地址选择卡片，点击触发地址选择事件 -->
    <div class="address-selection__card" @click="handleSelect">
      <!-- 地址信息完整时的显示内容 -->
      <!-- 展示省市区、详细地址、收货人姓名和电话 -->
      <div v-if="isComplete" class="address-selection__content">
        <!-- 省市区信息显示 -->
        <div class="address-selection__region">{{ address.region }}</div>
        <!-- 详细地址信息显示 -->
        <div class="address-selection__detail">{{ address.detailAddress }}</div>
        <!-- 收货人联系信息显示 -->
        <div class="address-selection__contact">
          <!-- 收货人姓名 -->
          <span class="address-selection__name">{{ address.name }}</span>
          <!-- 收货人电话 -->
          <span class="address-selection__phone">{{ address.phone }}</span>
        </div>
      </div>
      <!-- 地址信息不完整时的占位符内容 -->
      <!-- 显示地址选择提示和引导信息 -->
      <div v-else class="address-selection__placeholder">
        <!-- 地址图标容器 -->
        <div class="address-selection__icon">
          <!-- 地址位置图标，使用懒加载优化性能 -->
          <img src="../../../../../static/images/location.png" alt="地址" class="address-selection__location-icon" loading="lazy" />
        </div>
        <!-- 占位符文字内容 -->
        <div class="address-selection__placeholder-content">
          <!-- 主要提示文字 -->
          <div class="address-selection__title">请选择收货地址</div>
          <!-- 辅助说明文字 -->
          <div class="address-selection__subtitle">选择收货地址后才能下单</div>
        </div>
      </div>
      <!-- 右侧箭头图标，表示可点击进入选择 -->
      <img src="../../../../../static/images/arrow-right-gray.png" alt="选择地址" class="address-selection__arrow" loading="lazy" />
    </div>
  </section>
</template>

<script setup>
import { toRefs } from 'vue'

// ==================== 组件属性定义 ====================
// 定义组件接收的props参数
const props = defineProps({
  // 加载状态标识，控制是否显示加载效果
  loading: {
    type: Boolean,
    default: false
  },
  // 地址信息对象，包含收货地址的详细信息
  address: {
    type: Object,
    default: () => ({})
  },
  // 地址信息完整性标识，判断地址信息是否齐全
  isComplete: {
    type: Boolean,
    default: false
  }
})

// 使用toRefs解构props，保持响应性
const { loading, address, isComplete } = toRefs(props)

// 定义组件向父组件发射的事件
const emit = defineEmits(['select'])

// ==================== 事件处理函数 ====================
// 地址选择处理函数
// 当用户点击地址卡片时触发，向父组件发射select事件
const handleSelect = () => {
  emit('select')
}
</script>

<style scoped lang="less">
.address-selection {
  background-color: #FFFFFF;
  margin-bottom: 8px;
  border-radius: 10px;

  &__card {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 15px 13px;
    cursor: pointer;
    transition: background-color 0.2s ease;

    &:active {
      background-color: rgba(0, 0, 0, 0.02);
    }
  }

  &__content {
    flex: 1;
  }

  &__region {
    font-size: 13px;
    color: #718096;
    line-height: 1.5;
    margin-bottom: 2px;
  }

  &__detail {
    font-size: 16px;
    color: #171E24;
    font-weight: 600;
    line-height: 1.5;
    margin-bottom: 8px;
    word-break: break-word;
    overflow-wrap: break-word;
  }

  &__contact {
    display: flex;
    align-items: center;
    font-size: 13px;
    color: #718096;
    line-height: 1.5;
  }

  &__name {
    margin-right: 12px;
  }

  &__placeholder {
    flex: 1;
    display: flex;
    align-items: center;

    &-content {
      flex: 1;
    }
  }

  &__icon {
    margin-right: 10px;
    flex-shrink: 0;
  }

  &__location-icon {
    width: 14px;
    height: 15px;
    display: block;
  }

  &__title {
    font-size: 16px;
    color: #4A5568;
    font-weight: 500;
    margin-bottom: 2px;
    line-height: 1.4;
    word-break: break-word;
    overflow-wrap: break-word;
  }

  &__subtitle {
    font-size: 13px;
    color: #718096;
    line-height: 1.4;
  }

  &__arrow {
    margin-left: 10px;
    width: 6px;
    height: 12px;
    flex-shrink: 0;
    opacity: 0.6;
  }
}
</style>
