<!--
/**
 * 订单确认商品列表布局组件
 *
 * 主要功能：
 * 1. 展示订单确认页面的商品列表，支持分组显示
 * 2. 根据业务类型（政企/普通商城）提供不同的展示样式
 * 3. 支持商品分组功能，政企业务可按企业分组展示商品
 * 4. 集成商品卡片组件，统一商品信息展示格式
 * 5. 支持自定义图片尺寸和最小高度，适配不同场景需求
 * 6. 提供操作按钮插槽，支持扩展商品操作功能
 *
 * 技术特点：
 * - 使用计算属性实现商品分组逻辑
 * - 支持条件渲染，根据业务类型显示不同内容
 * - 采用插槽机制提供灵活的扩展能力
 * - 响应式设计，适配不同屏幕尺寸
 *
 * 使用场景：
 * - 订单确认页面的商品列表展示
 * - 需要分组显示商品的业务场景
 * - 政企采购订单的商品展示
 */
-->

<template>
  <!-- 商品列表主容器 -->
  <div class="goods-section">
    <!-- 按分组渲染商品列表 -->
    <!-- 遍历分组后的商品数据，为每个分组创建独立的展示区域 -->
    <div v-for="group in groupedGoodsList" :key="group.groupName" class="goods-group"
      :class="{ 'goods-group--card': isZQBiz && group.groupName && group.groupName !== '默认分组' }">
      <!-- 分组标题区域 -->
      <!-- 仅在政企业务且有有效分组名称时显示 -->
      <div v-if="shouldShowGroupName(group)" class="group-header">
        <!-- 企业图标 -->
        <img src="../../../../../assets/images/enterprise_icon.png" alt="企业图标" class="group-header__icon">
        <!-- 分组名称 -->
        <h3 class="group-header__title">{{ group.groupName }}</h3>
      </div>
      <!-- 商品列表卡片容器 -->
      <WoCard>
        <div class="goods-list">
          <!-- 渲染分组内的商品项 -->
          <!-- 使用OrderGoodsCard组件统一展示商品信息 -->
          <OrderGoodsCard v-for="item in group.items" :key="item.id" :item="item" :image-size="imageSize"
            :min-height="minHeight" :show-actions="showActions">
            <!-- 操作按钮插槽，允许父组件自定义商品操作 -->
            <template #actions="slotProps">
              <slot name="actions" :item="slotProps.item"></slot>
            </template>
          </OrderGoodsCard>
        </div>
      </WoCard>
    </div>
  </div>
</template>

<script setup>
import { computed, toRefs } from 'vue'
import WoCard from '@components/WoElementCom/WoCard.vue'
import OrderGoodsCard from '@components/GoodsListCommon/OrderGoodsCard.vue'
import { getBizCode } from '@utils/curEnv.js'

// ==================== 组件属性定义 ====================
// 定义组件接收的props参数
const props = defineProps({
  // 商品列表数据，包含所有需要展示的商品信息
  goodsList: {
    type: Array,
    required: true,
    default: () => []
  },
  // 商品图片尺寸，用于控制商品图片的显示大小
  imageSize: {
    type: Number,
    default: 90
  },
  // 商品卡片最小高度，确保布局一致性
  minHeight: {
    type: Number,
    default: 135
  },
  // 是否显示操作按钮，控制商品卡片是否显示操作区域
  showActions: {
    type: Boolean,
    default: false
  }
})

// 使用toRefs解构props，保持响应性
const { goodsList, imageSize, minHeight, showActions } = toRefs(props)

// ==================== 业务逻辑计算属性 ====================
// 判断是否为政企业务，仅政企商城支持分组显示
const isZQBiz = computed(() => getBizCode() === 'zq')

// 判断是否应该显示分组名称
// 根据业务类型和分组名称决定是否显示分组标题
const shouldShowGroupName = (group) => {
  if (!isZQBiz.value) {
    return false
  }
  // 政企商城显示有效的分组名称（非默认分组）
  return group.groupName && group.groupName !== '默认分组'
}

// 计算分组后的商品列表
// 将商品按照分组名称进行分类，便于分组展示
const groupedGoodsList = computed(() => {
  if (!goodsList.value || goodsList.value.length === 0) {
    return []
  }

  // 按groupName分组商品
  const groups = {}

  goodsList.value.forEach(item => {
    const groupName = item.rawData?.groupName || '默认分组'

    if (!groups[groupName]) {
      groups[groupName] = {
        groupName,
        items: []
      }
    }

    groups[groupName].items.push(item)
  })

  // 转换为数组并排序，将默认分组排在最后
  return Object.values(groups).sort((a, b) => {
    // 将"默认分组"排在最后
    if (a.groupName === '默认分组') return 1
    if (b.groupName === '默认分组') return -1
    return a.groupName.localeCompare(b.groupName)
  })
})
</script>

<style scoped lang="less">
.goods-section {
  width: 100%;

  .goods-group {
    margin-bottom: 10px;
    contain: layout style;

    &:not(:last-child) {
      margin-bottom: 12px;
    }

    // 政企商城分组卡片化样式
    &--card {
      background: #FFFFFF;
      border-radius: 10px;
      padding: 8px;
      margin-bottom: 12px;
      box-shadow: 0 1px 2px rgba(0, 0, 0, 0.03);
      border: 1px solid #f0f0f0;
    }

    .group-header {
      display: flex;
      align-items: center;
      margin: 6px 0 8px;
      padding: 0 0 6px 10px;
      border-bottom: 1px dashed #ededed;

      &__icon {
        width: 16px;
        height: 16px;
        margin-right: 6px;
        flex-shrink: 0;
      }

      &__title {
        font-size: 14px;
        font-weight: 600;
        color: #171E24;
        margin: 0;
      }
    }

    .goods-list {
      width: 100%;
    }
  }
}
</style>
