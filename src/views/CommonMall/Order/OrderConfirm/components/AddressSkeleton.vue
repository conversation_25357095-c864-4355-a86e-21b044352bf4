<!--
/**
 * 地址选择卡片骨架屏组件
 *
 * 主要功能：
 * 1. 在地址数据加载时显示骨架屏效果，提升用户体验
 * 2. 模拟地址卡片的布局结构，包括省市区、详细地址、联系人信息等区域
 * 3. 提供流畅的加载动画效果，减少用户等待时的焦虑感
 * 4. 与真实地址卡片保持一致的尺寸和布局，确保无缝切换
 *
 * 技术特点：
 * - 使用CSS动画实现流畅的骨架屏效果
 * - 采用渐变背景模拟内容加载状态
 * - 响应式设计，适配不同屏幕尺寸
 * - 无需JavaScript逻辑，纯CSS实现
 *
 * 使用场景：
 * - 订单确认页面地址信息加载时的占位显示
 * - 任何需要地址信息加载状态的页面
 */
-->

<template>
  <!-- 地址骨架屏主容器 -->
  <div class="address-skeleton">
    <!-- 地址内容区域骨架 -->
    <div class="address-skeleton__content">
      <!-- 省市区信息骨架线 -->
      <div class="skeleton-line address-skeleton__region"></div>
      <!-- 详细地址信息骨架线 -->
      <div class="skeleton-line address-skeleton__detail"></div>
      <!-- 联系人信息骨架区域 -->
      <div class="address-skeleton__contact">
        <!-- 收货人姓名骨架线 -->
        <div class="skeleton-line address-skeleton__name"></div>
        <!-- 收货人电话骨架线 -->
        <div class="skeleton-line address-skeleton__phone"></div>
      </div>
    </div>
    <!-- 右侧箭头图标骨架线 -->
    <div class="skeleton-line address-skeleton__arrow"></div>
  </div>
</template>

<script setup>
// ==================== 地址骨架屏组件 ====================
// 纯展示组件，无需任何逻辑处理
</script>

<style scoped lang="less">
@keyframes skeleton-loading {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

.skeleton-line {
  background: linear-gradient(90deg, #f2f2f2 25%, #e6e6e6 50%, #f2f2f2 75%);
  background-size: 200px 100%;
  animation: skeleton-loading 1.5s infinite;
  border-radius: 4px;
}

.address-skeleton {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 15px 13px;

  &__content {
    flex: 1;
  }

  &__region {
    height: 16px;
    width: 120px;
    margin-bottom: 6px;
  }

  &__detail {
    height: 20px;
    width: 200px;
    margin-bottom: 12px;
  }

  &__contact {
    display: flex;
    gap: 12px;
  }

  &__name {
    height: 16px;
    width: 60px;
  }

  &__phone {
    height: 16px;
    width: 100px;
  }

  &__arrow {
    width: 6px;
    height: 12px;
    margin-left: 10px;
    border-radius: 2px;
  }
}
</style>
