<!--
/**
 * 订单确认页面组件
 *
 * 主要功能：
 * 1. 展示订单确认信息，包括收货地址、商品列表、价格汇总等关键信息
 * 2. 支持收货地址选择和编辑，提供地址管理功能
 * 3. 实现订单价格计算，包括商品总价、运费、活动优惠等
 * 4. 支持慰问活动额度查询和使用，提供额度抵扣功能
 * 5. 区分普通商城和政企业务，提供不同的订单确认流程
 * 6. 集成京东商品支持，处理运费分摊逻辑
 * 7. 实现区域限制检查，确保商品可配送到指定地址
 * 8. 支持立即购买和购物车下单两种订单来源
 *
 * 技术特点：
 * - 使用骨架屏提升加载体验
 * - 采用异步组件优化首屏加载性能
 * - 集成Pinia状态管理，同步用户和购物车数据
 * - 使用防抖和节流优化用户交互
 * - 支持多种业务场景的订单处理逻辑
 *
 * 使用场景：
 * - 用户从商品详情页立即购买时的订单确认
 * - 用户从购物车结算时的订单确认
 * - 政企用户的采购订单确认
 */
-->

<template>
  <!-- 订单确认页面主容器 -->
  <main class="order-confirm">
    <!-- 收货地址选择区域 -->
    <!-- 显示当前选中的收货地址信息，支持地址选择和编辑 -->
    <section class="order-confirm__address">
      <!-- 地址加载骨架屏，在地址数据加载时显示 -->
      <AddressSkeleton v-if="addressLoading" />
      <!-- 地址选择卡片组件 -->
      <!-- 展示当前地址信息，支持点击选择新地址 -->
      <AddressSelectionCard
        :loading="addressLoading"
        :address="selectedAddress"
        :is-complete="isAddressComplete"
        @select="handleSelectAddress" />
    </section>

    <!-- 商品列表展示区域 -->
    <!-- 显示订单中的所有商品信息，包括图片、名称、价格、数量等 -->
    <section class="order-confirm__goods">
      <!-- 商品列表加载骨架屏，在商品数据加载时显示 -->
      <GoodsListSkeleton v-if="goodsLoading" />
      <!-- 订单确认商品列表布局组件 -->
      <!-- 以列表形式展示商品信息，不显示操作按钮 -->
      <OrderConfirmGoodsListLayout
        v-else-if="goodsList.length > 0"
        :goods-list="goodsList"
        :image-size="65"
        :min-height="65"
        :show-actions="false" />
    </section>

    <!-- 普通商城订单汇总信息区域 -->
    <!-- 仅在非政企业务时显示，包含价格明细和活动信息 -->
    <section v-if="!isZqBiz" class="order-confirm__summary">
      <!-- 订单汇总加载骨架屏，在汇总数据加载时显示 -->
      <OrderSummarySkeleton v-if="summaryLoading" />
      <!-- 订单汇总信息卡片 -->
      <WoCard v-else>
        <!-- 商品总价行，显示所有商品的总价格 -->
        <InfoRow label="商品总价">
          <template #value>
            <PriceDisplay :price="orderSummary.goodsTotal" size="small" />
          </template>
        </InfoRow>
        <!-- 运费信息行，显示运费或免运费提示 -->
        <InfoRow label="运费" :value="orderSummary.shippingText" />
        <!-- 慰问活动信息行，支持点击查看活动详情 -->
        <!-- 根据是否有活动显示不同的样式 -->
        <InfoRow
          label="慰问活动"
          :value="orderSummary.activityText"
          :value-class="orderSummary.activityText ? 'order-confirm__activity-text' : 'order-confirm__activity-text order-confirm__activity-text--empty'"
          :show-arrow="true"
          @click="handleShowQuotaPopup" />
        <!-- 实付款行，显示用户最终需要支付的金额 -->
        <InfoRow label="实付款">
          <template #value>
            <PriceDisplay :price="orderSummary.actualPayment" size="small" color="orange" />
          </template>
        </InfoRow>
      </WoCard>
    </section>

    <!-- 政企业务订单信息区域 -->
    <!-- 仅在政企业务时显示，展示企业相关信息 -->
    <section v-if="isZqBiz" class="order-confirm__summary">
      <!-- 订单汇总加载骨架屏 -->
      <OrderSummarySkeleton v-if="summaryLoading" />
      <!-- 企业信息卡片 -->
      <WoCard title="企业信息" v-else>
        <!-- 企业名称信息行 -->
        <InfoRow label="企业名称" :value="enterpriseName" />
        <!-- 采购数量信息行，显示总的商品采购数量 -->
        <InfoRow label="采购数量" :value="totalGoodsNum" />
      </WoCard>
    </section>

    <!-- 底部操作栏占位符，为固定底部操作栏预留空间 -->
    <WoActionBarPlaceholder />

    <!-- 底部固定操作栏 -->
    <WoActionBar>
      <!-- 订单提交区域，包含价格信息和提交按钮 -->
      <div class="order-confirm__submit">
        <!-- 普通商城的价格信息展示 -->
        <!-- 显示实付金额、额度抵扣、自付金额等详细信息 -->
        <div v-if="!isZqBiz" class="order-confirm__submit-info">
          <!-- 实付金额显示 -->
          <span class="order-confirm__submit-price">
            <PriceDisplay :price="summaryLoading ? '' : orderSummary.actualPayment" size="large" color="orange" />
          </span>
          <!-- 价格明细信息 -->
          <div class="order-confirm__submit-details">
            <!-- 额度抵扣金额显示 -->
            <span v-if="summaryLoading">额度抵扣 --.-</span>
            <span v-else-if="orderSummary.savingsText">{{ orderSummary.savingsText }}</span>
            <!-- 自付金额显示 -->
            <span v-if="summaryLoading">自付金额 --.-</span>
            <span v-else-if="orderSummary.refundText">{{ orderSummary.refundText }}</span>
          </div>
        </div>
        <!-- 订单提交按钮 -->
        <!-- 根据业务类型显示不同的按钮文字 -->
        <WoButton
          type="gradient"
          size="special"
          :block="isZqBiz"
          @click="handleSubmitOrder">
          {{ isZqBiz ? '通知发货' : '提交订单' }}
        </WoButton>
      </div>
    </WoActionBar>

    <!-- 地址快速选择弹窗 -->
    <!-- 支持地址选择、新建、编辑等操作 -->
    <AddressQuickSelectionPopup
      v-model:visible="showAddressPopup"
      @select="handleAddressSelected"
      @create="handleCreateAddress"
      @edit="handleEditAddress" />

    <!-- 慰问活动额度信息弹窗 -->
    <!-- 显示可用和不可用的活动额度信息 -->
    <QuotaInfoPopup
      v-model="showQuotaPopup"
      :available-activity-quota="availableActivityQuota"
      :un-available-activity-quota="unAvailableActivityQuota"
      @change="handleQuotaChange" />
  </main>
</template>

<script setup>
import { ref, computed, onMounted, defineAsyncComponent, nextTick } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { showToast, showLoadingToast, closeToast } from 'vant'
import { useUserStore } from '@store/modules/user.js'
import { useNewCartStore } from '@store/modules/newCart.js'
import { get, isEmpty, debounce, throttle, compact } from 'lodash-es'
import Decimal from 'decimal.js'

import PriceDisplay from '@components/Common/PriceDisplay.vue'
import WoButton from '@components/WoElementCom/WoButton/WoButton.vue'
import OrderConfirmGoodsListLayout from '@views/CommonMall/Order/OrderConfirm/components/OrderConfirmGoodsListLayout.vue'
import WoCard from '@components/WoElementCom/WoCard.vue'
import InfoRow from '@components/Common/InfoRow.vue'
import WoActionBar from '@components/WoElementCom/WoActionBar.vue'
import WoActionBarPlaceholder from '@components/WoElementCom/WoActionBarPlaceholder.vue'
import AddressSelectionCard from './components/AddressSelectionCard.vue'
import GoodsListSkeleton from './components/GoodsListSkeleton.vue'
import OrderSummarySkeleton from './components/OrderSummarySkeleton.vue'
import AddressSkeleton from '@views/CommonMall/Order/OrderConfirm/components/AddressSkeleton.vue'

import { getBuyNowGoods, getSelectedGoods, getFuLiHuiID, jdAddressCheck } from '@api/index.js'
import { submitOrder, queryAmount, notApplicableQuotaWish } from '@api/interface/order.js'
import { getLimitAreaList } from '@api/interface/goods.js'
import { getBizCode } from '@utils/curEnv.js'
import { buyProductNow, buyProductNowSession, buyProductCart, buyProductCartSession, curDeveloperId, curChannelBiz } from '@utils/storage.js'
import { formSubmit } from 'commonkit'
import { fenToYuan } from '@utils/amount.js'
import { useAlert } from '@/composables/index.js'
import { getEnterpriseManagerInfo, queryZqInfo } from '@utils/zqInfo.js'
import {JD_GOODS_CODE} from "@utils/types.js";

// ==================== 异步组件定义 ====================
// 地址快速选择弹窗组件，使用异步加载优化首屏性能
const AddressQuickSelectionPopup = defineAsyncComponent(() =>
  import('@components/Common/Address/AddressQuickSelectionPopup.vue')
)
// 慰问活动额度信息弹窗组件，使用异步加载优化首屏性能
const QuotaInfoPopup = defineAsyncComponent(() =>
  import('@components/Common/QuotaInfoPopup/QuotaInfoPopup.vue')
)

// ==================== 常量定义 ====================
// 订单类型枚举，用于区分不同的下单来源
const ORDER_TYPE = {
  BUY_NOW: '1',    // 立即购买订单
  CART: '2'        // 购物车订单
}

// ==================== 核心依赖和工具实例 ====================
// 弹窗提示工具实例，用于显示确认对话框
const $alert = useAlert()
// 路由导航实例，用于页面跳转
const router = useRouter()
// 当前路由信息实例，用于获取路由参数
const route = useRoute()
// 用户状态管理实例，管理用户信息和地址数据
const userStore = useUserStore()
// 购物车状态管理实例，管理购物车数据
const cartStore = useNewCartStore()

// ==================== 弹窗显示状态管理 ====================
// 地址选择弹窗显示状态，控制地址选择弹窗的显示和隐藏
const showAddressPopup = ref(false)
// 慰问活动额度弹窗显示状态，控制额度信息弹窗的显示和隐藏
const showQuotaPopup = ref(false)

// ==================== 页面加载状态管理 ====================
// 整体页面加载状态，控制页面初始化过程中的加载状态
const loading = ref(false)
// 地址信息加载状态，控制地址数据获取时的加载效果
const addressLoading = ref(true)
// 商品信息加载状态，控制商品数据获取时的加载效果
const goodsLoading = ref(true)
// 订单汇总信息加载状态，控制价格汇总数据获取时的加载效果
const summaryLoading = ref(true)

// ==================== 订单基础信息管理 ====================
// 订单类型，区分立即购买和购物车下单两种订单来源
const orderType = ref('')
// 订单商品列表数据，存储当前订单中的所有商品信息
const orderCartList = ref([])
// 购物车商品总价格，以元为单位的商品总价
const cartPriceTotal = ref(0)
// 总运费金额，订单的运费总计
const totalFreight = ref(0)
// 是否包含京东商品标识，用于判断是否需要特殊处理京东商品逻辑
const isJD = ref(false)
// 订单是否可提交标识，控制提交按钮的可用状态
const isSubmit = ref(false)
// 是否移除赠品标识，用于处理赠品库存不足的情况
const isRemoveGifts = ref(false)
// 订单详细信息对象，包含提交订单所需的完整信息
const orderVoInfo = ref(null)
// 开发者ID，用于福利汇业务的开发者标识
const developerId = ref('')
// 是否存在区域限制标识，标记商品是否在当前地区可售
const isRegionalPurchase = ref(false)

// ==================== 支付相关状态管理 ====================
// 对话框显示状态，用于扶贫业务的特殊支付流程
const dialogShow = ref(false)
// 微信支付相关信息，存储支付所需的关键参数
const wapay = ref({
  encryptContent: '',  // 支付加密内容
  wapURL: '',         // 支付跳转URL
  bizOrderId: ''      // 业务订单ID
})

// ==================== 慰问活动额度管理 ====================
// 可用的活动额度信息，存储用户可使用的活动额度数据
const availableActivityQuota = ref({})
// 不可用的活动额度信息，存储用户不可使用的活动额度数据
const unAvailableActivityQuota = ref({})
// 当前活动名称，显示当前适用的活动名称
const activeName = ref('')
// 额度抵扣金额，活动额度可抵扣的金额
const quotaPayment = ref(0)
// 自付金额，用户需要自己支付的金额
const selfPayment = ref(0)

// ==================== 业务类型和权限计算属性 ====================
// 判断是否为政企业务，用于区分不同的业务流程
const isZqBiz = computed(() => getBizCode() === 'zq')

// 获取企业名称，用于政企业务页面显示企业信息
const enterpriseName = computed(() => {
  const enterpriseInfo = getEnterpriseManagerInfo()
  return enterpriseInfo ? enterpriseInfo.ciName : '-'
})

// 计算商品总数量，用于政企业务显示采购数量统计
const totalGoodsNum = computed(() => {
  return goodsList.value.reduce((total, goods) => {
    return total + (goods.quantity || 0)
  }, 0)
})

// 获取用户角色类型，用于权限控制和功能限制
const roleType = computed(() => {
  const { roleType: rt = '' } = getEnterpriseManagerInfo() || {}
  return rt
})

// ==================== 地址信息计算属性 ====================
// 格式化当前选中的收货地址信息，转换为页面显示所需的格式
const selectedAddress = computed(() => {
  const addressInfo = userStore.curAddressInfo
  const { recName = '', recPhone = '', provinceName = '', cityName = '', countyName = '', addrDetail = '' } = addressInfo

  return {
    name: recName,                                                    // 收货人姓名
    phone: recPhone,                                                  // 收货人电话
    region: compact([provinceName, cityName, countyName]).join(''),   // 省市区组合字符串
    detailAddress: addrDetail                                         // 详细地址
  }
})

// 检查地址信息是否完整，用于判断是否可以提交订单
const isAddressComplete = computed(() => {
  const addressInfo = userStore.curAddressInfo
  const requiredFields = ['recName', 'recPhone', 'provinceName', 'cityName', 'countyName', 'addrDetail']
  return requiredFields.every(field => !isEmpty(get(addressInfo, field)))
})

// ==================== 商品信息计算属性 ====================
// 获取订单中的商品列表，从订单数据结构中提取商品信息
const goodsList = computed(() => {
  return get(orderCartList.value, '[0].goodsList', [])
})

// ==================== 订单汇总计算属性 ====================
// 计算订单汇总信息，包括价格、运费、活动等显示内容
const orderSummary = computed(() => {
  const priceInCents = cartPriceTotal.value * 100

  return {
    goodsTotal: priceInCents,                                                           // 商品总价（分）
    shippingText: isJD.value && totalFreight.value ? '运费已分摊至商品金额' : '免运费',    // 运费显示文字
    activityText: activeName.value || '暂无活动',                                       // 活动显示文字
    actualPayment: priceInCents,                                                        // 实付金额（分）
    savingsText: quotaPayment.value >= 0 ? `额度抵扣 ¥${quotaPayment.value}` : '',      // 额度抵扣显示文字
    refundText: selfPayment.value >= 0 ? `自付金额 ¥${selfPayment.value}` : ''          // 自付金额显示文字
  }
})

// ==================== 订单数据处理函数 ====================
// 处理立即购买和购物车数据，转换为统一的订单格式
// 将API返回的商品数据转换为页面展示所需的标准格式
const processBuyNowData = (data) => {
  // 获取有效和无效商品列表，合并为完整的商品数据
  const validGoodsList = get(data, 'validGoodsList', [])
  const invalidGoodsList = get(data, 'invalidGoodsList', [])
  const allGoodsList = [...validGoodsList, ...invalidGoodsList]

  // 转换商品数据格式，统一数据结构以适配页面组件
  const transformedGoodsList = allGoodsList.map(item => {
    const goods = get(item, 'goods', {})
    const sku = get(goods, 'skuList[0]', {})
    return {
      id: `${get(sku, 'goodsId', '')}_${get(sku, 'skuId', '')}`,    // 商品唯一标识符
      name: get(sku, 'name', ''),                                   // 商品名称
      price: get(item, 'nowPrice', 0),                              // 商品当前价格
      quantity: get(item, 'skuNum', 0),                             // 商品购买数量
      detailImageUrl: get(sku, 'listImageUrl', ''),                 // 商品展示图片URL
      skuNeedGift: get(item, 'skuNeedGift'),                        // 是否需要赠品标识
      rawData: item,                                                // 保留原始数据用于后续处理
      skuNumInfoList: [{                                            // SKU详细信息列表
        sku: {
          ...sku,
          goodsId: get(sku, 'goodsId', ''),
          skuId: get(sku, 'skuId', ''),
          name: get(sku, 'name', ''),
          detailImageUrl: get(sku, 'listImageUrl', ''),
          param: get(sku, 'param', ''),
          param1: get(sku, 'param1', ''),
          param2: get(sku, 'param2', ''),
          param3: get(sku, 'param3', ''),
          param4: get(sku, 'param4', ''),
          price: get(item, 'nowPrice', 0)
        },
        skuNum: get(item, 'skuNum', 0)
      }]
    }
  })

  // 更新订单商品列表数据
  orderCartList.value = [{ goodsList: transformedGoodsList }]
  // 更新购物车总价，从分转换为元进行显示
  cartPriceTotal.value = parseFloat((get(data, 'cartPriceTotal', 0) / 100).toFixed(2))
  // 更新总运费金额
  totalFreight.value = get(data, 'totalFreight', 0)

  // 检查是否包含京东商品，用于特殊逻辑处理
  checkJDGoods()
}

// 检查订单中是否包含京东商品
// 京东商品需要特殊的运费处理逻辑
const checkJDGoods = () => {
  isJD.value = orderCartList.value.some(goodGroup =>
    get(goodGroup, 'goodsList', []).some(goodsItem =>
      get(goodsItem, 'goods.skuList', []).some(sku =>
        get(sku, 'supplierCode', '').includes(JD_GOODS_CODE)
      )
    )
  )
}

// ==================== 区域限制检查函数 ====================
// 检查商品在当前收货地址是否可售
// 根据用户选择的收货地址检查商品的销售区域限制
const checkRegionalRestrictions = async () => {
  const curAddrInfo = userStore.curAddressInfo

  // 提取所有商品ID列表，用于区域限制查询
  const goodsIdList = compact(
    orderCartList.value.flatMap(supplier =>
      get(supplier, 'goodsList', []).map(skuInfo => {
        skuInfo.nosale = false  // 初始化为可售状态
        return get(skuInfo, 'rawData.cartGoodsId')
      })
    )
  )

  // 构建地址信息对象，包含省市区县等层级信息
  const addressFields = ['provinceId', 'cityId', 'countyId', 'townId']
  const areaInfo = addressFields.reduce((acc, field) => {
    const value = get(curAddrInfo, field)
    if (value) acc[field] = value
    return acc
  }, {})

  // 如果地址信息为空则跳过检查
  if (isEmpty(areaInfo)) return

  // 构建API请求参数
  const params = {
    area: JSON.stringify(areaInfo),
    goodsIdList
  }

  try {
    showLoadingToast()
    const [err, json] = await getLimitAreaList(params)
    closeToast()

    if (!err) {
      const hasRestrictions = !isEmpty(json)

      // 更新商品的可售状态，标记受限制的商品
      orderCartList.value.forEach(supplier => {
        get(supplier, 'goodsList', []).forEach(skuInfo => {
          const cartGoodsId = get(skuInfo, 'rawData.cartGoodsId')
          skuInfo.nosale = hasRestrictions && json.includes(cartGoodsId)
        })
      })

      // 更新区域限制标识
      isRegionalPurchase.value = hasRestrictions

      // 如果包含京东商品且有运费，显示运费分摊提示
      if (isJD.value && totalFreight.value) {
        showToast('运费已分摊至商品金额')
      }
    } else {
      showToast(err.msg)
    }
  } catch (error) {
    closeToast()
    console.warn('检查区域限制失败:', error)
  }
}

// ==================== 立即购买订单处理 ====================
// 处理立即购买订单的数据获取和初始化
// 从路由参数获取商品信息，调用API获取订单数据
const handleBuyNowOrder = async () => {
  const { goodsId, skuId, goodsNum } = route.query
  const addressInfo = JSON.stringify(userStore.curAddressInfo)

  // 设置订单类型为立即购买
  orderType.value = ORDER_TYPE.BUY_NOW

  // 优先使用缓存数据，避免重复请求
  const cachedData = buyProductNow.get() || buyProductNowSession.get()

  if (cachedData) {
    processBuyNowData(cachedData.data)
    if (cachedData.code !== '0000') {
      showToast(cachedData.msg)
      isSubmit.value = false
      return false
    }
    isSubmit.value = true
    return true
  }

  try {
    showLoadingToast()

    // 调用立即购买API获取订单数据
    const [res, data] = await getBuyNowGoods({
      goodsId,
      skuId,
      goodsNum,
      addressInfo,
      bizCode: getBizCode('ORDER')
    })

    closeToast()

    if (res.code !== '0000') {
      showToast(res.msg)
      isSubmit.value = false
      return false
    }

    // 处理返回的订单数据
    processBuyNowData(data)
    isSubmit.value = true
    return true

  } catch (error) {
    closeToast()
    showToast('加载订单信息失败')
    isSubmit.value = false
    return false
  }
}

// ==================== 购物车订单处理 ====================
// 处理购物车订单的数据获取和初始化
// 从缓存获取购物车选中商品，调用API获取订单数据
const handleCartOrder = async () => {
  const addressInfo = JSON.stringify(userStore.curAddressInfo)

  // 设置订单类型为购物车订单
  orderType.value = ORDER_TYPE.CART

  // 从缓存获取购物车选中的商品列表
  const buyGoodsList = buyProductCart.get() || buyProductCartSession.get()

  if (!buyGoodsList) {
    console.warn('购物车下单缓存数据为空')
    showToast('购物车数据异常')
    isSubmit.value = false
    return false
  }

  try {
    showLoadingToast()

    // 调用购物车结算API获取订单数据
    const [res, data] = await getSelectedGoods({
      bizCode: getBizCode('ORDER'),
      addressInfo,
      buyGoodsList: JSON.stringify(buyGoodsList)
    })

    closeToast()

    if (res.code !== '0000') {
      showToast(res.msg)
      isSubmit.value = false
      return false
    }

    // 处理返回的订单数据
    processBuyNowData(data)
    isSubmit.value = true
    return true

  } catch (error) {
    closeToast()
    showToast('加载订单信息失败')
    isSubmit.value = false
    return false
  }
}

// ==================== 数据加载管理函数 ====================
// 加载用户地址数据
// 获取用户的默认收货地址信息
const loadAddressData = async () => {
  addressLoading.value = true
  try {
    await userStore.queryDefaultAddr()
  } catch (error) {
    console.warn('加载地址失败:', error)
    showToast('加载地址失败')
  } finally {
    addressLoading.value = false
  }
}

// 加载商品数据
// 根据订单类型（立即购买或购物车）加载对应的商品信息
const loadGoodsData = async () => {
  goodsLoading.value = true
  try {
    const { goodsId, skuId, goodsNum } = route.query
    const isDirectBuy = goodsId && skuId && goodsNum

    // 根据是否为直接购买选择不同的处理方式
    const success = isDirectBuy ? await handleBuyNowOrder() : await handleCartOrder()

    if (success) {
      // 如果包含京东商品且有运费，显示运费分摊提示
      if (isJD.value && totalFreight.value) {
        showToast('运费已分摊至商品金额')
      }
      // 检查商品的区域销售限制
      await checkRegionalRestrictions()
    }

    return success
  } catch (error) {
    console.warn('加载商品数据失败:', error)
    showToast('加载商品数据失败')
    return false
  } finally {
    goodsLoading.value = false
  }
}

// 加载订单汇总数据
// 获取价格汇总、活动信息等数据（政企业务跳过）
const loadSummaryData = async () => {
  // 政企业务不需要加载汇总数据
  if (isZqBiz.value) {
    summaryLoading.value = false
    return
  }

  summaryLoading.value = true
  try {
    await getPaymentDetails()
  } catch (error) {
    console.warn('加载订单汇总失败:', error)
    showToast('加载订单汇总失败')
  } finally {
    summaryLoading.value = false
  }
}

// 初始化订单数据
// 按顺序加载地址、商品、汇总等数据
const initOrderData = async () => {
  // 防止重复初始化
  if (loading.value) return

  loading.value = true

  try {
    // 按顺序加载各模块数据，使用nextTick确保DOM更新
    await loadAddressData()
    await nextTick()

    const goodsSuccess = await loadGoodsData()
    await nextTick()

    await loadSummaryData()

    if (!goodsSuccess) {
      console.warn('商品数据加载失败，但页面仍可使用')
    }

  } catch (error) {
    console.warn('初始化订单失败:', error)
    showToast('初始化订单失败')
  } finally {
    loading.value = false
  }
}

// ==================== 地址选择事件处理 ====================
// 处理地址选择按钮点击事件
// 使用防抖避免频繁点击
const handleSelectAddress = debounce(() => {
  showAddressPopup.value = true
}, 300)

// 京东地址检查
// 检查当前地址是否符合京东配送要求
const addressCheck = async () => {
  showLoadingToast()
  const [err, json] = await jdAddressCheck()
  closeToast()

  if (err) {
    showToast(err.msg)
    return false
  }

  // 如果地址检查不通过，提示用户修改地址
  if (!json) {
    $alert({
      message: '由于物流配送地址库规则升级，收货地址需要精确到街道，请您重新设置地址，按指引操作及保存',
      confirmButtonText: '修改地址',
      showCancelButton: true,
      cancelButtonText: '取消',
      messageAlign: 'center',
      onConfirmCallback: () => {
        router.push({
          name: 'address-edit',
          query: {
            addrId: userStore.curAddressInfo.addressId,
            isInvalid: '1'
          }
        })
      },
      onCancelCallback: () => { }
    })

    return false
  }

  return true
}

// 地址选择完成后的处理函数
// 重新计算订单信息，更新价格和可售状态
const handleAddressSelected = async () => {
  showToast('地址选择成功')
  cartStore.query()

  // 检查地址是否符合京东配送要求
  const isPassed = await addressCheck()
  if (!isPassed) return

  const { goodsId, skuId, goodsNum } = route.query
  const info = userStore.curAddressInfo

  // 构建地址信息对象
  const addressFields = ['provinceId', 'provinceName', 'cityId', 'cityName', 'countyId', 'countyName', 'townId', 'townName']
  const addressInfo = JSON.stringify(
    addressFields.reduce((acc, field) => {
      acc[field] = get(info, field)
      return acc
    }, {})
  )

  const isDirectBuy = goodsId && skuId && goodsNum

  try {
    showLoadingToast()
    orderType.value = isDirectBuy ? ORDER_TYPE.BUY_NOW : ORDER_TYPE.CART

    let res, json

    // 根据订单类型重新获取订单数据
    if (isDirectBuy) {
      [res, json] = await getBuyNowGoods({
        goodsId,
        skuId,
        goodsNum,
        addressInfo,
        bizCode: getBizCode('ORDER')
      })
    } else {
      const buyGoodsList = buyProductCart.get() || buyProductCartSession.get()
      if (!buyGoodsList) {
        console.error('购物车下单缓存数据为空')
      }

      [res, json] = await getSelectedGoods({
        bizCode: getBizCode('ORDER'),
        addressInfo,
        buyGoodsList: JSON.stringify(buyGoodsList)
      })
    }

    closeToast()

    if (res.code !== '0000') {
      isSubmit.value = false
      showToast(res.msg)
    } else {
      isSubmit.value = true
      processBuyNowData(json)
      // 如果包含京东商品且有运费，显示运费分摊提示
      if (isJD.value && totalFreight.value) {
        showToast('运费已分摊至商品金额')
      }
    }
  } catch (error) {
    closeToast()
    console.warn('重新计算订单失败:', error)
    showToast('重新计算订单失败')
  }

  // 重新检查区域限制
  await checkRegionalRestrictions()
}

// 创建新地址处理函数
// 跳转到地址新建页面
const handleCreateAddress = () => {
  router.push('/addr/add')
}

// 编辑地址处理函数
// 跳转到地址编辑页面
const handleEditAddress = (address) => {
  router.push(`/addr/edit/${address.addressId}`)
}

// ==================== 慰问活动额度处理 ====================
// 显示慰问活动额度弹窗
// 获取并显示用户的活动额度信息
const handleShowQuotaPopup = async (event) => {
  // 政企业务不支持慰问活动
  if (isZqBiz.value) return

  if (event) {
    event.preventDefault()
    event.stopPropagation()
  }

  const availableActivityQuotaList = get(availableActivityQuota.value, 'quotaInfo', [])

  // 如果已有可用额度数据，直接显示弹窗
  if (!isEmpty(availableActivityQuotaList)) {
    showQuotaPopup.value = true
    return
  }

  // 构建查询参数，价格需要转换为分
  const params = {
    bizCode: getBizCode(),
    orderPrice: parseInt(new Decimal(cartPriceTotal.value).mul(new Decimal(100)).toString())
  }

  showQuotaPopup.value = true
  showLoadingToast()

  try {
    const [err, json] = await queryAmount(params)
    closeToast()

    if (!err) {
      availableActivityQuota.value = json
    } else {
      showToast(err.msg)
    }
  } catch (error) {
    closeToast()
    console.warn('获取慰问活动数据失败:', error)
    showToast('获取活动信息失败')
  }
}

// 获取支付详情和活动额度信息
// 计算活动额度抵扣和自付金额
const getPaymentDetails = async (orderPrice = null) => {
  // 政企业务不需要支付详情
  if (isZqBiz.value) return

  const priceToUse = orderPrice || cartPriceTotal.value
  if (priceToUse === 0) return

  showLoadingToast()
  const params = {
    bizCode: getBizCode(),
    orderPrice: parseInt(new Decimal(priceToUse).mul(new Decimal(100)).toString())
  }

  const [err, json] = await queryAmount(params)
  closeToast()

  if (!err) {
    availableActivityQuota.value = json
    const grantAmount = get(json, 'grantAmount', 0)
    const balanceAmount = get(json, 'amount', 0)
    const realPrice = priceToUse * 100

    // 如果有授权额度，优先使用授权额度
    if (grantAmount > 0) {
      const activityName = get(json, 'activityName', '')
      activeName.value = activityName

      if (!activityName) {
        quotaPayment.value = fenToYuan(0)
        selfPayment.value = fenToYuan(realPrice)
        return
      }

      // 计算额度抵扣和自付金额
      if (realPrice > balanceAmount) {
        quotaPayment.value = fenToYuan(balanceAmount)
        selfPayment.value = fenToYuan(realPrice - balanceAmount)
      } else {
        quotaPayment.value = fenToYuan(realPrice)
        selfPayment.value = fenToYuan(0)
      }
    } else {
      // 使用可用活动额度
      const availableActivityQuotaList = get(availableActivityQuota.value, 'quotaInfo', [])
      const activityName = get(availableActivityQuotaList, '[0].activityName', '')
      activeName.value = activityName

      if (!activityName) {
        quotaPayment.value = fenToYuan(0)
        selfPayment.value = fenToYuan(realPrice)
        return
      }

      if (!isEmpty(availableActivityQuotaList)) {
        const { balanceAmount } = availableActivityQuotaList[0]
        // 计算额度抵扣和自付金额
        if (realPrice > balanceAmount) {
          quotaPayment.value = fenToYuan(balanceAmount)
          selfPayment.value = fenToYuan(realPrice - balanceAmount)
        } else {
          quotaPayment.value = fenToYuan(realPrice)
          selfPayment.value = fenToYuan(0)
        }
      }
    }
  } else {
    showToast(err.msg)
  }
}

// 处理额度弹窗标签页切换
// 根据标签页索引获取对应的额度数据
const handleQuotaChange = async (tabIndex) => {
  // 政企业务不支持额度功能
  if (isZqBiz.value) return

  const params = {
    bizCode: getBizCode(),
    orderPrice: parseInt(new Decimal(cartPriceTotal.value).mul(new Decimal(100)).toString())
  }

  const availableActivityQuotaList = get(availableActivityQuota.value, 'quotaInfo', [])
  const unAvailableActivityQuotaList = get(unAvailableActivityQuota.value, 'quotaInfo', [])

  // 如果对应标签页已有数据，则不重复请求
  if (tabIndex === 0 && availableActivityQuotaList && availableActivityQuotaList.length > 0) {
    return
  }

  if (tabIndex === 1 && unAvailableActivityQuotaList && unAvailableActivityQuotaList.length > 0) {
    return
  }

  // 根据标签页索引请求对应数据
  if (tabIndex === 0) {
    // 获取可用额度数据
    showLoadingToast()
    const [err, json] = await queryAmount(params)
    closeToast()
    if (!err) {
      availableActivityQuota.value = json
    } else {
      showToast(err.msg)
    }
  } else {
    // 获取不可用额度数据
    showLoadingToast()
    const [err, json] = await notApplicableQuotaWish(params)
    closeToast()
    if (!err) {
      unAvailableActivityQuota.value = json
    } else {
      showToast(err.msg)
    }
  }
}

// ==================== 订单提交处理 ====================
// 订单提交处理函数，使用节流防止重复提交
const handleSubmitOrder = throttle(async () => {
  // 检查订单是否可提交
  if (!isSubmit.value) return

  // 检查用户权限，角色类型为4的用户不支持下单
  if(roleType.value === '4'){
    showToast('当前用户暂不支持下单')
    return
  }

  const curAddrInfo = userStore.curAddressInfo

  // 检查地址状态，如果是临时地址或未选择地址，提示用户新建
  if (curAddrInfo.type === 2 || curAddrInfo.type === 3) {
    $alert({
      message: curAddrInfo.type === 2
        ? '您还未选择收货地址，是否新建收货地址？'
        : `您在浏览中选择${curAddrInfo.provinceName}${curAddrInfo.cityName}，是否新建收货地址？`,
      confirmButtonText: '新建地址',
      cancelButtonText: '取消',
      showCancelButton: true,
      onConfirmCallback: () => {
        router.push({ name: 'address-add', query: curAddrInfo })
      },
      onCancelCallback: () => {
        userStore.setTempAddr(null)
      }
    })
    return
  }

  // 检查是否有不可售商品
  const hasUnsupportedGoods = orderCartList.value.some(supplier =>
    get(supplier, 'goodsList', []).some(skuInfo => get(skuInfo, 'nosale'))
  )

  if (hasUnsupportedGoods) {
    showToast('当前部分商品在所选地区暂不支持销售，请更改收货地址或者返回重新选择商品')
    return
  }

  // 福利汇业务需要获取开发者ID
  let currentDeveloperId = ''
  if (getBizCode() === 'fulihui') {
    currentDeveloperId = curDeveloperId.get()
    if (!currentDeveloperId) {
      showLoadingToast()
      try {
        const [err, json] = await getFuLiHuiID({ bizCode: getBizCode('QUERY') })
        closeToast()
        if (!err) {
          currentDeveloperId = json || ''
          curDeveloperId.set(currentDeveloperId)
        }
      } catch (error) {
        closeToast()
        console.warn('获取福利汇ID失败:', error)
      }
    }
  }

  // 构建订单信息对象
  if (!isRemoveGifts.value) {
    // 构建购物车商品列表
    const cartGoodsList = orderCartList.value.flatMap(supplier =>
      get(supplier, 'goodsList', []).map(skuInfo => ({
        cartGoodsId: get(skuInfo, 'rawData.cartGoodsId'),
        cartSkuId: get(skuInfo, 'rawData.cartSkuId'),
        skuNum: get(skuInfo, 'quantity'),
        supplierCode: get(skuInfo, 'rawData.supplierCode'),
        skuNeedGift: true
      }))
    )

    // 构建基础订单信息
    const baseOrderInfo = {
      disriBiz: getBizCode('ORDER'),
      bizChannelCode: curChannelBiz.get(),
      addressInfo: { ...curAddrInfo },
      developerInfo: {
        developerId: developerId.value || currentDeveloperId,
        proxyChannel: ''
      },
      cartGoodsList,
      isDirectPay: null
    }

    // 如果是政企业务，添加企业编码
    if (getBizCode() === 'zq') {
      const zqInfo = queryZqInfo()
      baseOrderInfo.enterpriseCode = zqInfo.ciCode ? zqInfo.ciCode : ''
    }

    orderVoInfo.value = baseOrderInfo
  }

  // 设置支付类型
  if (orderType.value === ORDER_TYPE.BUY_NOW) {
    orderVoInfo.value.isDirectPay = true
  } else if (orderType.value === ORDER_TYPE.CART) {
    orderVoInfo.value.isDirectPay = false
  } else {
    return
  }

  orderVoInfo.value.bizChannelCode = curChannelBiz.get()

  // 处理债券相关参数（如果存在）
  const bondParams = ['curSelectedMoney', 'curSelectedTime', 'orderNo']
  bondParams.forEach(param => {
    const value = get(route.query, param)
    if (value) {
      const mapping = {
        curSelectedMoney: 'bondPrice',
        curSelectedTime: 'bondTerm',
        orderNo: 'bondOrderId'
      }
      orderVoInfo.value[mapping[param]] = param === 'curSelectedTime' ? (+value) * 12 : +value
    }
  })

  // 提交订单
  try {
    showLoadingToast()
    const [err, json] = await submitOrder({
      orderVoInfo: JSON.stringify(orderVoInfo.value)
    })
    closeToast()

    if (!err) {
      // 根据不同业务类型处理订单提交成功后的逻辑
      if (getBizCode() === 'fupin' && get(json, 'isNeedCompanyInsert') === 'true') {
        // 扶贫业务需要显示对话框
        dialogShow.value = true
        wapay.value = {
          encryptContent: get(json, 'encryptContent', ''),
          wapURL: get(json, 'wapURL', ''),
          bizOrderId: get(json, 'storeOrderId', '')
        }
      } else if (getBizCode() === 'zq') {
        // 政企业务通知成功后跳转到订单结果页
        router.push('/zq/orderresult')
        if (orderVoInfo.value.isDirectPay) {
          buyProductNow.set('')
          buyProductNowSession.set('')
        }
      } else {
        // 普通业务跳转到支付页面
        formSubmit(get(json, 'wapURL'), { param: get(json, 'encryptContent') })
        if (get(orderVoInfo.value, 'isDirectPay')) {
          buyProductNow.set('')
          buyProductNowSession.set('')
        }
      }
    } else {
      // 处理订单提交失败的情况
      if (get(err, 'code') === '3008') {
        // 赠品库存不足的特殊处理
        const removeGifts = () => {
          orderCartList.value.forEach(supplier =>
            get(supplier, 'goodsList', []).forEach(product => {
              if (get(product, 'rawData.cartSkuId') === get(err, 'data')) {
                const skuList = get(product, 'rawData.goods.skuList', [])
                skuList.forEach(item => {
                  item.giftList = []
                })
              }
            })
          )

          get(orderVoInfo.value, 'cartGoodsList', []).forEach(item => {
            if (get(item, 'cartSkuId') === get(err, 'data')) {
              item.skuNeedGift = false
            }
          })
          isRemoveGifts.value = true
        }

        $alert({
          message: `您购买的商品: ${get(err, 'msg')}。赠品已赠完，是否继续购买？`,
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          showCancelButton: true,
          messageAlign: 'left',
          onConfirmCallback: removeGifts,
          onCancelCallback: () => { }
        })
      } else {
        showToast(get(err, 'msg'))
      }
    }
  } catch (error) {
    closeToast()
    console.warn('提交订单失败:', error)
    showToast('提交订单失败，请重试')
  }
}, 1000)

// ==================== 生命周期管理 ====================
// 组件挂载时初始化订单数据
// 按顺序加载地址、商品、汇总等数据
onMounted(async () => {
  await initOrderData()
})
</script>

<style scoped lang="less">
.order-confirm {
  min-height: 100vh;
  background-color: #F8F9FA;
  padding: 8px 10px;
  box-sizing: border-box;

  &__goods {
    margin-bottom: 8px;
  }

  &__summary {
    margin-bottom: 8px;

    :deep(.order-confirm__activity-text) {
      color: var(--wo-biz-theme-color);
      font-weight: 500;

      &--empty {
        color: #718096;
        font-weight: 400;
      }
    }
  }

  &__submit {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 16px;

    &-info {
      flex: 1;
      min-width: 0;
    }

    &-price {
      font-size: 14px;
      color: #171E24;
      display: block;
      margin-bottom: 4px;
      line-height: 1.4;
    }

    &-details {
      display: flex;
      flex-wrap: wrap;
      gap: 8px;

      span {
        font-size: 13px;
        color: #718096;
        line-height: 1.4;
        white-space: nowrap;
      }
    }
  }
}

* {
  box-sizing: border-box;
}

img {
  max-width: 100%;
  height: auto;
  vertical-align: middle;
}
</style>
