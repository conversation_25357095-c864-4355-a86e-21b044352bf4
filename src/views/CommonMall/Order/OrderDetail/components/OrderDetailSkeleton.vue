<!--
/**
 * 订单详情页骨架屏组件
 *
 * 主要功能：
 * 1. 在订单详情数据加载时显示骨架屏效果，提升用户体验
 * 2. 模拟订单详情页面的完整布局结构，包括头部、物流、地址、商品、价格、订单信息等区域
 * 3. 提供流畅的加载动画效果，减少用户等待时的焦虑感
 * 4. 与真实订单详情页面保持一致的尺寸和布局，确保无缝切换
 * 5. 采用分区域设计，分别模拟不同功能模块的加载状态
 *
 * 技术特点：
 * - 使用CSS动画实现流畅的骨架屏效果
 * - 采用渐变背景模拟内容加载状态
 * - 响应式设计，适配不同屏幕尺寸
 * - 无需JavaScript逻辑，纯CSS实现
 * - 模块化设计，便于维护和扩展
 *
 * 使用场景：
 * - 订单详情页面数据加载时的占位显示
 * - 任何需要订单详情加载状态的页面
 */
-->

<template>
  <!-- 订单详情骨架屏主容器 -->
  <div class="order-detail-skeleton">
    <!-- 订单状态头部骨架 -->
    <div class="order-detail-skeleton__header">
      <!-- 订单状态标题骨架线 -->
      <div class="skeleton-line skeleton-line--title"></div>
      <!-- 订单状态副标题骨架线 -->
      <div class="skeleton-line skeleton-line--subtitle"></div>
    </div>

    <!-- 订单详情内容区域骨架 -->
    <div class="order-detail-skeleton__content">
      <!-- 物流状态卡片骨架 -->
      <div class="skeleton-card">
        <div class="skeleton-logistics">
          <!-- 物流时间轴骨架 -->
          <div class="skeleton-logistics__timeline">
            <!-- 物流状态点骨架 -->
            <div class="skeleton-circle"></div>
            <!-- 物流连接线骨架 -->
            <div class="skeleton-line--vertical"></div>
            <!-- 物流状态点骨架 -->
            <div class="skeleton-circle"></div>
          </div>
          <!-- 物流内容骨架 -->
          <div class="skeleton-logistics__content">
            <!-- 物流标题骨架线 -->
            <div class="skeleton-line skeleton-line--logistics-title"></div>
            <!-- 物流详情骨架线 -->
            <div class="skeleton-line skeleton-line--logistics-detail"></div>
            <!-- 物流地址骨架线 -->
            <div class="skeleton-line skeleton-line--logistics-address"></div>
          </div>
        </div>
      </div>

      <!-- 收货地址卡片骨架 -->
      <div class="skeleton-card">
        <div class="skeleton-address">
          <!-- 地址头部骨架 -->
          <div class="skeleton-address__header">
            <!-- 收货人信息骨架线 -->
            <div class="skeleton-line skeleton-line--address-user"></div>
            <!-- 地址操作骨架线 -->
            <div class="skeleton-line skeleton-line--address-action"></div>
          </div>
          <!-- 详细地址骨架线 -->
          <div class="skeleton-line skeleton-line--address-detail"></div>
        </div>
      </div>

      <!-- 商品信息卡片骨架 -->
      <div class="skeleton-card">
        <div class="skeleton-goods">
          <!-- 商品图片骨架 -->
          <div class="skeleton-goods__image"></div>
          <!-- 商品信息骨架 -->
          <div class="skeleton-goods__info">
            <!-- 商品标题骨架线 -->
            <div class="skeleton-line skeleton-line--goods-title"></div>
            <!-- 商品规格骨架线 -->
            <div class="skeleton-line skeleton-line--goods-spec"></div>
            <!-- 商品价格骨架线 -->
            <div class="skeleton-line skeleton-line--goods-price"></div>
          </div>
        </div>
      </div>

      <!-- 价格明细卡片骨架 -->
      <div class="skeleton-card">
        <div class="skeleton-price">
          <!-- 价格行骨架线 -->
          <div class="skeleton-line skeleton-line--price-row"></div>
          <div class="skeleton-line skeleton-line--price-row"></div>
          <!-- 总价骨架线 -->
          <div class="skeleton-line skeleton-line--price-total"></div>
        </div>
      </div>

      <!-- 订单信息卡片骨架 -->
      <div class="skeleton-card">
        <div class="skeleton-order">
          <!-- 订单信息行骨架线 -->
          <div class="skeleton-line skeleton-line--info-row"></div>
          <div class="skeleton-line skeleton-line--info-row"></div>
          <div class="skeleton-line skeleton-line--info-row"></div>
          <div class="skeleton-line skeleton-line--info-row"></div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
// ==================== 订单详情骨架屏组件 ====================
// 纯展示组件，无需任何逻辑处理
</script>

<style scoped lang="less">
.order-detail-skeleton {
  min-height: 100vh;
  background-color: #F8F9FA;

  &__header {
    width: 100%;
    height: 70px;
    background: #FFFFFF;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 8px;
    padding: 0 20px;
    box-sizing: border-box;
  }

  &__content {
    padding: 8px 10px;
    box-sizing: border-box;
  }
}

.skeleton-card {
  background-color: #FFFFFF;
  padding: 15px;
  margin-bottom: 8px;
  border-radius: 10px;
}

.skeleton-line {
  height: 16px;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
  border-radius: 4px;

  &--title {
    width: 120px;
    height: 20px;
    margin-bottom: 8px;
  }

  &--subtitle {
    width: 200px;
    height: 16px;
  }

  &--logistics-title {
    width: 150px;
    height: 16px;
    margin-bottom: 8px;
  }

  &--logistics-detail {
    width: 200px;
    height: 14px;
    margin-bottom: 6px;
  }

  &--logistics-address {
    width: 180px;
    height: 14px;
  }

  &--address-user {
    width: 120px;
    height: 16px;
  }

  &--address-action {
    width: 60px;
    height: 16px;
  }

  &--address-detail {
    width: 100%;
    height: 14px;
  }

  &--goods-title {
    width: 100%;
    height: 16px;
    margin-bottom: 8px;
  }

  &--goods-spec {
    width: 80%;
    height: 14px;
    margin-bottom: 8px;
  }

  &--goods-price {
    width: 60px;
    height: 16px;
  }

  &--price-row {
    width: 100%;
    height: 14px;
    margin-bottom: 8px;
  }

  &--price-total {
    width: 80px;
    height: 18px;
  }

  &--info-row {
    width: 100%;
    height: 14px;
    margin-bottom: 8px;

    &:last-child {
      margin-bottom: 0;
    }
  }

  &--vertical {
    width: 2px;
    height: 25px;
    background: linear-gradient(180deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 100% 200%;
    animation: skeleton-loading 1.5s infinite;
    margin: 5px 0;
  }
}

.skeleton-circle {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
}

.skeleton-logistics {
  display: flex;
  align-items: flex-start;
  gap: 12px;

  &__timeline {
    flex-shrink: 0;
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-top: 2px;
  }

  &__content {
    flex: 1;
  }
}

.skeleton-address {
  &__header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
  }
}

.skeleton-goods {
  display: flex;
  gap: 12px;

  &__image {
    width: 90px;
    height: 90px;
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: skeleton-loading 1.5s infinite;
    border-radius: 4px;
    flex-shrink: 0;
  }

  &__info {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
  }
}

.skeleton-price {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.skeleton-order {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.skeleton-actions {
  display: flex;
  gap: 12px;
  padding: 15px;
  background-color: #FFFFFF;
  border-radius: 10px;
}

.skeleton-button {
  height: 42px;
  flex: 1;
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
  border-radius: 9999px;

  &--primary {
    background: linear-gradient(90deg, #ffd4b3 25%, #ffb380 50%, #ffd4b3 75%);
    background-size: 200% 100%;
  }
}

@keyframes skeleton-loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}
</style>
