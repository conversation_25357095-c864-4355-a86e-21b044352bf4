<!--
/**
 * 地址信息卡片组件
 *
 * 主要功能：
 * 1. 展示订单收货地址信息，包括收货人姓名、电话和详细地址
 * 2. 支持地址修改功能，在特定订单状态下允许用户修改收货地址
 * 3. 显示地址修改状态，包括审核中、修改成功、修改失败等状态
 * 4. 根据业务类型和订单状态控制地址操作权限
 * 5. 集成地址图标和操作图标，提升用户体验
 * 6. 支持京东订单的特殊地址处理逻辑
 *
 * 技术特点：
 * - 使用计算属性控制操作区域显示
 * - 采用条件渲染实现不同状态的界面切换
 * - 集成业务代码判断，区分不同业务场景
 * - 使用toRefs保持props响应性
 *
 * 使用场景：
 * - 订单详情页面的地址信息展示
 * - 需要地址修改功能的订单管理场景
 * - 地址状态跟踪和反馈
 */
-->

<template>
  <!-- 地址信息卡片容器 -->
  <WoCard>
    <div class="address-info-card">
      <!-- 地址信息头部区域 -->
      <div class="address-info-card__header">
        <!-- 收货人信息区域 -->
        <div class="address-info-card__user">
          <!-- 地址图标 -->
          <img src="../../../../../static/images/address-icon.png" alt="地址图标" class="address-info-card__user-icon" />
          <!-- 收货人姓名 -->
          <span class="address-info-card__user-name">{{ receiverName }}</span>
          <!-- 收货人电话 -->
          <span class="address-info-card__user-phone">{{ receiverPhone }}</span>
        </div>
        <!-- 地址操作区域 -->
        <!-- 根据订单状态和业务类型决定是否显示 -->
        <div class="address-info-card__action" v-if="shouldShowAddressAction">
          <!-- 根据地址更新状态显示不同内容 -->
          <!-- 可修改状态：显示修改地址按钮 -->
          <div
            v-if="addressUpdateState === '01'"
            class="address-info-card__action-button"
            @click="handleEditAddress"
          >
            <img src="../../../../../static/images/mod-address-icon.png" alt="地址编辑" class="address-info-card__action-icon" />
            修改地址
          </div>
          <!-- 审核中状态：显示审核中标签 -->
          <span v-else-if="addressUpdateState === '02'" class="address-status status-review">审核中</span>
          <!-- 修改失败状态：显示修改失败标签 -->
          <span v-else-if="addressUpdateState === '03' || addressUpdateState === '06'" class="address-status status-fail">修改失败</span>
          <!-- 修改成功状态：显示修改成功标签 -->
          <span v-else-if="addressUpdateState === '04'" class="address-status status-success">修改成功</span>
          <!-- addressUpdateState === '00' 或其他情况不显示任何内容 -->
        </div>
      </div>
      <!-- 地址详细信息内容区域 -->
      <div class="address-info-card__content">
        <!-- 详细地址信息 -->
        <div class="address-info-card__detail">
          {{ fullAddress }}
        </div>
      </div>
    </div>
  </WoCard>
</template>

<script setup>
import { toRefs, computed } from 'vue'
import WoCard from '@components/WoElementCom/WoCard.vue'
import { getBizCode } from '@/utils/curEnv'

// ==================== 组件属性定义 ====================
// 定义组件接收的props参数
const props = defineProps({
  // 收货人姓名
  receiverName: {
    type: String,
    default: ''
  },
  // 收货人电话
  receiverPhone: {
    type: String,
    default: ''
  },
  // 完整收货地址
  fullAddress: {
    type: String,
    default: ''
  },
  // 地址更新状态，用于控制地址修改功能的显示
  addressUpdateState: {
    type: String,
    default: '00'
  },
  // 订单状态，用于判断是否允许地址修改
  orderState: {
    type: String,
    default: ''
  },
  // 是否为京东商品订单
  isJD: {
    type: Boolean,
    default: false
  },
  // 订单详细信息对象
  orderInfo: {
    type: Object,
    default: () => ({})
  },
  // 收货人完整数据，用于地址编辑
  receiveData: {
    type: Object,
    default: () => ({})
  }
})

// 定义组件向父组件发射的事件
const emit = defineEmits(['edit-address'])

// ==================== 业务逻辑处理 ====================
// 获取当前业务代码
const bizCode = getBizCode()

// 使用toRefs解构props，保持响应性
const { receiverName, receiverPhone, fullAddress, addressUpdateState, orderState, isJD, orderInfo, receiveData } = toRefs(props)

// 是否显示地址操作区域
// 仅在特定订单状态且非政企业务时显示地址修改功能
const shouldShowAddressAction = computed(() => {
  return (orderState.value === '0' || orderState.value === '1' || orderState.value === '3') && bizCode !== 'zq'
})

// ==================== 事件处理函数 ====================
// 处理地址编辑按钮点击事件
// 向父组件发射edit-address事件
const handleEditAddress = () => {
  emit('edit-address')
}
</script>

<style scoped lang="less">
.address-info-card {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 12px;

  &__header {
    display: flex;
    justify-content: space-between;
    width: 100%;
    overflow: hidden;
  }

  &__content {
    width: 100%;
  }

  &__user {
    flex: 1;
    display: flex;
    align-items: center;
    font-size: 16px;
    color: #171E24;
    font-weight: 500;
    line-height: 1.5;
    margin-right: 8px;
    overflow: hidden;
  }

  &__user-icon {
    width: 13px;
    height: 15px;
    margin-right: 8px;
  }

  &__user-name {
    margin-right: 12px;
    overflow: hidden; text-overflow: ellipsis; white-space: nowrap;;
  }

  &__action {
    font-size: 14px;
    font-weight: 500;
    line-height: 1.5;
  }

  &__action-button {
    display: flex;
    align-items: center;
    color: var(--wo-biz-theme-color);
    cursor: pointer;
  }

  &__action-icon {
    margin-right: 4px;
    width: 13px;
    height: 13px;
    vertical-align: middle;
  }

  &__detail {
    font-size: 12px;
    color: #4A5568;
    font-weight: 400;
    line-height: 1.5;
    display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
        word-break: break-all;
  }
}

// 地址状态样式
.address-status {
  font-size: 13px;
  font-weight: 500;
  line-height: 1.5;

  &.status-review {
    color: #718096;
  }

  &.status-fail {
    color: #EF4444;
  }

  &.status-success {
    color: #10B981;
  }
}
</style>
