<!--
/**
 * 物流状态卡片组件
 *
 * 主要功能：
 * 1. 展示订单物流状态信息，包括当前配送状态和最新物流动态
 * 2. 提供物流时间轴视觉效果，直观显示配送进度
 * 3. 支持物流详情查看功能，点击可跳转到详细物流页面
 * 4. 显示收货人信息，包括姓名、电话和收货地址
 * 5. 根据订单完成状态调整显示内容和样式
 * 6. 集成物流跟踪数据，实时更新配送状态
 *
 * 技术特点：
 * - 使用时间轴组件展示物流进度
 * - 采用条件渲染控制不同状态的显示
 * - 集成点击事件处理物流详情查看
 * - 使用toRefs保持props响应性
 *
 * 使用场景：
 * - 订单详情页面的物流状态展示
 * - 需要显示配送进度的业务场景
 */
-->

<template>
  <!-- 物流状态卡片容器 -->
  <WoCard>
    <div class="logistics-status-card">
      <!-- 物流时间轴区域 -->
      <!-- 仅在订单未完成时显示，展示配送进度的视觉时间轴 -->
      <div class="logistics-status-card__timeline" v-if="!isCompleted">
        <div class="logistics-status-card__timeline-item">
          <!-- 已接收状态点，表示订单已被接收处理 -->
          <div class="logistics-status-card__dot logistics-status-card__dot--received"></div>
          <!-- 连接线，表示配送过程中的时间流 -->
          <div class="logistics-status-card__line" :style="{ minHeight: 25 + 'px' }"></div>
          <!-- 待配送状态点，表示正在配送中 -->
          <div class="logistics-status-card__dot logistics-status-card__dot--pending"></div>
        </div>
      </div>

      <!-- 物流状态内容区域 -->
      <div class="logistics-status-card__content">
        <!-- 物流状态信息区域，点击可查看详细物流信息 -->
        <div class="logistics-status-card__status" @click="handleViewLogistics">
          <div class="logistics-status-card__status-content">
            <!-- 物流状态头部信息 -->
            <div class="logistics-status-card__header">
              <!-- 物流包裹图标 -->
              <img src="../../assets/package.png" alt="物流图标" class="logistics-status-card__icon" />
              <!-- 物流状态文字描述 -->
              <span class="logistics-status-card__text">
                {{ statusText }}
              </span>
              <!-- 最新物流动态详情 -->
              <!-- 根据是否有物流跟踪记录显示不同内容 -->
              <span class="logistics-status-card__detail" v-if="lastTrack">
                {{ lastTrack.context || '暂无物流信息' }}
              </span>
              <span class="logistics-status-card__detail" v-else>已上传物流单号，暂无快递信息</span>
            </div>
            <!-- 查看详情箭头图标 -->
            <!-- 仅在支持查看详情时显示 -->
            <div class="logistics-status-card__arrow" v-if="showArrow">
              <img src="../../assets/arrow-black.png" alt="查看详情" class="arrow-icon" />
            </div>
          </div>
        </div>
        <!-- 收货人信息区域 -->
        <!-- 仅在订单未完成时显示收货人详细信息 -->
        <div class="logistics-status-card__receiver" v-if="!isCompleted">
          <!-- 收货人基本信息：姓名和电话 -->
          <div class="logistics-status-card__receiver-info">
            <!-- 收货人姓名 -->
            <div class="logistics-status-card__receiver-name">{{ receiverName }}</div>
            <!-- 收货人电话 -->
            <div class="logistics-status-card__receiver-phone">{{ receiverPhone }}</div>
          </div>
          <!-- 收货地址信息 -->
          <div class="logistics-status-card__receiver-address">
            {{ receiverAddress }}
          </div>
        </div>
      </div>
    </div>
  </WoCard>
</template>

<script setup>
import { toRefs } from 'vue'
import WoCard from '@components/WoElementCom/WoCard.vue'

// ==================== 组件属性定义 ====================
// 定义组件接收的props参数
const props = defineProps({
  // 物流状态文字描述
  statusText: {
    type: String,
    default: '暂无物流信息'
  },
  // 最新物流跟踪记录对象
  lastTrack: {
    type: Object,
    default: null
  },
  // 是否显示查看详情箭头
  showArrow: {
    type: Boolean,
    default: false
  },
  // 订单是否已完成标识
  isCompleted: {
    type: Boolean,
    default: false
  },
  // 收货人姓名
  receiverName: {
    type: String,
    default: ''
  },
  // 收货人电话
  receiverPhone: {
    type: String,
    default: ''
  },
  // 收货地址
  receiverAddress: {
    type: String,
    default: ''
  }
})

// 使用toRefs解构props，保持响应性
const {
  statusText,
  lastTrack,
  showArrow,
  isCompleted,
  receiverName,
  receiverPhone,
  receiverAddress
} = toRefs(props)

// 定义组件向父组件发射的事件
const emit = defineEmits(['view-logistics'])

// ==================== 事件处理函数 ====================
// 查看物流详情处理函数
// 当用户点击物流状态区域时触发，向父组件发射view-logistics事件
const handleViewLogistics = () => {
  emit('view-logistics')
}
</script>

<style scoped lang="less">
.logistics-status-card {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  overflow: hidden;

  &__timeline {
    flex-shrink: 0;
    position: relative;
  }

  &__timeline-item {
    margin-top: 2px;
    position: relative;
    display: flex;
    flex-direction: column;
    align-items: center;
  }

  &__dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    position: relative;
    z-index: 2;

    &--received {
      background: var(--wo-biz-theme-color);
    }

    &--pending {
      background: rgba(255, 120, 10, 0.50);
    }
  }

  &__line {
    width: 2px;
    background-color: #E2E8EE;
    transition: height 0.3s ease;
    z-index: 1;
    margin: 5px 0;
    opacity: 0.5;
    background: var(--wo-biz-theme-color);
  }

  &__content {
    flex: 1;
    overflow: hidden;
  }

  &__status {
    cursor: pointer;
  }

  &__status-content {
    display: flex;
    justify-content: space-between;
  }

  &__header {
    font-size: 14px;
    color: #171E24;
    font-weight: 500;
    line-height: 1.4;
    margin: 0;
    display: flex;
    align-items: center;
    overflow: hidden;
  }

  &__icon {
    width: 16px;
    height: 16px;
    margin-right: 6px;
  }

  &__text {
    margin-right: 5px;
    flex-shrink: 0;
    color: var(--wo-biz-theme-color);
    font-weight: 600;
  }

  &__detail {
    flex: 1;
    overflow: hidden; text-overflow: ellipsis; white-space: nowrap;;
  }

  &__arrow {
    flex-shrink: 0;
    width: 7px;
    height: 10px;

    img {
      width: 100%;
      height: 100%;
    }
  }

  &__receiver {
    margin-top: 16px;
  }

  &__receiver-info {
    display: flex;
    align-items: center;
    margin-bottom: 8px;
    font-size: 14px;
    color: #171E24;
    font-weight: 500;
  }

  &__receiver-name {
    margin-right: 12px;
    overflow: hidden; text-overflow: ellipsis; white-space: nowrap;;
  }

  &__receiver-address {
    font-size: 12px;
    color: #4A5568;
    font-weight: 400;
    line-height: 1.5;
    display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
        overflow: hidden;
        word-break: break-all;
  }
}
</style>
