<!--
/**
 * 保证金信息卡片组件
 *
 * 主要功能：
 * 1. 展示订单相关的保证金信息，包括保证金金额和存期
 * 2. 提供保证金详情查看功能，支持点击跳转到详情页面
 * 3. 采用卡片式布局，清晰展示保证金相关信息
 * 4. 集成保证金图标，提升视觉识别度
 * 5. 支持响应式设计，适配不同屏幕尺寸
 *
 * 技术特点：
 * - 使用点击事件处理保证金详情查看
 * - 采用flex布局实现信息对齐
 * - 使用toRefs保持props响应性
 * - 集成图标和箭头提升用户体验
 *
 * 使用场景：
 * - 订单详情页面的保证金信息展示
 * - 包含保证金的订单管理场景
 * - 保证金相关业务的信息展示
 */
-->

<template>
  <!-- 保证金信息卡片容器 -->
  <WoCard>
    <!-- 保证金信息主容器，支持点击查看详情 -->
    <div class="deposit-info-card" @click="handleViewDeposit">
      <!-- 保证金基本信息区域 -->
      <div class="deposit-info-card__info">
        <!-- 保证金图标 -->
        <img src="../../../../../static/images/baozhengjin.png" alt="保证金图标" class="deposit-info-card__icon" />
        <!-- 保证金标签 -->
        <span class="deposit-info-card__label">保证金</span>
      </div>
      <!-- 保证金金额和期限信息区域 -->
      <div class="deposit-info-card__amount">
        <!-- 保证金金额和存期信息 -->
        <span class="deposit-info-card__amount-text">{{ depositAmount }}元存期{{ depositPeriod }}年</span>
      </div>
      <!-- 操作区域，查看详情按钮 -->
      <div class="deposit-info-card__action" @click="handleViewDeposit">
        <!-- 查看详情文字 -->
        <span class="deposit-info-card__action-text">查看详情</span>
        <!-- 右箭头图标 -->
        <img src="../../../../../static/images/arrow-right-gray.png" alt="查看详情" class="deposit-info-card__action-icon" />
      </div>
    </div>
  </WoCard>
</template>

<script setup>
import { toRefs } from 'vue'
import WoCard from '@components/WoElementCom/WoCard.vue'

// ==================== 组件属性定义 ====================
// 定义组件接收的props参数
const props = defineProps({
  // 保证金金额，支持数字和字符串类型
  depositAmount: {
    type: [Number, String],
    default: 0
  },
  // 保证金存期，以年为单位
  depositPeriod: {
    type: [Number, String],
    default: 0
  }
})

// 定义组件向父组件发射的事件
const emit = defineEmits(['view-deposit'])

// 使用toRefs解构props，保持响应性
const { depositAmount, depositPeriod } = toRefs(props)

// ==================== 事件处理函数 ====================
// 处理查看保证金详情事件
// 向父组件发射view-deposit事件
const handleViewDeposit = () => {
  emit('view-deposit')
}
</script>

<style scoped lang="less">
.deposit-info-card {
  display: flex;
  align-items: center;
  justify-content: space-between;
  cursor: pointer;
  padding: 4px 0;

  &__info {
    display: flex;
    align-items: center;
    gap: 8px;
  }

  &__icon {
    width: 20px;
    height: 20px;
  }

  &__label {
    font-size: 16px;
    color: #171E24;
    font-weight: 500;
  }

  &__amount {
    flex: 1;
    text-align: center;
  }

  &__amount-text {
    font-size: 14px;
    color: #4A5568;
    font-weight: 400;
  }

  &__action {
    display: flex;
    align-items: center;
    gap: 4px;
  }

  &__action-text {
    font-size: 14px;
    color: #718096;
    font-weight: 400;
  }

  &__action-icon {
    width: 6px;
    height: 10px;
  }
}
</style>
