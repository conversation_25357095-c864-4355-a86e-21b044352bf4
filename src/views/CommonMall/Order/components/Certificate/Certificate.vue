<template>
  <Teleport to="body">
    <Transition name="certificate-fade" appear>
      <div class="certificate-modal" v-if="show" @click.self="onClose">
        <div class="certificate-backdrop" @click="onClose" />
        <Transition name="certificate-scale" appear>
          <div class="certificate-content" v-if="contentVisible">
            <!-- 骨架屏加载状态 -->
            <div v-if="isLoading" class="certificate-skeleton">
              <div class="skeleton-image">
                <div class="skeleton-shimmer"></div>
              </div>
              <div class="skeleton-hint">
                <div class="skeleton-shimmer"></div>
              </div>
              <div class="skeleton-progress">
                <div class="progress-bar"></div>
              </div>
            </div>

            <!-- 证书内容 -->
            <template v-else>
              <Transition name="certificate-image" appear>
                <img class="certificate-image" :src="img" v-if="img" alt="荣誉证书" data-name="user-comp-certificate"
                  loading="eager" decoding="async" @load="onImageLoad" @error="onImageError" />
              </Transition>

              <Transition name="certificate-hint" appear>
                <img class="certificate-save-hint" :src="saveHintImg" v-if="allowImgSave && saveHintImg" alt="长按保存提示"
                  loading="eager" decoding="sync" />
              </Transition>

              <Transition name="certificate-button" appear>
                <button class="certificate-close-btn" v-if="img" @click="onClose" aria-label="关闭证书" type="button">
                  <img :src="closeButtonImg" alt="关闭" loading="eager" decoding="sync" />
                </button>
              </Transition>
            </template>
          </div>
        </Transition>
      </div>
    </Transition>
  </Teleport>
</template>


<script setup>
import { ref, computed, watch, onMounted, nextTick, onUnmounted, Teleport, Transition } from 'vue'
import { isHarmonyOS, isUnicom, isWopay, log } from 'commonkit'
import { showToast } from 'vant'
import { debounce } from 'lodash-es'
import { useEventListener } from '@vueuse/core'
import certificateImg from './certificate-img.js'
import { fpCertificateCache } from '@utils/storage.js'

// 静态资源预加载 - 立即预加载关键图片
import saveHintImgUrl from './assets/certificate-save-tips.png'
import closeButtonImgUrl from './assets/certificate-close.png'

// 立即预加载这些小图片到浏览器缓存
const preloadImage = (src) => {
  const link = document.createElement('link')
  link.rel = 'preload'
  link.as = 'image'
  link.href = src
  document.head.appendChild(link)
}

// 在模块加载时就预加载
preloadImage(saveHintImgUrl)
preloadImage(closeButtonImgUrl)

const FP_CERTIFICATE_VER = '20241223'

// 定义props
const props = defineProps({
  show: {
    type: Boolean,
    default: false
  },
  title: {
    type: String,
    required: true
  },
  date: {
    type: Date,
    required: true
  },
  amt: {
    type: String,
    required: true
  }
})

// 定义emits
const emit = defineEmits(['close'])

// 缓存相关函数
const getFpCertificateImgByCache = () => {
  const data = fpCertificateCache.get() || {}
  return data.ver === FP_CERTIFICATE_VER ? data.data : ''
}

const setFpCertificateImgToCache = () => new Promise(resolve => {
  const data = getFpCertificateImgByCache()
  if (data) {
    log('获取荣誉证书，当前存在数据，结束')
    resolve(null)
    return
  }
  log('获取荣誉证书，当前不存在数据，通过 js 文件赋值')
  fpCertificateCache.set({ ver: FP_CERTIFICATE_VER, data: certificateImg })
  resolve(null)
})

// 优化的证书生成函数 - 使用 OffscreenCanvas 提升性能
const generateCertificate = (name, amt, date) => new Promise((resolve, reject) => {
  log('[Certificate] 生成荣誉证书', name, amt, date)

  const dateText = `${date.getFullYear()}年${date.getMonth() + 1}月${date.getDate()}日`

  // 优先使用 OffscreenCanvas
  const useOffscreen = typeof OffscreenCanvas !== 'undefined'
  const canvas = useOffscreen ? new OffscreenCanvas(600, 800) : document.createElement('canvas')
  const ctx = canvas.getContext('2d', {
    alpha: false, // 不需要透明度，提升性能
    willReadFrequently: false // 优化读取性能
  })

  const baseImg = new Image()

  const cleanup = () => {
    if (!useOffscreen) {
      canvas.width = 0
      canvas.height = 0
    }
  }

  baseImg.crossOrigin = 'Anonymous'
  baseImg.src = getFpCertificateImgByCache()

  baseImg.onload = () => {
    try {
      // 使用 requestAnimationFrame 确保在下一帧渲染
      requestAnimationFrame(() => {
        canvas.width = baseImg.width
        canvas.height = baseImg.height

        // 启用图像平滑以提升质量
        ctx.imageSmoothingEnabled = true
        ctx.imageSmoothingQuality = 'high'

        ctx.drawImage(baseImg, 0, 0, baseImg.width, baseImg.height)

        const fontFamily = '"PingFang SC", "Microsoft YaHei", "微软雅黑", "Helvetica Neue", Helvetica, Arial, sans-serif'

        // 用户名
        ctx.font = `normal 24px ${fontFamily}`
        ctx.fillStyle = '#000'
        ctx.textAlign = 'left'
        ctx.textBaseline = 'middle'
        ctx.fillText(name, 50, 300)

        // 金额
        ctx.font = `normal 24px ${fontFamily}`
        ctx.fillStyle = 'red'
        ctx.textAlign = 'center'
        ctx.fillText(amt, 438, 352)

        // 日期
        ctx.font = `normal 18px ${fontFamily}`
        ctx.fillStyle = '#000'
        ctx.textAlign = 'right'
        ctx.fillText(dateText, 547, 596)

        // 使用更高质量的输出
        const getDataUrl = useOffscreen
          ? () => canvas.convertToBlob({ type: 'image/jpeg', quality: 0.9 }).then(blob => {
            return new Promise(resolve => {
              const reader = new FileReader()
              reader.onload = () => resolve(reader.result)
              reader.readAsDataURL(blob)
            })
          })
          : () => Promise.resolve(canvas.toDataURL('image/jpeg', 0.9))

        getDataUrl().then(dataUrl => {
          cleanup()
          log('[Certificate] 生成荣誉证书成功')
          resolve(dataUrl)
        }).catch(reject)
      })
    } catch (e) {
      cleanup()
      log('[Certificate] 生成荣誉证书失败', e)
      reject(e)
    }
  }

  baseImg.onerror = (e) => {
    cleanup()
    log('[Certificate] 生成荣誉证书失败', e)
    reject(e)
  }
})

// 响应式数据
const img = ref('')
const isLoading = ref(false)
const contentVisible = ref(false)
// 直接使用导入的静态资源，避免动态加载
const saveHintImg = ref(saveHintImgUrl)
const closeButtonImg = ref(closeButtonImgUrl)

// 计算属性
const allowImgSave = computed(() => {
  if (!img.value) return false
  if (isUnicom) return false
  if (isWopay && isHarmonyOS) return false
  return true
})

// 预加载静态资源 - 现在直接使用静态导入，无需动态加载
const preloadStaticAssets = () => {
  // 创建 Image 对象进行预加载，确保图片已缓存
  const preloadImages = [saveHintImgUrl, closeButtonImgUrl]

  preloadImages.forEach(src => {
    const img = new Image()
    img.src = src
    // 预加载但不需要等待完成
  })
}

// 方法
const handleBodyOverflow = (isShow) => {
  document.body.style.overflow = isShow ? 'hidden' : ''
}

// 防抖处理证书生成，避免重复调用
const processCertificate = debounce(async (isShow) => {
  if (!isShow) {
    img.value = ''
    contentVisible.value = false
    isLoading.value = false
    handleBodyOverflow(false)
    return
  }

  try {
    isLoading.value = true
    contentVisible.value = true

    // 确保缓存数据存在
    if (!getFpCertificateImgByCache()) {
      log('展示荣誉证书-当前未读取到图片')
      await setFpCertificateImgToCache()
      log('展示荣誉证书-写入荣誉证书图片完成')
    }

    log('开始生成荣誉证书')
    const base64 = await generateCertificate(props.title, props.amt, props.date)
    log('生成荣誉证书完成')

    img.value = base64
    isLoading.value = false
    await nextTick()
    handleBodyOverflow(true)
  } catch (e) {
    isLoading.value = false
    showToast('查看荣誉证书失败')
    emit('close')
    throw new Error(e)
  }
}, 300)

const onClose = () => {
  emit('close')
}

// 图片加载完成回调
const onImageLoad = () => {
  log('[Certificate] 证书图片加载完成')
}

// 图片加载失败回调
const onImageError = () => {
  log('[Certificate] 证书图片加载失败')
  showToast('证书图片加载失败')
}

// ESC 键关闭
const handleEscKey = (event) => {
  if (event.key === 'Escape' && props.show) {
    onClose()
  }
}

// 监听器
watch(() => props.show, processCertificate, { immediate: true })

// 键盘事件监听
useEventListener('keydown', handleEscKey)

// 生命周期
onMounted(async () => {
  log('初始化写入荣誉证书')
  await setFpCertificateImgToCache()
  log('初始化写入荣誉证书完成')

  // 静态资源已在模块加载时预加载，这里可以进行额外的预加载确保
  preloadStaticAssets()
})

onUnmounted(() => {
  // 清理防抖函数
  processCertificate.cancel?.()
  handleBodyOverflow(false)
})

// 暴露给模板的数据和方法
defineExpose({
  img,
  allowImgSave,
  onClose,
  isLoading
})
</script>

<style lang="less" scoped>
.certificate-modal {
  position: fixed;
  inset: 0;
  z-index: 999;
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100vw;
  height: 100vh;
  // 硬件加速
  transform: translateZ(0);
  will-change: opacity;

  .certificate-backdrop {
    position: fixed;
    inset: 0;
    z-index: 1;
    width: 100vw;
    height: 100vh;
    background-color: rgba(0, 0, 0, 0.4);
    backdrop-filter: blur(2px);
    // 硬件加速
    transform: translateZ(0);
    cursor: pointer;
  }

  .certificate-content {
    position: relative;
    z-index: 2;
    display: flex;
    flex-direction: column;
    width: min(85vw, 320px);
    max-width: 85vw;
    padding: 0 12px;
    // 硬件加速
    transform: translateZ(0);
    will-change: transform, opacity;

    // 小屏手机优化
    @media (max-width: 375px) {
      width: min(90vw, 300px);
      padding: 0 8px;
    }

    // 大屏手机和小平板
    @media (min-width: 414px) and (max-width: 768px) {
      width: min(75vw, 350px);
      padding: 0 16px;
    }

    // 平板设备
    @media (min-width: 768px) and (max-width: 1024px) {
      width: min(60vw, 400px);
      padding: 0 20px;
    }

    // 桌面设备
    @media (min-width: 1024px) {
      width: min(45vw, 420px);
      padding: 0 24px;
    }

    .certificate-image {
      width: 100%;
      height: auto;
      max-height: 75vh;
      border-radius: 8px;
      box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
      object-fit: contain;
      // 图片优化
      image-rendering: -webkit-optimize-contrast;
      image-rendering: crisp-edges;

      // 确保在小屏设备上不会过大
      @media (max-height: 667px) {
        max-height: 70vh;
      }

      // 超小屏设备进一步限制
      @media (max-height: 568px) {
        max-height: 65vh;
      }
    }

    .certificate-save-hint {
      margin: 5px auto 0;
      width: min(58%, 180px);
      max-width: 180px;
      pointer-events: none;
      // 优化图片渲染
      image-rendering: -webkit-optimize-contrast;
      image-rendering: crisp-edges;
      // 硬件加速
      transform: translateZ(0);
      will-change: opacity, transform;

      // 小屏设备调整
      @media (max-width: 375px) {
        width: min(65%, 160px);
        max-width: 160px;
      }

      // 大屏设备调整
      @media (min-width: 768px) {
        width: min(50%, 200px);
        max-width: 200px;
      }
    }

    .certificate-close-btn {
      margin: 7px auto 0;
      width: 42px;
      height: 42px;
      cursor: pointer;
      border: none;
      background: transparent;
      padding: 0;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 50%;
      transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
      // 硬件加速
      transform: translateZ(0);

      img {
        width: 100%;
        height: 100%;
        object-fit: contain;
        // 优化图片渲染
        image-rendering: -webkit-optimize-contrast;
        image-rendering: crisp-edges;
        // 硬件加速
        transform: translateZ(0);
      }

      // 小屏设备
      @media (max-width: 375px) {
        width: 38px;
        height: 38px;
      }

      // 大屏设备
      @media (min-width: 768px) {
        width: 48px;
        height: 48px;
      }

      // 触摸设备优化
      @media (hover: none) and (pointer: coarse) {
        width: 46px;
        height: 46px;

        @media (max-width: 375px) {
          width: 42px;
          height: 42px;
        }
      }
    }
  }
}

// 骨架屏样式
// 骨架屏样式优化
.certificate-skeleton {
  display: flex;
  flex-direction: column;
  width: 100%;
  animation: skeleton-fade-in 0.6s cubic-bezier(0.4, 0, 0.2, 1);

  .skeleton-image {
    position: relative;
    width: 100%;
    height: 350px;
    background: linear-gradient(135deg, #f5f5f5 0%, #eeeeee 50%, #f5f5f5 100%);
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
    animation: skeleton-pulse 2s ease-in-out infinite;

    // 小屏设备
    @media (max-width: 375px) {
      height: 300px;
    }

    // 超小屏设备
    @media (max-height: 568px) {
      height: 280px;
    }

    // 大屏设备
    @media (min-width: 768px) {
      height: 400px;
    }

    // 桌面设备
    @media (min-width: 1024px) {
      height: 450px;
    }

    .skeleton-shimmer {
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg,
          transparent 0%,
          rgba(255, 255, 255, 0.6) 50%,
          transparent 100%);
      animation: skeleton-shimmer 2s infinite;
    }
  }

  .skeleton-hint {
    position: relative;
    margin: 12px auto 0;
    width: min(58%, 180px);
    height: 30px;
    background: linear-gradient(135deg, #f5f5f5 0%, #eeeeee 50%, #f5f5f5 100%);
    border-radius: 6px;
    overflow: hidden;
    animation: skeleton-pulse 2s ease-in-out infinite 0.2s;

    // 小屏设备
    @media (max-width: 375px) {
      width: min(65%, 160px);
      height: 28px;
    }

    // 大屏设备
    @media (min-width: 768px) {
      width: min(50%, 200px);
      height: 34px;
    }

    .skeleton-shimmer {
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg,
          transparent 0%,
          rgba(255, 255, 255, 0.6) 50%,
          transparent 100%);
      animation: skeleton-shimmer 2s infinite 0.3s;
    }
  }

  .skeleton-progress {
    margin: 16px auto 0;
    width: 40%;
    height: 3px;
    background: #f0f0f0;
    border-radius: 2px;
    overflow: hidden;

    .progress-bar {
      width: 0%;
      height: 100%;
      background: linear-gradient(90deg, #4CAF50, #81C784);
      border-radius: 2px;
      animation: progress-loading 3s ease-out infinite;
    }
  }
}

// 关键帧动画
@keyframes skeleton-fade-in {
  from {
    opacity: 0;
    transform: translateY(10px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes skeleton-shimmer {
  0% {
    left: -100%;
  }

  100% {
    left: 100%;
  }
}

@keyframes skeleton-pulse {

  0%,
  100% {
    opacity: 1;
    transform: scale(1);
  }

  50% {
    opacity: 0.8;
    transform: scale(1.002);
  }
}

@keyframes progress-loading {
  0% {
    width: 0%;
  }

  70% {
    width: 85%;
  }

  100% {
    width: 100%;
  }
}

// 内容元素错落动画
.certificate-image-enter-active {
  transition: all 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.certificate-image-enter-from {
  opacity: 0;
  transform: scale(0.9) translateY(20px);
}

.certificate-hint-enter-active {
  transition: all 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94) 0.2s;
}

.certificate-hint-enter-from {
  opacity: 0;
  transform: translateY(15px);
}

.certificate-button-enter-active {
  transition: all 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275) 0.4s;
}

.certificate-button-enter-from {
  opacity: 0;
  transform: scale(0.8) translateY(10px);
}

// 优化过渡动画
.certificate-fade-enter-active {
  transition: all 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.certificate-fade-leave-active {
  transition: all 0.4s cubic-bezier(0.55, 0.055, 0.675, 0.19);
}

.certificate-scale-enter-active {
  transition: all 0.6s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.certificate-scale-leave-active {
  transition: all 0.4s cubic-bezier(0.55, 0.055, 0.675, 0.19);
}

.certificate-scale-enter-from {
  opacity: 0;
  transform: scale(0.7) translateY(30px) rotateX(15deg);
}

.certificate-scale-leave-to {
  opacity: 0;
  transform: scale(0.95) translateY(-15px) rotateX(-5deg);
}

// 减少动画偏好设置
@media (prefers-reduced-motion: reduce) {

  .certificate-fade-enter-active,
  .certificate-fade-leave-active,
  .certificate-scale-enter-active,
  .certificate-scale-leave-active {
    transition: none;
  }

  .certificate-close-btn {
    transition: none;
  }

  .certificate-skeleton {
    animation: none;
  }

  .skeleton-image,
  .skeleton-hint {
    animation: none;
    background: #f0f0f0;
  }
}
</style>
