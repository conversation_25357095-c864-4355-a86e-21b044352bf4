<!--
/**
 * 通用订单项组件
 *
 * 主要功能：
 * 1. 展示单个订单的完整信息，包括订单号、状态、商品列表等
 * 2. 支持响应式按钮布局，根据屏幕尺寸智能调整按钮显示
 * 3. 集成订单倒计时功能，实时显示待付款订单的剩余时间
 * 4. 区分普通商城和政企业务，显示不同的头部信息
 * 5. 提供订单操作按钮，支持多种订单操作功能
 * 6. 支持订单号复制功能，方便用户操作
 *
 * 响应式设计：
 * - 大屏幕（>480px）：显示所有按钮
 * - 中屏幕（350-480px）：最多显示3个按钮
 * - 小屏幕（<350px）：最多显示2个按钮
 * - 超出的按钮自动归入"更多操作"
 *
 * 按钮优先级：
 * - 非gray按钮优先显示
 * - 特殊业务按钮（如荣誉证书）必须显示
 * - gray按钮作为补充显示
 *
 * 技术特点：
 * - 使用toRefs保持props的响应性
 * - 集成屏幕尺寸监听，实现响应式布局
 * - 采用计算属性优化按钮分配逻辑
 * - 支持多种业务场景的差异化显示
 *
 * 使用场景：
 * - 订单列表页面的订单项展示
 * - 搜索结果页面的订单项展示
 * - 回收站页面的订单项展示
 */
-->

<template>
  <!-- 通用订单项主容器 -->
  <article class="common-order-item">
    <WoCard>
      <!-- 订单头部信息区域 -->
      <header class="common-order-item__header">
        <!-- 订单号/企业信息区域 -->
        <div class="common-order-item__number-wrapper">
          <!-- 政企业务显示企业信息 -->
          <template v-if="isZQ">
            <img src="../../../../assets/images/enterprise_icon.png" alt="企业图标"
              class="common-order-item__enterprise-icon" />
            <span class="common-order-item__supplier-name">{{ order.supplier.name }}</span>
          </template>
          <!-- 普通商城显示订单号 -->
          <template v-else>
            <span class="common-order-item__number-text">订单号：{{ order.id }}</span>
            <!-- 订单号复制按钮 -->
            <img src="../../../../static/images/copy.png" alt="复制" class="common-order-item__copy-icon"
              @click.stop="handleCopyOrder" />
          </template>
        </div>

        <!-- 订单状态/倒计时区域 -->
        <!-- 待付款订单显示倒计时 -->
        <OrderCountdown v-if="order.orderState === '0' && order.remainingTime > 0"
          :remaining-time="order.remainingTime" />

        <!-- 其他状态显示状态文字 -->
        <div v-else class="common-order-item__status" :class="getStatusClass(order.orderState)">
          {{ orderState(order.orderState) }}
        </div>
      </header>

      <!-- 订单商品信息区域 -->
      <section class="common-order-item__goods">
        <!-- 订单商品卡片组件 -->
        <OrderGoodsCard
          :key="order.id"
          :item="order"
          :image-size="75"
          :min-height="110"
          :showActions="true"
          :moreActions="computedMoreActions"
          :itemId="order.id"
          @click="handleDetailClick">
          <!-- 操作按钮插槽 -->
          <template #actions>
            <!-- 按钮容器，支持响应式布局 -->
            <div ref="buttonsContainerRef" class="buttons-container">
              <!-- 遍历显示计算后的可见按钮 -->
              <WoButton
                v-for="button in computedVisibleButtons"
                :key="button.text"
                :type="getButtonType(button.color)"
                size="small"
                @click="button.handler">
                {{ button.text }}
              </WoButton>
            </div>
          </template>
        </OrderGoodsCard>
      </section>
    </WoCard>
  </article>
</template>

<script setup>
import { toRefs, ref, computed, onMounted, onUnmounted } from 'vue'

import WoCard from '@components/WoElementCom/WoCard.vue'
import OrderGoodsCard from '@components/GoodsListCommon/OrderGoodsCard.vue'
import WoButton from '@components/WoElementCom/WoButton/WoButton.vue'
import OrderCountdown from './OrderCountdown.vue'

import orderState from '@utils/orderState.js'
import { useOrderButtons } from '../composables/useOrderButtons.js'
import { getBizCode } from '@utils/curEnv.js'

// ==================== 组件属性定义 ====================
const props = defineProps({
  order: {
    type: Object,
    required: true
  },
  visibleButtons: {
    type: Array,
    default: () => []
  },
  moreActions: {
    type: Array,
    default: () => []
  }
})

// 使用toRefs保持props的响应性
const { order, visibleButtons: originalVisibleButtons, moreActions: originalMoreActions } = toRefs(props)

// ==================== 事件定义 ====================
const emit = defineEmits(['detail-click', 'copy-order'])

// ==================== 业务类型判断 ====================
// 判断是否为政企业务
const isZQ = computed(() => getBizCode() === 'zq')

// ==================== 按钮样式功能 ====================
// 获取按钮样式和状态样式的工具函数
const { getButtonType, getStatusClass } = useOrderButtons()

// ==================== 响应式布局管理 ====================
// 按钮容器DOM引用
const buttonsContainerRef = ref(null)
// 屏幕宽度状态
const screenWidth = ref(window.innerWidth)

// 监听屏幕宽度变化的处理函数
const updateScreenWidth = () => {
  screenWidth.value = window.innerWidth
}

// ==================== 生命周期管理 ====================
// 组件挂载时添加屏幕尺寸监听
onMounted(() => {
  window.addEventListener('resize', updateScreenWidth)
})

// 组件卸载时移除屏幕尺寸监听
onUnmounted(() => {
  window.removeEventListener('resize', updateScreenWidth)
})

// ==================== 按钮管理逻辑 ====================
// 按钮排序函数，根据优先级排序
const sortButtonsByPriority = (buttons, prioritizeNonGray = true) => {
  return buttons.sort((a, b) => {
    const aIsGray = a.color === 'gray'
    const bIsGray = b.color === 'gray'

    if (prioritizeNonGray) {
      // 优先显示非gray按钮
      if (aIsGray && !bIsGray) return 1
      if (!aIsGray && bIsGray) return -1
    } else {
      // 优先显示gray按钮
      if (aIsGray && !bIsGray) return -1
      if (!aIsGray && bIsGray) return 1
    }
    return 0
  })
}

// 根据屏幕宽度获取最大可见按钮数量
const getMaxVisibleCount = (screenWidth) => {
  if (screenWidth >= 350 && screenWidth <= 480) return 3  // 中屏幕显示3个
  return 2  // 小屏幕默认显示2个
}

// 判断按钮是否必须显示
const isMustShowButton = (button, bizCode) => {
  // 非gray按钮必须显示
  if (button.color !== 'gray') return true

  // 扶贫业务时，荣誉证书必须显示
  if (bizCode === 'fupin' && button.text === '荣誉证书') return true

  return false
}

// ==================== 按钮分配计算属性 ====================
// 计算最终显示的可见按钮
const computedVisibleButtons = computed(() => {
  // 大屏幕时，直接返回排序后的原始按钮
  if (screenWidth.value > 480) {
    return sortButtonsByPriority([...originalVisibleButtons.value])
  }

  // 小屏幕时的响应式逻辑
  const bizCode = getBizCode()
  const maxVisibleCount = getMaxVisibleCount(screenWidth.value)

  // 合并并排序所有按钮
  const allButtons = [...originalVisibleButtons.value, ...originalMoreActions.value]
  const sortedButtons = sortButtonsByPriority([...allButtons], false)

  // 找出必须显示的按钮
  const mustShowButtons = sortedButtons.filter(button =>
    isMustShowButton(button, bizCode)
  )

  // 如果必须显示的按钮已经足够，直接返回前N个
  if (mustShowButtons.length >= maxVisibleCount) {
    return sortButtonsByPriority(mustShowButtons.slice(0, maxVisibleCount), false)
  }

  // 补充gray按钮填满剩余位置
  const remainingSlots = maxVisibleCount - mustShowButtons.length
  const availableGrayButtons = sortedButtons.filter(button =>
    button.color === 'gray' && !isMustShowButton(button, bizCode)
  )

  const finalButtons = [
    ...mustShowButtons,
    ...availableGrayButtons.slice(0, remainingSlots)
  ]

  return sortButtonsByPriority(finalButtons, false)
})

// 计算更多操作中的按钮
const computedMoreActions = computed(() => {
  // 大屏幕时，使用原始分配
  if (screenWidth.value > 480) {
    return originalMoreActions.value
  }

  // 小屏幕时，计算剩余按钮
  const bizCode = getBizCode()
  const allButtons = [...originalVisibleButtons.value, ...originalMoreActions.value]
  const sortedButtons = sortButtonsByPriority([...allButtons])

  // 获取已显示按钮的文本集合
  const visibleButtonTexts = new Set(
    computedVisibleButtons.value.map(btn => btn.text)
  )

  // 过滤出剩余的gray按钮
  return sortedButtons
    .filter(button => {
      // 排除已显示的按钮
      if (visibleButtonTexts.has(button.text)) return false

      // 只保留gray按钮
      if (button.color !== 'gray') return false

      // 扶贫业务的荣誉证书不放在更多操作中
      if (bizCode === 'fupin' && button.text === '荣誉证书') return false

      return true
    })
    .map(item => ({ ...item, color: 'gray' }))
})

// ==================== 事件处理函数 ====================
// 处理订单详情点击事件
const handleDetailClick = () => {
  emit('detail-click', order.value)
}

// 处理订单号复制事件
const handleCopyOrder = () => {
  emit('copy-order', order.value.id)
}
</script>

<style scoped lang="less">
.common-order-item {
  margin-bottom: 10px;

  &:last-child {
    margin-bottom: 0;
  }

  &__header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 11px;
  }

  &__number-wrapper {
    display: flex;
    align-items: center;
    margin-right: 15px;
    width: 100%;
    overflow: hidden;
  }

  &__enterprise-icon {
    width: 16px;
    height: 16px;
    margin-right: 6px;
    flex-shrink: 0;
  }

  &__supplier-name {
    font-size: 13px;
    color: #171E24;
    font-weight: 500;
    margin-right: 3px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    ;
  }

  &__number-text {
    font-size: 11px;
    color: #4A5568;
    margin-right: 3px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    ;
  }

  &__copy-icon {
    width: 10px;
    height: 10px;
    cursor: pointer;
  }

  &__status {
    flex-shrink: 0;
    font-size: 14px;
    font-weight: 600;

    &.order-status--unpaid {
      color: var(--wo-biz-theme-color);
    }

    &.order-status--completed {
      color: #4A5568;
    }
  }

  &__goods {
    margin-bottom: 10px;

    &:last-child {
      margin-bottom: 0;
    }
  }
}

.buttons-container {
  display: flex;
  gap: 8px;
  width: 100%;
  overflow: hidden;
}
</style>
