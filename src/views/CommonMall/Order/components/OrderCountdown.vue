<!--
/**
 * 订单倒计时组件
 *
 * 主要功能：
 * 1. 显示订单的剩余时间倒计时，主要用于待付款订单
 * 2. 将毫秒时间戳格式化为易读的时分秒格式
 * 3. 提供统一的倒计时样式，保持界面一致性
 * 4. 支持时间为0或负数时的安全处理
 *
 * 时间格式：
 * - 显示格式：HH:MM:SS（时:分:秒）
 * - 自动补零，确保两位数显示
 * - 时间耗尽时显示 00:00:00
 *
 * 技术特点：
 * - 使用计算属性实现响应式时间格式化
 * - 采用CSS变量支持主题色定制
 * - 轻量级设计，性能优化
 *
 * 使用场景：
 * - 待付款订单的支付倒计时
 * - 订单列表中的时间提醒
 * - 订单详情页的时间显示
 */
-->

<template>
  <!-- 订单倒计时容器 -->
  <div class="order-countdown">
    <!-- 倒计时标签 -->
    <span class="order-countdown__label">剩余</span>
    <!-- 格式化的倒计时时间 -->
    <span class="order-countdown__time">{{ formattedTime }}</span>
  </div>
</template>

<script setup>
import { computed } from 'vue'

// ==================== 组件属性定义 ====================
const props = defineProps({
  remainingTime: {
    type: Number,      // 剩余时间，单位：毫秒
    required: true
  }
})

// ==================== 时间格式化计算属性 ====================
// 将毫秒时间戳格式化为 HH:MM:SS 格式
const formattedTime = computed(() => {
  // 时间耗尽或为负数时返回默认值
  if (props.remainingTime <= 0) return '00:00:00'

  // 转换为总秒数
  const totalSeconds = Math.floor(props.remainingTime / 1000)
  // 计算小时数
  const hours = Math.floor(totalSeconds / 3600)
  // 计算分钟数
  const minutes = Math.floor((totalSeconds % 3600) / 60)
  // 计算秒数
  const seconds = totalSeconds % 60

  // 格式化为两位数并拼接
  return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`
})
</script>

<style scoped lang="less">
.order-countdown {
  display: flex;
  align-items: center;
  flex-shrink: 0;
  color: var(--wo-biz-theme-color);

  &__label {
    font-size: 14px;
    font-weight: 600;
    margin-right: 4px;
    color: var(--wo-biz-theme-color);
  }

  &__time {
    font-size: 14px;
    font-weight: 600;
    color: var(--wo-biz-theme-color);
  }
}
</style>
