<!--
/**
 * 订单回收站页面组件
 *
 * 主要功能：
 * 1. 展示用户已删除的订单列表，提供订单回收站管理功能
 * 2. 支持订单还原功能，将删除的订单恢复到正常订单列表
 * 3. 提供永久删除功能，彻底删除不需要的订单
 * 4. 集成订单详情查看，支持跳转到订单详情页面
 * 5. 实现订单号复制功能，方便用户操作
 * 6. 支持无限滚动加载，分页展示大量删除订单
 * 7. 提供滚动位置记忆，页面切换时保持浏览位置
 *
 * 功能限制：
 * - 已删除订单无法申请售后
 * - 需要先还原订单才能进行其他操作
 *
 * 技术特点：
 * - 使用van-list实现无限滚动加载
 * - 集成骨架屏和空状态提升用户体验
 * - 使用节流优化滚动事件处理
 * - 支持页面激活/失活时的状态保存和恢复
 * - 采用组件化设计，职责分离清晰
 *
 * 使用场景：
 * - 用户管理已删除的订单
 * - 订单误删后的恢复操作
 * - 清理不需要的历史订单
 */
-->

<template>
  <!-- 订单回收站页面主容器 -->
  <div class="recycle-bin">
    <!-- 功能提示区域 -->
    <!-- 告知用户已删除订单的操作限制 -->
    <div class="recycle-bin__tip">
      已删除订单无法申请售后，如需操作请先还原订单
    </div>

    <!-- 回收站内容区域 -->
    <div class="recycle-bin__content" ref="contentRef">
      <!-- 加载骨架屏，在初始加载时显示 -->
      <RecycleBinSkeleton v-if="showSkeleton"/>

      <!-- 空状态组件，当没有删除订单时显示 -->
      <RecycleBinEmptyState
        v-if="!showSkeleton && !loading && orderList.length === 0 && finished"
        :empty-image="noDataImage"
      />

      <!-- 订单列表组件，支持无限滚动加载 -->
      <van-list
        v-else
        v-model:loading="loading"
        :finished="finished"
        finished-text="没有更多了"
        @load="onLoad"
        :immediate-check="false"
      >
        <!-- 遍历显示每个删除的订单 -->
        <RecycleBinOrderItem
          v-for="order in orderList"
          :key="order.id"
          :order-data="order"
          :is-deleting="order.isDeleting"
          @copy-order="copyOrderNumber"
          @detail-click="onDetailClick"
          @permanent-delete="handlePermanentDelete"
          @restore-order="handleRestoreOrder"
        />
      </van-list>
    </div>
  </div>
</template>
<script setup>
import { ref, onMounted, onUnmounted, nextTick, onActivated, onDeactivated, computed } from 'vue'
import { throttle } from 'lodash-es'
import useClipboard from 'vue-clipboard3'
import { showLoadingToast, showToast, closeToast } from 'vant'
import { useRouter } from "vue-router"

import RecycleBinSkeleton from './components/RecycleBinSkeleton.vue'
import RecycleBinEmptyState from './components/RecycleBinEmptyState.vue'
import RecycleBinOrderItem from './components/RecycleBinOrderItem.vue'

import { getOrderRecycleBinList, modOrderListShow } from '@api/index.js'
import { getBizCode } from '@utils/curEnv.js'
import { useAlert } from '@/composables/index.js'

// ==================== 核心工具和依赖实例 ====================
// 剪贴板操作工具
const { toClipboard } = useClipboard()
// 弹窗提示工具实例
const $alert = useAlert()
// 路由导航实例
const router = useRouter()

// ==================== DOM引用管理 ====================
// 内容区域DOM引用
const contentRef = ref(null)

// ==================== 列表加载状态管理 ====================
// 列表加载状态
const loading = ref(false)
// 是否已加载完所有数据
const finished = ref(false)
// 显示骨架屏状态
const showSkeleton = ref(true)
// 数据加载锁，防止重复请求
const onLoadGetDataLock = ref(false)
// 加载完成提示文字
const finishedText = ref('没有更多了')

// ==================== 订单数据管理 ====================
// 订单列表数据
const orderList = ref([])
// 当前页码
const currentPage = ref(1)
// 每页数据量
const pageSize = ref(10)
// 总页数
const totalPage = ref(0)

// ==================== 滚动位置管理 ====================
// 滚动位置缓存
const scrollPositions = ref({
  all: 0
})

// ==================== 静态资源计算属性 ====================
// 空状态图片路径
const noDataImage = computed(() => '../assets/no-data.png')

// ==================== 滚动位置功能 ====================
// 更新滚动位置缓存
const updateScrollPosition = (position) => {
  scrollPositions.value.all = position
}

// 滚动事件处理函数，使用节流优化性能
const handleScroll = throttle(() => {
  if (contentRef.value) {
    const scrollTop = contentRef.value.scrollTop || document.documentElement.scrollTop || document.body.scrollTop
    updateScrollPosition(scrollTop)
  }
}, 100)

// ==================== 用户交互功能 ====================
// 复制订单号功能
const copyOrderNumber = async (orderNumber) => {
  try {
    await toClipboard(orderNumber)
    showToast('复制成功')
  } catch (error) {
    showToast('复制失败')
  }
}

// 订单详情点击处理
const onDetailClick = (order) => {
  router.push({
    path: '/user/order/detail',
    query: {
      orderId: order.id,
      isPay: order.orderState === '0' ? '1' : '2'
    }
  })
}

// ==================== 订单操作功能 ====================
// 永久删除订单处理
const handlePermanentDelete = async (order) => {
  try {
    // 执行删除操作的内部函数
    const cancelOrderFn = async () => {
      showLoadingToast()
      const params = {
        supplierOrderId: order.id,
        isDelete: 2  // 2表示永久删除
      }
      const [err] = await modOrderListShow(params)
      closeToast()

      if (!err) {
        showToast('订单删除成功!')
        // 找到订单在列表中的位置
        const index = orderList.value.findIndex(item => item.id === order.id)
        if (index !== -1) {
          // 设置删除动画状态
          orderList.value[index].isDeleting = true
          // 延迟移除，等待动画完成
          setTimeout(() => {
            orderList.value.splice(index, 1)
            // 如果列表为空，清空完成提示文字
            if (orderList.value.length === 0) {
              finishedText.value = ''
            }
          }, 500)
        }
      } else {
        showToast(err.msg || '删除失败')
      }
    }

    // 显示确认删除弹窗
    $alert({
      title: '',
      message: '永久删除后，您将无法查看及还原订单，确认继续删除吗？',
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      showCancelButton: true,
      onConfirmCallback: async () => {
        await cancelOrderFn()
      }
    })
  } catch (error) {
    if (error !== 'cancel') {
      showToast('删除失败')
    }
  }
}

// 还原订单处理
const handleRestoreOrder = async (order) => {
  try {
    showLoadingToast()
    const params = {
      supplierOrderId: order.id,
      isDelete: 0  // 0表示还原订单
    }
    const [err] = await modOrderListShow(params)
    closeToast()

    if (!err) {
      showToast('订单已成功还原至"我的订单"')
      // 设置还原标识，用于其他页面判断是否需要刷新
      window.sessionStorage.setItem('woRecycleBinRestore', 1)

      // 找到订单在列表中的位置
      const index = orderList.value.findIndex(item => item.id === order.id)
      if (index !== -1) {
        // 设置删除动画状态
        orderList.value[index].isDeleting = true
        // 延迟移除，等待动画完成
        setTimeout(() => {
          orderList.value.splice(index, 1)
          // 如果列表为空，清空完成提示文字
          if (orderList.value.length === 0) {
            finishedText.value = ''
          }
        }, 500)
      }
    } else {
      showToast(err.msg || '还原失败')
    }
  } catch (error) {
    closeToast()
    showToast('还原失败')
  }
}

// ==================== 数据加载功能 ====================
// 加载回收站订单数据
const onLoad = async () => {
  // 防止重复请求
  if (onLoadGetDataLock.value) {
    return
  }

  // 构建请求参数
  const params = {
    bizCode: getBizCode('ORDER'),
    pageNo: currentPage.value,
    pageSize: pageSize.value
  }

  // 设置加载锁
  onLoadGetDataLock.value = true

  // 请求回收站数据
  const [err, json] = await getOrderRecycleBinList(params)

  // 释放加载锁并更新状态
  onLoadGetDataLock.value = false
  loading.value = false
  showSkeleton.value = false

  if (!err) {
    // 页码递增，准备下次加载
    currentPage.value++

    // 设置完成提示文字
    if (json?.list.length > 0) {
      finishedText.value = '没有更多了'
    }

    // 如果没有数据，标记为加载完成
    if (json && json?.list.length <= 0) {
      finished.value = true
      return
    }

    // 追加新数据到订单列表
    orderList.value = [...orderList.value, ...json.list]

    // 更新总页数并检查是否加载完成
    totalPage.value = json.totalPage
    if (currentPage.value > totalPage.value) {
      finished.value = true
    }

    // 恢复滚动位置（仅在第二页加载时）
    if (currentPage.value === 2 && scrollPositions.value.all > 0) {
      nextTick(() => {
        if (contentRef.value) {
          contentRef.value.scrollTop = scrollPositions.value.all
        }
      })
    }
  } else {
    showToast(err.msg || '获取回收站数据失败')
  }
}

// ==================== 生命周期管理 ====================
// 组件挂载时初始化滚动监听和数据加载
onMounted(() => {
  // 添加滚动事件监听器
  if (contentRef.value) {
    contentRef.value.addEventListener('scroll', handleScroll)
  } else {
    window.addEventListener('scroll', handleScroll)
  }
  // 初始化加载数据
  onLoad()
})

// 组件卸载时清理滚动监听器
onUnmounted(() => {
  if (contentRef.value) {
    contentRef.value.removeEventListener('scroll', handleScroll)
  } else {
    window.removeEventListener('scroll', handleScroll)
  }
})

// 页面激活时恢复滚动位置
onActivated(() => {
  nextTick(() => {
    if (contentRef.value && scrollPositions.value.all > 0) {
      contentRef.value.scrollTop = scrollPositions.value.all
    }
  })
})

// 页面失活时保存滚动位置
onDeactivated(() => {
  if (contentRef.value) {
    scrollPositions.value.all = contentRef.value.scrollTop
  }
})
</script>

<style scoped lang="less">
.recycle-bin {
  height: 100vh;
  display: flex;
  flex-direction: column;

  &__tip {
    background: #FEFAE9;
    color: var(--wo-biz-theme-color);
    padding: 10px 22px;
    font-size: 12px;
    box-sizing: border-box;
  }

  &__content {
    flex: 1;
    overflow: auto;
    background-color: #F8F9FA;
    padding: 10px;
  }
}
</style>
