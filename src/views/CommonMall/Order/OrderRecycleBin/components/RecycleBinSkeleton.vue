<!--
/**
 * 回收站订单骨架屏组件
 *
 * 主要功能：
 * 1. 在回收站订单数据加载时显示骨架屏效果，提升用户体验
 * 2. 模拟回收站订单项的完整布局结构，包括订单号、状态、商品信息、操作按钮等
 * 3. 提供流畅的加载动画效果，减少用户等待时的焦虑感
 * 4. 与真实回收站订单项保持一致的尺寸和布局，确保无缝切换
 * 5. 采用静态骨架设计，专门适配回收站页面的加载需求
 *
 * 技术特点：
 * - 使用CSS背景色模拟内容加载状态
 * - 采用固定的骨架项数量，适配回收站页面特点
 * - 响应式设计，适配不同屏幕尺寸
 * - 无需JavaScript逻辑，纯CSS实现
 * - 模块化设计，便于维护和扩展
 *
 * 使用场景：
 * - 回收站页面数据加载时的占位显示
 * - 任何需要回收站订单加载状态的页面
 */
-->

<template>
  <!-- 回收站骨架屏主容器 -->
  <div class="recycle-bin-skeleton">
    <!-- 循环生成3个订单项骨架 -->
    <div v-for="i in 3" :key="`skeleton-${i}`" class="recycle-bin-skeleton__item">
      <!-- 订单卡片容器 -->
      <WoCard>
        <!-- 订单内容骨架 -->
        <div class="recycle-bin-skeleton__content">
          <!-- 订单头部信息骨架 -->
          <div class="recycle-bin-skeleton__header">
            <!-- 订单号骨架线 -->
            <div class="recycle-bin-skeleton__order-number"></div>
            <!-- 订单状态骨架线 -->
            <div class="recycle-bin-skeleton__status"></div>
          </div>
          <!-- 商品信息区域骨架 -->
          <div class="recycle-bin-skeleton__goods">
            <!-- 商品图片骨架 -->
            <div class="recycle-bin-skeleton__image"></div>
            <!-- 商品信息骨架 -->
            <div class="recycle-bin-skeleton__info">
              <!-- 商品标题骨架线 -->
              <div class="recycle-bin-skeleton__title"></div>
              <!-- 商品副标题骨架线 -->
              <div class="recycle-bin-skeleton__subtitle"></div>
              <!-- 商品价格骨架线 -->
              <div class="recycle-bin-skeleton__price"></div>
            </div>
          </div>
          <!-- 操作按钮区域骨架 -->
          <div class="recycle-bin-skeleton__actions">
            <!-- 操作按钮骨架 -->
            <div class="recycle-bin-skeleton__button"></div>
            <div class="recycle-bin-skeleton__button"></div>
          </div>
        </div>
      </WoCard>
    </div>
  </div>
</template>

<script setup>
import WoCard from '@components/WoElementCom/WoCard.vue'

// ==================== 回收站骨架屏组件 ====================
// 纯展示组件，无需任何逻辑处理
</script>

<style scoped lang="less">
.recycle-bin-skeleton {
  &__item {
    margin-bottom: 10px;
  }

  &__content {
    padding: 15px;
  }

  &__header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
  }

  &__order-number {
    width: 120px;
    height: 14px;
    background: #f0f0f0;
    border-radius: 4px;
  }

  &__status {
    width: 60px;
    height: 14px;
    background: #f0f0f0;
    border-radius: 4px;
  }

  &__goods {
    display: flex;
    margin-bottom: 15px;
  }

  &__image {
    width: 75px;
    height: 75px;
    background: #f0f0f0;
    border-radius: 8px;
    margin-right: 12px;
    flex-shrink: 0;
  }

  &__info {
    flex: 1;
  }

  &__title {
    width: 80%;
    height: 16px;
    background: #f0f0f0;
    border-radius: 4px;
    margin-bottom: 8px;
  }

  &__subtitle {
    width: 60%;
    height: 14px;
    background: #f0f0f0;
    border-radius: 4px;
    margin-bottom: 8px;
  }

  &__price {
    width: 40%;
    height: 14px;
    background: #f0f0f0;
    border-radius: 4px;
  }

  &__actions {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
  }

  &__button {
    width: 60px;
    height: 28px;
    background: #f0f0f0;
    border-radius: 4px;
  }
}
</style>
