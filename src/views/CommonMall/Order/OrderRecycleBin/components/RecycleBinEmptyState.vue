<!--
/**
 * 订单回收站空状态组件
 *
 * 主要功能：
 * 1. 展示订单回收站为空时的占位状态，提供友好的用户体验
 * 2. 支持自定义空状态图片，提供灵活的视觉配置选项
 * 3. 使用图形化方式告知用户当前回收站没有已删除的订单
 * 4. 提供统一的空状态视觉设计，保持界面一致性
 * 5. 支持响应式布局，适配不同屏幕尺寸的显示效果
 * 6. 采用语义化HTML结构，提升可访问性和用户体验
 *
 * 技术特点：
 * - 使用props配置实现图片自定义
 * - 采用Flexbox布局实现居中对齐
 * - 集成透明度效果增强视觉层次
 * - 使用toRefs保持props响应性
 *
 * 使用场景：
 * - 订单回收站页面无数据时的占位显示
 * - 用户清空回收站后的状态提示
 * - 首次进入回收站时的引导界面
 */
-->

<template>
  <!-- 回收站空状态主容器 -->
  <div class="recycle-bin-empty">
    <!-- 空状态内容区域 -->
    <div class="recycle-bin-empty__content">
      <!-- 空状态插图，支持自定义图片路径 -->
      <!-- 使用半透明效果突出空状态的视觉特征 -->
      <img :src="emptyImage" alt="暂无订单" class="recycle-bin-empty__image" />
    </div>
  </div>
</template>

<script setup>
import { toRefs } from 'vue'

// ==================== 组件属性定义 ====================
// 定义组件接收的props参数
const props = defineProps({
  // 空状态图片路径，支持自定义空状态插图
  emptyImage: {
    type: String,
    default: '../assets/no-data.png'
  }
})

// 使用toRefs解构props，保持响应性
const { emptyImage } = toRefs(props)
</script>

<style scoped lang="less">
.recycle-bin-empty {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 300px;

  &__content {
    text-align: center;
  }

  &__image {
    width: 120px;
    height: 120px;
    opacity: 0.6;
  }
}
</style>
