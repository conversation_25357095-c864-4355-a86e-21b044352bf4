<!--
/**
 * 回收站订单项组件
 *
 * 主要功能：
 * 1. 展示回收站中单个订单的详细信息，包括订单号、状态、商品信息等
 * 2. 提供订单号复制功能，方便用户在其他平台查询订单
 * 3. 支持订单详情查看，点击可跳转到订单详情页面
 * 4. 提供订单还原功能，将删除的订单恢复到正常状态
 * 5. 支持永久删除功能，彻底删除不需要的订单
 * 6. 集成删除状态显示，在执行删除操作时提供视觉反馈
 * 7. 使用商品卡片组件统一展示商品信息
 *
 * 技术特点：
 * - 使用计算属性优化数据处理和按钮配置
 * - 集成事件发射机制处理各种用户操作
 * - 采用条件样式类实现删除状态的视觉效果
 * - 使用toRefs保持props响应性
 * - 支持按钮类型映射，统一按钮样式
 *
 * 使用场景：
 * - 订单回收站页面的订单项展示
 * - 已删除订单的管理和操作
 * - 订单恢复和永久删除功能
 */
-->

<template>
  <!-- 回收站订单项主容器 -->
  <!-- 根据删除状态添加对应的CSS类 -->
  <div class="recycle-bin-order-item" :class="{ 'recycle-bin-order-item--deleting': isDeleting }">
    <!-- 订单卡片容器 -->
    <WoCard>
      <!-- 订单头部信息区域 -->
      <div class="recycle-bin-order-item__header">
        <!-- 订单基本信息区域 -->
        <div class="recycle-bin-order-item__order-info">
          <!-- 订单号显示 -->
          <span class="recycle-bin-order-item__order-number">订单号：{{ orderId }}</span>
          <!-- 复制订单号图标 -->
          <img
            src="../../../../../static/images/copy.png"
            alt="复制"
            class="recycle-bin-order-item__copy-icon"
            @click.stop="handleCopyOrder"
          />
        </div>
        <!-- 订单状态显示 -->
        <div class="recycle-bin-order-item__status" :class="statusClass">{{ statusText }}</div>
      </div>
      <!-- 商品信息区域 -->
      <div class="recycle-bin-order-item__goods">
        <!-- 使用商品卡片组件展示商品信息 -->
        <OrderGoodsCard
          :key="orderId"
          :item="orderData"
          :image-size="75"
          :min-height="110"
          :showActions="true"
          :itemId="orderId"
          :showMore="false"
          @click="handleDetailClick"
        >
          <!-- 操作按钮插槽 -->
          <template #actions>
            <!-- 遍历显示操作按钮 -->
            <WoButton
              v-for="button in actionButtons"
              :key="button.text"
              :type="getButtonType(button.color)"
              size="small"
              @click="button.handler"
            >
              {{ button.text }}
            </WoButton>
          </template>
        </OrderGoodsCard>
      </div>
    </WoCard>
  </div>
</template>

<script setup>
import { toRefs, computed } from 'vue'
import WoCard from '@components/WoElementCom/WoCard.vue'
import OrderGoodsCard from '@components/GoodsListCommon/OrderGoodsCard.vue'
import WoButton from '@components/WoElementCom/WoButton/WoButton.vue'

// ==================== 组件属性定义 ====================
// 定义组件接收的props参数
const props = defineProps({
  // 订单数据对象，包含订单的完整信息
  orderData: {
    type: Object,
    required: true
  },
  // 是否正在删除状态，用于显示删除中的视觉效果
  isDeleting: {
    type: Boolean,
    default: false
  }
})

// 定义组件向父组件发射的事件
const emit = defineEmits(['copy-order', 'detail-click', 'permanent-delete', 'restore-order'])

// 使用toRefs解构props，保持响应性
const { orderData, isDeleting } = toRefs(props)

// ==================== 订单信息计算属性 ====================
// 订单ID，从订单数据中提取
const orderId = computed(() => orderData.value.id)
// 订单状态文字，用于显示订单当前状态
const statusText = computed(() => orderData.value.statusText)
// 订单状态样式类，用于设置状态显示的样式
const statusClass = computed(() => orderData.value.statusClass)

// ==================== 操作按钮配置 ====================
// 操作按钮配置数组，定义回收站订单的可用操作
const actionButtons = computed(() => [
  {
    text: '永久删除',
    color: 'default',
    handler: () => emit('permanent-delete', orderData.value)
  },
  {
    text: '还原订单',
    color: 'primary',
    handler: () => emit('restore-order', orderData.value)
  }
])

// ==================== 工具函数 ====================
// 按钮类型映射函数，将颜色配置转换为按钮组件的type属性
const getButtonType = (color) => {
  const typeMap = {
    primary: 'gradient',
    default: 'default'
  }
  return typeMap[color] || 'default'
}

// ==================== 事件处理函数 ====================
// 复制订单号处理函数
// 向父组件发射copy-order事件并传递订单ID
const handleCopyOrder = () => {
  emit('copy-order', orderId.value)
}

// 订单详情点击处理函数
// 向父组件发射detail-click事件并传递订单数据
const handleDetailClick = () => {
  emit('detail-click', orderData.value)
}
</script>

<style scoped lang="less">
.recycle-bin-order-item {
  margin-bottom: 10px;
  transition: all 0.5s ease;

  &--deleting {
    opacity: 0;
    transform: translateX(-100%);
  }

  &__header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 11px;
  }

  &__order-info {
    display: flex;
    align-items: center;
    margin-right: 15px;
    width: 100%;
    overflow: hidden;
  }

  &__order-number {
    font-size: 11px;
    color: #4A5568;
    margin-right: 3px;
    overflow: hidden; text-overflow: ellipsis; white-space: nowrap;;
  }

  &__copy-icon {
    width: 10px;
    height: 10px;
    cursor: pointer;
  }

  &__status {
    flex-shrink: 0;
    font-size: 14px;
    font-weight: 600;

    &.status-unpaid {
      color: var(--wo-biz-theme-color);
    }

    &.status-unshipped {
      color: #2196f3;
    }

    &.status-shipped {
      color: #4caf50;
    }

    &.status-completed {
      color: #4A5568;
    }
  }

  &__goods {
    margin-bottom: 10px;

    &:last-child {
      margin-bottom: 0;
    }
  }
}
</style>
