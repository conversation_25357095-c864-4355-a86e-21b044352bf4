<!--
/**
 * 物流空状态组件
 *
 * 主要功能：
 * 1. 在没有任何物流信息时显示空状态提示
 * 2. 提供友好的用户体验，避免空白页面
 * 3. 使用图标和文字组合，清晰传达当前状态
 * 4. 支持无障碍访问，提供语义化标签
 * 5. 采用居中布局，适配不同屏幕尺寸
 *
 * 技术特点：
 * - 使用CSS背景图片实现图标显示
 * - 采用flex布局实现垂直居中对齐
 * - 支持响应式设计，适配移动端
 * - 无需JavaScript逻辑，纯展示组件
 *
 * 使用场景：
 * - 订单没有任何物流信息时的占位显示
 * - 物流查询失败或数据为空时的友好提示
 */
-->

<template>
  <!-- 物流空状态主容器 -->
  <section class="express-empty">
    <!-- 空状态内容容器 -->
    <div class="express-empty__container">
      <!-- 空状态图标，使用背景图片显示 -->
      <div class="express-empty__icon" role="img" aria-label="暂无数据"></div>
      <!-- 空状态提示文字 -->
      <p class="express-empty__text">暂无消息~</p>
    </div>
  </section>
</template>

<script setup>
// ==================== 物流空状态组件 ====================
// 纯展示组件，无需任何逻辑处理
</script>

<style lang="less" scoped>
.express-empty {
  padding-left: 17px;
  text-align: center;
  background: #FFFFFF;
  box-sizing: border-box;

  &__container {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 20px;
  }

  &__icon {
    margin: 31px auto 20px;
    width: 208px;
    height: 161px;
    background: #FFFFFF url(../assets/empty.png) no-repeat center;
    background-size: contain;

    &::before {
      content: '';
      display: block;
      background-image: url('../assets/empty.png');
      width: 0;
      height: 0;
      opacity: 0;
    }
  }

  &__text {
    line-height: 1.5;
    font-size: 13px;
    color: #718096;
    text-align: center;
    margin: 0;
  }
}
</style>
