<!--
/**
 * 物流查询指引组件
 *
 * 主要功能：
 * 1. 在有快递信息但无跟踪数据时显示查询指引
 * 2. 提供第三方物流查询平台的链接，方便用户自助查询
 * 3. 给出查询建议和注意事项，提升用户体验
 * 4. 支持外部链接安全访问，使用noopener属性
 * 5. 采用清晰的文字层次，突出重要信息
 *
 * 技术特点：
 * - 使用语义化HTML标签组织内容
 * - 采用安全的外部链接处理方式
 * - 支持响应式设计，适配移动端
 * - 无需JavaScript逻辑，纯展示组件
 *
 * 使用场景：
 * - 物流单号已生成但第三方接口暂无数据时的指引
 * - 帮助用户了解如何自助查询物流信息
 */
-->

<template>
  <!-- 物流查询指引主容器 -->
  <section class="express-query-guide">
    <!-- 指引内容容器 -->
    <div class="express-query-guide__content">
      <!-- 查询指引主要文字 -->
      <p class="express-query-guide__text">
        您可通过复制物流单号，前往物流公司官网查询物流情况，也可快速访问"快递100"进行查询。快递100：
        <!-- 快递100查询链接 -->
        <a
          href="https://www.kuaidi100.com"
          target="_blank"
          rel="noopener"
          class="express-query-guide__link"
        >
          https://www.kuaidi100.com
        </a>
      </p>
      <!-- 查询注意事项 -->
      <p class="express-query-guide__note">
        (建议前往官方网站查询，当查询失效时，可检查单号是否填写正确)
      </p>
    </div>
  </section>
</template>

<script setup>
// ==================== 物流查询指引组件 ====================
// 纯展示组件，无需任何逻辑处理
</script>

<style lang="less" scoped>
.express-query-guide {
  padding: 10px 20px;
  background: #FFFFFF;
  box-sizing: border-box;

  &__content {
    position: relative;
    padding-bottom: 22px;
    font-size: 14px;
    line-height: 20px;
    text-align: left;
    color: #171E24;

    &::before {
      content: '';
      position: absolute;
      z-index: 2;
      left: -32px;
      display: block;
      width: 20px;
      height: 20px;
      background-size: 100% 100%;
    }
  }

  &__text {
    margin-bottom: 10px;
    line-height: 1.5;
  }

  &__note {
    line-height: 1.3;
    font-size: 13px;
    color: #718096;
  }

  &__link {
    text-decoration: underline;
    color: #171E24;
  }
}
</style>
