<!--
/**
 * 物流时间轴组件
 *
 * 主要功能：
 * 1. 展示物流跟踪记录的时间轴，按时间顺序显示物流状态变化
 * 2. 突出显示最新的物流状态，提供清晰的视觉层次
 * 3. 支持多种数据格式，兼容不同物流接口的返回结构
 * 4. 采用语义化时间标签，提升可访问性和SEO效果
 * 5. 提供流畅的时间轴视觉效果，增强用户体验
 *
 * 技术特点：
 * - 使用ol/li语义化标签组织时间轴结构
 * - 采用CSS伪元素实现时间轴连接线和节点
 * - 支持动态数据渲染，自动适配不同数量的记录
 * - 使用time标签标记时间信息，符合HTML5语义
 *
 * 使用场景：
 * - 物流详情页面的核心展示组件
 * - 任何需要时间轴展示的业务场景
 */
-->

<template>
  <!-- 物流时间轴主容器 -->
  <section class="express-timeline">
    <!-- 物流记录列表，使用有序列表语义化标签 -->
    <ol class="express-timeline__list">
      <!-- 遍历物流跟踪数据，生成时间轴项 -->
      <li
        class="express-timeline__item"
        v-for="(item, index) in trackingData"
        :key="`track-${index}-${item.msgTime || item.time}`"
        :class="{ 'express-timeline__item--active': index === 0 }"
      >
        <!-- 物流状态内容描述 -->
        <div class="express-timeline__content">{{ item.content || item.context }}</div>
        <!-- 物流状态时间，使用time标签提供语义化时间信息 -->
        <time class="express-timeline__time">{{ item.msgTime || item.time }}</time>
      </li>
    </ol>
  </section>
</template>

<script setup>
import { toRefs } from 'vue'

// ==================== 组件属性定义 ====================
// 定义组件接收的props参数
const props = defineProps({
  // 物流跟踪数据数组，包含物流状态变化的完整记录
  trackingData: {
    type: Array,
    default: () => []
  }
})

// 使用toRefs解构props，保持响应性
const { trackingData } = toRefs(props)
</script>

<style lang="less" scoped>
.express-timeline {
  padding: 15px 15px 15px 56px;
  width: 100%;
  background: #FFFFFF;
  box-sizing: border-box;
  contain: layout style;

  &__list {
    margin: 0;
    padding: 0;
    list-style: none;
    transform: translateZ(0);
  }

  &__item {
    position: relative;
    padding-bottom: 22px;
    font-size: 14px;
    line-height: 20px;
    text-align: left;
    color: #718096;

    &::before {
      content: '';
      position: absolute;
      z-index: 2;
      left: -32px;
      display: block;
      width: 8px;
      height: 8px;
      background-color: #CBD5E0;
      border: 2px solid #FFFFFF;
      border-radius: 50%;
    }

    &::after {
      content: '';
      position: absolute;
      z-index: 1;
      left: -26px;
      top: 0;
      display: block;
      width: 1px;
      height: 100%;
      background: #E2E8EE;
    }

    &--active {
      color: #171E24;

      &::before {
        left: -34px;
        width: 12px;
        height: 12px;
        background-color: var(--wo-biz-theme-color);
        border: 3px solid #ffd6b5;
      }
    }

    &:last-child::after {
      display: none;
    }
  }

  &__content {
    display: block;
    margin-bottom: 4px;
    word-break: break-word;
  }

  &__time {
    display: block;
    color: #718096;
    font-size: 12px;
  }
}
</style>
