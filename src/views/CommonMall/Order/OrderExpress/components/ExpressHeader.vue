<!--
/**
 * 物流信息头部组件
 *
 * 主要功能：
 * 1. 展示物流基本信息，包括订单号、承运商名称和运单号
 * 2. 提供运单号复制功能，方便用户在其他平台查询物流信息
 * 3. 采用语义化HTML结构，提升可访问性和SEO效果
 * 4. 支持运单号有效性检查，仅在有效时显示复制按钮
 * 5. 集成响应式设计，适配不同屏幕尺寸的显示效果
 * 6. 提供清晰的信息层次结构，便于用户快速获取关键信息
 *
 * 技术特点：
 * - 使用dl/dt/dd语义化标签组织信息
 * - 采用计算属性控制按钮显示逻辑
 * - 集成事件发射机制处理复制操作
 * - 使用toRefs保持props响应性
 *
 * 使用场景：
 * - 物流详情页面的头部信息展示
 * - 需要显示物流基本信息的业务场景
 */
-->

<template>
  <!-- 物流信息头部容器 -->
  <header class="express-header">
    <!-- 物流信息列表，使用语义化标签组织内容 -->
    <dl class="express-header__info-list">
      <!-- 订单号信息项 -->
      <div class="express-header__info-item">
        <!-- 订单号标签 -->
        <dt class="express-header__label">订单号：</dt>
        <!-- 订单号值 -->
        <dd class="express-header__value">{{ orderId }}</dd>
      </div>
      <!-- 承运商信息项 -->
      <div class="express-header__info-item">
        <!-- 承运商标签 -->
        <dt class="express-header__label">承运商：</dt>
        <!-- 承运商名称值 -->
        <dd class="express-header__value">{{ expressName }}</dd>
      </div>
      <!-- 运单号信息项 -->
      <div class="express-header__info-item">
        <!-- 运单号标签 -->
        <dt class="express-header__label">运单号：</dt>
        <!-- 运单号值和复制按钮容器 -->
        <dd class="express-header__value">
          <!-- 运单号文本显示 -->
          <span class="express-header__tracking-number">{{ expressNo }}</span>
          <!-- 复制按钮，仅在运单号有效时显示 -->
          <button
            v-if="shouldShowCopyButton"
            class="express-header__copy-btn"
            @click="handleCopy"
            aria-label="复制运单号"
          >
            复制
          </button>
        </dd>
      </div>
    </dl>
  </header>
</template>

<script setup>
import { computed, toRefs } from 'vue'

// ==================== 组件属性定义 ====================
// 定义组件接收的props参数
const props = defineProps({
  // 订单号，用于标识具体的订单
  orderId: {
    type: String,
    default: ''
  },
  // 承运商名称，显示负责配送的物流公司
  expressName: {
    type: String,
    default: '--'
  },
  // 运单号，用于在物流公司官网查询配送状态
  expressNo: {
    type: String,
    default: '--'
  }
})

// 使用toRefs解构props，保持响应性
const { orderId, expressName, expressNo } = toRefs(props)

// 定义组件向父组件发射的事件
const emit = defineEmits(['copy'])

// ==================== 计算属性 ====================
// 判断是否应该显示复制按钮
// 仅在运单号存在且不为默认占位符时显示
const shouldShowCopyButton = computed(() =>
  expressNo.value && expressNo.value !== '--'
)

// ==================== 事件处理函数 ====================
// 复制运单号处理函数
// 当用户点击复制按钮时触发，向父组件发射copy事件并传递运单号
const handleCopy = () => {
  emit('copy', expressNo.value)
}
</script>

<style lang="less" scoped>
.express-header {
  contain: layout style;
  padding: 10px;
  width: 100%;
  margin-bottom: 10px;
  line-height: 15px;
  border-bottom: 9px solid #F8F9FA;
  font-size: 13px;
  color: #171E24;
  box-sizing: border-box;

  &__info-list {
    margin: 0;
  }

  &__info-item {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    margin-bottom: 10px;
    line-height: 1.5;

    &:last-child {
      margin-bottom: 0;
    }
  }

  &__label {
    margin: 0;
    color: #171E24;
    font-weight: normal;
  }

  &__value {
    margin: 0;
    color: #171E24;
    display: flex;
    align-items: center;
  }

  &__tracking-number {
    margin-right: 10px;
  }

  &__copy-btn {
    margin-left: 10px;
    padding: 0;
    width: 48px;
    height: 23px;
    line-height: 23px;
    border: 1px solid var(--wo-biz-theme-color);
    border-radius: 2px;
    background: transparent;
    font-size: 13px;
    font-style: normal;
    text-align: center;
    color: var(--wo-biz-theme-color);
    cursor: pointer;
    transition: all 0.2s ease;

    &:active {
      background-color: var(--wo-biz-theme-color);
      color: #FFFFFF;
    }
  }
}
</style>
