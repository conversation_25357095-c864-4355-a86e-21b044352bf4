<!--
/**
 * 物流时间轴骨架屏组件
 *
 * 主要功能：
 * 1. 在物流数据加载时显示骨架屏效果，提升用户体验
 * 2. 模拟物流时间轴的布局结构，包括时间点、连接线、文字内容等
 * 3. 提供流畅的加载动画效果，减少用户等待时的焦虑感
 * 4. 支持自定义骨架项数量，适配不同的加载场景
 * 5. 与真实物流时间轴保持一致的尺寸和布局，确保无缝切换
 *
 * 技术特点：
 * - 使用CSS动画实现流畅的骨架屏效果
 * - 采用渐变背景和脉冲动画模拟内容加载状态
 * - 使用语义化的ol/li标签组织时间轴结构
 * - 支持响应式设计，适配不同屏幕尺寸
 *
 * 使用场景：
 * - 物流详情页面数据加载时的占位显示
 * - 任何需要时间轴加载状态的页面
 */
-->

<template>
  <!-- 物流骨架屏主容器 -->
  <section class="express-skeleton">
    <!-- 物流时间轴骨架列表 -->
    <ol class="express-skeleton__timeline">
      <!-- 循环生成指定数量的骨架项 -->
      <li class="express-skeleton__item" v-for="n in skeletonCount" :key="n">
        <!-- 物流信息文字骨架 -->
        <div class="express-skeleton__text"></div>
        <!-- 物流时间骨架 -->
        <div class="express-skeleton__time"></div>
      </li>
    </ol>
  </section>
</template>

<script setup>
import { toRefs } from 'vue'

// ==================== 组件属性定义 ====================
// 定义组件接收的props参数
const props = defineProps({
  // 骨架项数量，控制显示多少个骨架条目
  skeletonCount: {
    type: Number,
    default: 3
  }
})

// 使用toRefs解构props，保持响应性
const { skeletonCount } = toRefs(props)
</script>

<style lang="less" scoped>
.express-skeleton {
  padding: 15px 15px 15px 56px;
  width: 100%;
  background: #FFFFFF;
  box-sizing: border-box;
  contain: layout style;

  &__timeline {
    margin: 0;
    padding: 0;
    list-style: none;
    transform: translateZ(0);
  }

  &__item {
    position: relative;
    padding-bottom: 22px;
    font-size: 14px;
    line-height: 20px;
    text-align: left;

    &::before {
      content: '';
      position: absolute;
      z-index: 2;
      left: -32px;
      display: block;
      width: 8px;
      height: 8px;
      background-color: #e0e0e0;
      border: 2px solid #FFFFFF;
      border-radius: 50%;
      animation: skeleton-pulse 1.5s infinite;
    }

    &::after {
      content: '';
      position: absolute;
      z-index: 1;
      left: -26px;
      top: 0;
      display: block;
      width: 1px;
      height: 100%;
      background: #E2E8EE;
    }

    &:last-child::after {
      display: none;
    }
  }

  &__text {
    height: 20px;
    width: 85%;
    margin-bottom: 4px;
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: skeleton-shimmer 1.5s infinite;
    border-radius: 2px;

    &:nth-child(odd) {
      width: 75%;
    }
  }

  &__time {
    height: 12px;
    width: 120px;
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: skeleton-shimmer 1.5s infinite;
    border-radius: 2px;
  }
}

@keyframes skeleton-shimmer {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

@keyframes skeleton-pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}
</style>
