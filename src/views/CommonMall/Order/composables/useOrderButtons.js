import { getBizCode } from "@/utils/curEnv"

/**
 * 订单按钮配置相关的通用逻辑
 */
export function useOrderButtons() {

  /**
   * 检查数组是否包含某个值（兼容性更好的方式）
   */
  const arrayIncludes = (arr, value) => arr.indexOf(value) !== -1

  /**
   * 检查是否显示查看物流按钮
   */
  const toShowViewLogistics = (item) => {
    return item.orderState === '5' || item.orderState === '9'
  }

  /**
   * 检查是否显示香蕉树按钮
   */
  const toShowBananaTree = (item) => {
    return item.supplierDistriBiz?.distriBizCode === 'banana'
  }

  /**
   * 创建按钮配置对象
   */
  const createButtonConfig = (text, color, handler) => ({ text, color, handler })

  /**
   * 获取订单按钮配置
   */
  const getOrderButtonsConfig = (item, actions) => {
    const { orderState, supplierDistriBiz } = item
    const bizCode = getBizCode()
    const buttons = []

    // 如果是zq业务，只显示特定按钮
    if (bizCode === 'zq') {
      // 删除订单按钮
      if (arrayIncludes(['2', '9'], orderState)) {
        buttons.push(createButtonConfig('删除订单', 'gray', () => actions.deleteOrderAction(item, actions.onSuccess)))
      }

      // 取消订单按钮（待支付状态）
      if (arrayIncludes(['1', '3'], orderState)) {
        buttons.push(createButtonConfig('取消订单', 'gray', () => actions.cancelOrderAction(item.bizOrderId || item.id, actions.onSuccess)))
      }

      // 查看物流按钮
      if (arrayIncludes(['4','5','9'], orderState)) {
        buttons.push(createButtonConfig('查看物流', 'gray', () => actions.viewLogisticsAction(item)))
      }

      // 确认收货按钮
      if (arrayIncludes(['4','5'], orderState)) {
        buttons.push(createButtonConfig('确认收货', 'orange', () => actions.confirmReceiptAction(item, actions.onSuccess)))
      }

      return buttons
    }

    // 非zq业务，保持原有逻辑
    // 删除订单按钮
    if (arrayIncludes(['2', '9', '10'], orderState)) {
      buttons.push(createButtonConfig('删除订单', 'gray', () => actions.deleteOrderAction(item, actions.onSuccess)))
    }

    // 待支付状态按钮
    if (orderState === '0') {
      buttons.push(createButtonConfig('取消订单', 'gray', () => actions.cancelOrderAction(item.bizOrderId || item.id, actions.onSuccess)))
      buttons.push(createButtonConfig('去支付', 'orange', () => actions.payOrderAction(item.bizOrderId || item.id)))
    }

    // 查看物流按钮
    if (toShowViewLogistics(item)) {
      buttons.push(createButtonConfig('查看物流', 'gray', () => actions.viewLogisticsAction(item)))
    }

    // 香蕉树按钮
    if (toShowBananaTree(item)) {
      buttons.push(createButtonConfig('我的香蕉树', 'gray', () => actions.bananaTreeAction()))
    }

    // 荣誉证书按钮
    if (supplierDistriBiz?.distriBizCode === 'fupin' && arrayIncludes(['1', '3', '5', '9'], orderState)) {
      buttons.push(createButtonConfig('荣誉证书', 'gray', () => actions.certificateAction(item, actions.setCertificate)))
    }

    // 催发货按钮
    if (orderState === '3') {
      buttons.push(createButtonConfig('催发货', 'orange', () => actions.urgeShipmentAction(item)))
    }

    // 确认收货按钮
    if (orderState === '5') {
      buttons.push(createButtonConfig('确认收货', 'orange', () => actions.confirmReceiptAction(item, actions.onSuccess)))
    }

    // 再次购买按钮
    if (arrayIncludes(['2', '9', '10'], orderState)) {
      buttons.push(createButtonConfig('再次购买', 'orange', () => actions.buyAgainAction(item)))
    }

    return buttons
  }

  /**
   * 获取可见的按钮（≤3个时全部显示，>3个时显示倒数3个）
   */
  const getVisibleButtons = (item, actions) => {
    const allButtons = getOrderButtonsConfig(item, actions)
    return allButtons.length <= 3 ? allButtons : allButtons.slice(-3)
  }

  /**
   * 获取更多操作按钮（>3个时显示除倒数3个外的其他按钮）
   */
  const getMoreActions = (item, actions) => {
    const allButtons = getOrderButtonsConfig(item, actions)
    return allButtons.length > 3 ? allButtons.slice(0, -3) : []
  }


  /**
   * 获取按钮类型
   */
  const getButtonType = (color) => {
    switch (color) {
      case 'orange':
        return 'primary'
      case 'gray':
      default:
        return 'default'
    }
  }

  /**
   * 获取订单状态样式类
   */
  const getStatusClass = (state) => {
    const statusMap = {
      '0': 'order-status--unpaid',
      '3': 'order-status--unpaid',
      '5': 'order-status--unpaid',
      '9': 'order-status--completed'
    }
    return statusMap[state] || 'order-status--unpaid'
  }

  return {
    getOrderButtonsConfig,
    getVisibleButtons,
    getMoreActions,
    getButtonType,
    getStatusClass,
    toShowViewLogistics,
    toShowBananaTree
  }
}
