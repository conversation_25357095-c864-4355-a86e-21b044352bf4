import { ref, onUnmounted } from 'vue'

/**
 * 订单倒计时相关的通用逻辑
 */
export function useOrderCountdown() {
  const countdownTimers = ref(new Map())
  const PAYMENT_TIMEOUT = 30 * 60 * 1000 // 30分钟

  /**
   * 开始倒计时
   */
  const startCountdown = (order, orderList) => {
    if (order.orderState !== '0') return

    const createTime = order.createTime || order.orderDate
    if (!createTime) return

    // 清除已存在的定时器
    if (countdownTimers.value.has(order.id)) {
      clearInterval(countdownTimers.value.get(order.id))
    }

    const updateCountdown = () => {
      const now = Date.now()
      const orderTime = new Date(createTime).getTime()
      const elapsed = now - orderTime
      const remaining = PAYMENT_TIMEOUT - elapsed

      if (remaining <= 0) {
        // 倒计时结束，更新订单状态
        if (orderList) {
          const orderIndex = orderList.value.findIndex(o => o.id === order.id)
          if (orderIndex !== -1) {
            orderList.value[orderIndex].remainingTime = 0
            orderList.value[orderIndex].orderState = '2'
          }
        } else {
          // 直接更新订单对象
          Object.assign(order, {
            remainingTime: 0,
            orderState: '2'
          })
        }

        // 清除定时器
        clearInterval(countdownTimers.value.get(order.id))
        countdownTimers.value.delete(order.id)
      } else {
        // 更新剩余时间
        if (orderList) {
          const orderIndex = orderList.value.findIndex(o => o.id === order.id)
          if (orderIndex !== -1) {
            orderList.value[orderIndex].remainingTime = remaining
          }
        } else {
          order.remainingTime = remaining
        }
      }
    }

    // 立即执行一次
    updateCountdown()
    
    // 设置定时器
    const timer = setInterval(updateCountdown, 1000)
    countdownTimers.value.set(order.id, timer)
  }

  /**
   * 清除单个订单的倒计时
   */
  const clearCountdown = (orderId) => {
    if (countdownTimers.value.has(orderId)) {
      clearInterval(countdownTimers.value.get(orderId))
      countdownTimers.value.delete(orderId)
    }
  }

  /**
   * 清除所有倒计时
   */
  const clearAllCountdowns = () => {
    countdownTimers.value.forEach(timer => clearInterval(timer))
    countdownTimers.value.clear()
  }

  /**
   * 初始化订单列表的倒计时
   */
  const initOrderCountdowns = (orderList) => {
    orderList.value.forEach((order) => {
      if (order.orderState === '0') {
        const createTime = order.createTime || order.orderDate
        if (createTime) {
          const now = Date.now()
          const orderTime = new Date(createTime).getTime()
          const elapsed = now - orderTime
          const remaining = PAYMENT_TIMEOUT - elapsed

          const orderIndex = orderList.value.findIndex(o => o.id === order.id)
          if (orderIndex !== -1) {
            orderList.value[orderIndex].remainingTime = Math.max(0, remaining)

            if (orderList.value[orderIndex].remainingTime > 0) {
              startCountdown(orderList.value[orderIndex], orderList)
            } else {
              orderList.value[orderIndex].orderState = '2'
              orderList.value[orderIndex].remainingTime = 0
            }
          }
        }
      }
    })
  }

  /**
   * 处理页面可见性变化
   */
  const handleVisibilityChange = (orderList) => {
    if (document.hidden) {
      clearAllCountdowns()
    } else {
      // 页面重新可见时重新初始化倒计时
      setTimeout(() => {
        initOrderCountdowns(orderList)
      }, 100)
    }
  }

  // 组件卸载时清除所有定时器
  onUnmounted(() => {
    clearAllCountdowns()
  })

  return {
    countdownTimers,
    startCountdown,
    clearCountdown,
    clearAllCountdowns,
    initOrderCountdowns,
    handleVisibilityChange,
    PAYMENT_TIMEOUT
  }
}
