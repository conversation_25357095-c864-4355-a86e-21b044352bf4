/**
 * 订单操作组合式函数
 *
 * 主要功能：
 * 1. 提供订单相关的通用操作功能，包括取消、删除、支付、确认收货等
 * 2. 集成订单详情跳转和订单号复制等基础功能
 * 3. 支持物流查询和再次购买等扩展功能
 * 4. 处理不同业务场景的订单操作逻辑
 * 5. 提供统一的错误处理和用户反馈机制
 *
 * 操作类型：
 * - 基础操作：复制订单号、查看详情
 * - 状态操作：取消订单、删除订单、确认收货
 * - 支付操作：重新支付、支付状态处理
 * - 扩展操作：查看物流、再次购买
 *
 * 技术特点：
 * - 使用组合式API提升代码复用性
 * - 集成多种第三方库和工具函数
 * - 支持批量操作和单个操作
 * - 提供完善的错误处理机制
 *
 * 使用场景：
 * - 订单列表页面的操作按钮
 * - 订单详情页面的操作功能
 * - 搜索结果页面的订单操作
 */

import { showToast, showLoadingToast, closeToast } from 'vant'
import { useRouter } from 'vue-router'
import { computed } from 'vue'
import useClipboard from 'vue-clipboard3'
import dayjs from 'dayjs'
import { formSubmit } from 'commonkit'

import { useAlert } from '@/composables/index.js'
import {
  cancelOrder,
  modOrderListShow,
  manualConfirmRecv,
  repayOrder,
  getOrderExpress,
  verifySupplierOrderRepurchased
} from '@api/interface/order.js'
import { getBizCode } from '@utils/curEnv.js'
import { buyProductCart, buyProductCartSession } from '@utils/storage.js'
import { fenToYuan } from '@utils/amount.js'
import { getEnterpriseManagerInfo, getCustomerManagerInfo } from '@utils/zqInfo.js'

/**
 * 订单操作相关的通用逻辑组合式函数
 */
export function useOrderActions() {
  // ==================== 核心依赖实例 ====================
  // 路由导航实例
  const router = useRouter()
  // 弹窗提示工具实例
  const $alert = useAlert()
  // 剪贴板操作工具
  const { toClipboard } = useClipboard()

  // ==================== 用户角色信息 ====================
  // 获取用户角色类型，用于权限控制
  const roleType = computed(() => {
    const { roleType = '' } = getEnterpriseManagerInfo() || getCustomerManagerInfo() || {}
    return roleType
  })

  // ==================== 基础操作功能 ====================
  /**
   * 复制订单号到剪贴板
   * @param {string} orderNumber - 订单号
   */
  const copyOrderNumber = async (orderNumber) => {
    try {
      await toClipboard(orderNumber)
      showToast('复制成功')
    } catch (e) {
      showToast('复制失败')
    }
  }

  /**
   * 跳转到订单详情页面
   * @param {Object} order - 订单对象
   */
  const goToOrderDetail = (order) => {
    router.push({
      path: '/user/order/detail',
      query: {
        orderId: order.id,
        isPay: order.orderState === '0' ? '1' : '2'  // 待付款订单标记为支付页面
      }
    })
  }

  // ==================== 订单状态操作功能 ====================
  /**
   * 取消订单操作
   * @param {string} bizOrderId - 业务订单ID
   * @param {Function} onSuccess - 成功回调函数
   */
  const cancelOrderAction = async (bizOrderId, onSuccess) => {
    // 执行取消订单的内部函数
    const cancelOrderFn = async () => {
      showLoadingToast()
      const [err] = await cancelOrder(bizOrderId)
      closeToast()

      if (!err) {
        showToast('取消成功')
        onSuccess && onSuccess()
      } else {
        showToast(err.msg)
      }
    }

    // 显示确认取消弹窗
    $alert({
      title: '',
      message: '取消后将无法恢复，您确定要取消订单吗？',
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      showCancelButton: true,
      onConfirmCallback: cancelOrderFn
    })
  }

  /**
   * 删除订单操作
   * @param {Object} item - 订单对象
   * @param {Function} onSuccess - 成功回调函数
   */
  const deleteOrderAction = async (item, onSuccess) => {
    // 执行删除订单的内部函数
    const deleteOrderFn = async () => {
      showLoadingToast()

      try {
        // 处理包含多个供应商订单的情况
        if (item.supplierOrderList?.length > 0) {
          // 批量删除所有供应商订单
          const deletePromises = item.supplierOrderList.map(supplierOrder => {
            return modOrderListShow({
              supplierOrderId: supplierOrder.id,
              isDelete: 1  // 1表示删除到回收站
            })
          })

          const results = await Promise.all(deletePromises)
          const hasError = results.some(([err]) => err)

          if (hasError) {
            const errorResult = results.find(([err]) => err)
            throw new Error(errorResult[0].msg)
          }
        } else {
          // 处理单个订单的情况
          const [err] = await modOrderListShow({
            supplierOrderId: item.id,
            isDelete: 1  // 1表示删除到回收站
          })
          if (err) {
            throw new Error(err.msg)
          }
        }

        closeToast()
        showToast('删除成功')
        onSuccess && onSuccess()
      } catch (error) {
        closeToast()
        showToast(error.message || '删除失败')
      }
    }

    // 显示确认删除弹窗
    $alert({
      title: '',
      message: '确认删除该订单？',
      confirmButtonText: '确定',
      showCancelButton: true,
      cancelButtonText: '取消',
      onConfirmCallback: deleteOrderFn
    })
  }

  /**
   * 支付订单
   */
  const payOrderAction = async (bizOrderId) => {
    showLoadingToast()
    try {
      const [res, json] = await repayOrder(bizOrderId)
      closeToast()

      if (res.code === '0000') {
        formSubmit(json.wapURL, { param: json.encryptContent })
      } else if (res.code === '2091070302' && res.data?.length > 0) {
        if (json.some(info => info.state === '2')) {
          showToast('您的订单中有商品已下架')
        } else if (json.some(info => info.state === '3')) {
          showToast('您的订单中有无货商品')
        } else if (json.some(info => info.state === '4')) {
          showToast('您的订单中有商品库存不足')
        }
      } else {
        showToast(res.msg)
      }
    } catch (error) {
      closeToast()
      console.error('支付失败:', error)
      showToast('支付失败，请重试')
    }
  }

  /**
   * 确认收货
   */
  const confirmReceiptAction = async (item, onSuccess) => {
    const confirmReceiptFn = async () => {
      showLoadingToast()

      try {
        if (item.supplierOrderList?.length > 0) {
          const confirmPromises = item.supplierOrderList.map(supplierOrder => {
            return manualConfirmRecv({ supplierOrderId: supplierOrder.id })
          })

          const results = await Promise.all(confirmPromises)
          const hasError = results.some(([err]) => err)

          if (hasError) {
            const errorResult = results.find(([err]) => err)
            throw new Error(errorResult[0].msg)
          }
        } else {
          const [err] = await manualConfirmRecv({ supplierOrderId: item.id })
          if (err) {
            throw new Error(err.msg)
          }
        }

        closeToast()
        showToast('确认收货成功')
        onSuccess && onSuccess()
      } catch (error) {
        closeToast()
        showToast(error.message || '操作失败')
      }
    }

    $alert({
      title: '确认收货',
      message: item.orderState === '4' ? '您有部分订单可能未发货，是否确认收货？' : '确认收到商品了吗？',
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      messageAlign: 'center',
      onConfirmCallback: confirmReceiptFn
    })
  }

  /**
   * 查看物流
   */
  const viewLogisticsAction = async (item) => {
    const { id, orderDate } = item
    const now = dayjs()
    const orderDateDayjs = dayjs(orderDate)
    const endTimeSub180 = now.subtract(12, 'month')
    const isWithinScope = orderDateDayjs.isBefore(endTimeSub180, 'minute')

    if (isWithinScope) {
      showToast('物流信息已失效 ！')
      return
    }

    try {
      // 根据bizCode判断是否传递roleType参数
      const bizCode = getBizCode()
      let orderExpress

      if (bizCode === 'zq') {
        const [err, data] = await getOrderExpress(id, roleType.value)
        if (err) {
          showToast('查询物流信息失败')
          return
        }
        orderExpress = data
      } else {
        const [err, data] = await getOrderExpress(id)
        if (err) {
          showToast('查询物流信息失败')
          return
        }
        orderExpress = data
      }

      const { orderPackageList } = orderExpress
      if (orderPackageList?.length > 0) {
        router.push({
          name: 'user-order-entry-express',
          params: { orderExpress },
          query: { orderId: id }
        })
        return
      }

      showToast('物流信息已失效 ！')
    } catch (error) {
      console.error('查询物流信息失败:', error)
      showToast('查询物流信息失败')
    }
  }

  /**
   * 香蕉树
   */
  const bananaTreeAction = () => {
    router.push('/fpHome/banana-tree')
  }

  /**
   * 荣誉证书
   */
  const certificateAction = (item, setCertificate) => {
    setCertificate({
      date: new Date(item.orderDate.replace(/-/g, '/')),
      title: '尊敬的沃钱包用户：',
      amt: fenToYuan(item.paymentDetail.actualPayAmount),
      show: true
    })
  }

  /**
   * 催发货
   */
  const urgeShipmentAction = (item) => {
    const { orderDate } = item
    const targetDate = dayjs(orderDate)
    const now = dayjs()
    const diff = now.diff(targetDate, 'millisecond')
    const diffInHours = diff / (1000 * 60 * 60)
    const isWithin48Hours = Math.abs(diffInHours) <= 48

    if (isWithin48Hours) {
      const dateAdd48 = targetDate.add(48, 'hour')
      const formattedDate = dateAdd48.format('M月DD日')
      $alert({
        messageHtml: `<div>您的商品目前处于正常配送时效内，商家将于<span style="color:#FF780A;">${formattedDate}</span>前发货，请您耐心等待。</div>`,
        confirmButtonText: '确定',
        allowHtml: true,
        messageAlign: 'center'
      })
    } else {
      $alert({
        message: '给您带来的不便深感抱歉，已为您提醒商家发货，请您耐心等待。',
        confirmButtonText: '确定',
        messageAlign: 'center'
      })
    }
  }

  /**
   * 再次购买
   */
  const buyAgainAction = async (orderInfo) => {
    const bizCode = getBizCode('ORDER')
    showLoadingToast()

    try {
      let allValidGoodsList = []
      let totalSkuCount = 0

      if (orderInfo.supplierOrderList?.length > 0) {
        const verifyPromises = orderInfo.supplierOrderList.map(supplierOrder => {
          return verifySupplierOrderRepurchased({
            bizCode,
            supplierOrderId: supplierOrder.id
          })
        })

        const results = await Promise.all(verifyPromises)

        results.forEach(([err, res], index) => {
          if (!err && res?.validGoodsList) {
            allValidGoodsList = [...allValidGoodsList, ...res.validGoodsList]
          }
          const supplierOrder = orderInfo.supplierOrderList[index]
          if (supplierOrder.skuNumInfoList) {
            totalSkuCount += supplierOrder.skuNumInfoList.length
          }
        })
      } else {
        const [err, res] = await verifySupplierOrderRepurchased({
          bizCode,
          supplierOrderId: orderInfo.id
        })

        if (!err && res) {
          allValidGoodsList = res.validGoodsList || []
        }
        totalSkuCount = orderInfo.skuNumInfoList?.length || 0
      }

      closeToast()

      if (allValidGoodsList.length === 0) {
        showToast('订单中的商品都卖光了，在看看其他商品吧~')
        return
      }

      buyProductCart.set(allValidGoodsList)
      buyProductCartSession.set(allValidGoodsList)

      if (totalSkuCount === allValidGoodsList.length) {
        router.push('/orderconfirm')
      } else if (totalSkuCount > allValidGoodsList.length) {
        $alert({
          title: '',
          message: '部分商品无货或已下架无法购买!',
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          onConfirmCallback: () => router.push('/orderconfirm')
        })
      }
    } catch (error) {
      closeToast()
      console.error('再次购买检查失败:', error)
      showToast('操作失败，请重试')
    }
  }

  return {
    copyOrderNumber,
    goToOrderDetail,
    cancelOrderAction,
    deleteOrderAction,
    payOrderAction,
    confirmReceiptAction,
    viewLogisticsAction,
    bananaTreeAction,
    certificateAction,
    urgeShipmentAction,
    buyAgainAction
  }
}
