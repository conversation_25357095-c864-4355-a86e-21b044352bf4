<!--
/**
 * 订单支付回调页面组件
 *
 * 主要功能：
 * 1. 展示订单支付完成状态，为用户提供明确的支付成功反馈
 * 2. 提供快速导航功能，支持返回首页和查看订单两个核心操作
 * 3. 显示后续操作提示，引导用户了解订单处理流程
 * 4. 采用响应式设计，适配不同设备屏幕尺寸
 * 5. 集成路由导航，实现页面间的无缝跳转
 *
 * 技术特点：
 * - 使用语义化HTML结构，提升页面可访问性
 * - 采用BEM命名规范，保持样式代码的可维护性
 * - 集成Vue Router进行页面导航管理
 * - 使用组合式API，代码结构清晰简洁
 *
 * 使用场景：
 * - 用户完成订单支付后的确认页面
 * - 支付流程的最终环节，提供后续操作指引
 */
-->

<template>
  <!-- 订单支付回调页面主容器 -->
  <main class="order-callback">
    <!-- 页面内容区域，包含支付状态、操作按钮和提示信息 -->
    <section class="order-callback__content">
      <!-- 支付状态展示区域 -->
      <!-- 通过图标和文字组合展示支付完成状态 -->
      <div class="order-callback__status">
        <!-- 支付成功状态图标，使用背景图片方式展示 -->
        <span class="order-callback__status-icon" aria-label="支付成功图标"/>
        <!-- 支付状态文字说明，使用h1标签突出重要性 -->
        <h1 class="order-callback__status-text">支付完成</h1>
      </div>

      <!-- 用户操作按钮区域 -->
      <!-- 提供返回首页和查看订单两个主要操作选项 -->
      <div class="order-callback__actions">
        <!-- 返回首页按钮，使用次要样式 -->
        <!-- 点击后导航到应用首页 -->
        <WoButton
          size="medium"
          @click="handleGoHome">
          返回首页
        </WoButton>
        <!-- 查看订单按钮，使用主要样式突出重要性 -->
        <!-- 点击后导航到订单列表页面，默认显示待发货订单 -->
        <WoButton
          type="primary"
          size="medium"
          @click="handleViewOrder">
          查看订单
        </WoButton>
      </div>

      <!-- 后续操作提示信息 -->
      <!-- 告知用户订单处理状态和查询方式 -->
      <p class="order-callback__tips">正在为您备货，请在我的订单中查询物流详情。</p>
    </section>
  </main>
</template>

<script setup>
import { useRouter } from 'vue-router'
import WoButton from '@components/WoElementCom/WoButton/WoButton.vue'

// ==================== 路由导航管理 ====================
// 获取Vue Router实例，用于页面导航操作
const router = useRouter()

// 返回首页处理函数
// 当用户点击"返回首页"按钮时触发
const handleGoHome = () => {
  // 导航到应用首页路径
  router.push('/home')
}

// 查看订单处理函数
// 当用户点击"查看订单"按钮时触发
const handleViewOrder = () => {
  // 导航到用户订单列表页面
  // 通过query参数type=3指定显示待发货订单类型
  router.push({
    path: '/user/order/list',
    query: { type: 3 }
  })
}
</script>

<style scoped lang="less">
.order-callback {
  min-height: 100vh;
  background-color: #FFFFFF;

  &__content {
    padding: 25px 16px;
    text-align: center;
  }

  &__status {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 25px;
  }

  &__status-icon {
    display: block;
    width: 28px;
    height: 28px;
    background-image: url(./assets/icon-success.png);
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
    will-change: transform;
  }

  &__status-text {
    margin: 0 0 0 7px;
    font-size: 17px;
    font-weight: 500;
    color: #171E24;
    line-height: 1.4;
  }

  &__actions {
    display: flex;
    justify-content: center;
    gap: 30px;
    margin-bottom: 16px;
  }

  &__tips {
    margin: 0;
    font-size: 13px;
    color: #4A5568;
    line-height: 1.5;
  }
}
</style>
