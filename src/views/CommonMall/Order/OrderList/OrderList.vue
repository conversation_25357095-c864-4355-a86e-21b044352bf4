<!--
/**
 * 订单列表页面组件
 *
 * 主要功能：
 * 1. 展示用户的订单列表，支持按状态分类查看
 * 2. 提供订单搜索功能，支持跳转到专门的搜索页面
 * 3. 集成下拉刷新功能，实时更新订单数据
 * 4. 支持Tab切换，按订单状态筛选显示
 * 5. 区分普通商城和政企业务，显示不同的订单状态标签
 * 6. 提供回收站入口，查看已删除的订单
 * 7. 实现滚动位置记忆，切换Tab时保持滚动状态
 *
 * 订单状态分类：
 * - 普通商城：全部、待付款、待发货、待收货、已完成
 * - 政企业务：全部、待发货、待收货、已取消、已签收
 *
 * 技术特点：
 * - 使用van-tabs实现Tab切换功能
 * - 集成van-pull-refresh提供下拉刷新
 * - 支持路由参数控制默认显示的Tab
 * - 实现滚动位置缓存，提升用户体验
 * - 采用组件化设计，职责分离清晰
 *
 * 使用场景：
 * - 用户查看个人订单列表
 * - 按订单状态筛选查看
 * - 订单搜索和管理
 */
-->

<template>
  <!-- 订单列表页面主容器 -->
  <div class="order-list">
    <!-- 顶部搜索区域 -->
    <!-- 仅在非政企业务时显示搜索功能 -->
    <SearchHeader
      v-if="currentBizCode !== 'zq'"
      v-model="searchKeyword"
      placeholder="搜索我的订单"
      :redirectToSearch="true"
      redirectUrl="/user/order/search"
      @search="handleSearch">
      <!-- 自定义右侧操作区插槽 -->
      <!-- 提供回收站入口 -->
      <template #right-action>
        <div class="recycle-icon" @click="goToRecycle">
          <img src="@/static/images/recycle.png" alt="回收站" />
        </div>
      </template>
    </SearchHeader>

    <!-- Tab切换区域 -->
    <!-- 使用粘性定位，滚动时保持Tab栏可见 -->
    <van-tabs v-model:active="activeTab" sticky @change="handleTabChange">
      <!-- 遍历订单状态标签 -->
      <van-tab v-for="(tab, index) in orderTabs" :key="tab.key" :title="tab.name">
        <!-- 下拉刷新包装器 -->
        <van-pull-refresh v-model="refreshing" @refresh="onRefresh">
          <!-- 订单Tab内容组件 -->
          <!-- 使用动态key强制重新渲染，确保Tab切换时数据更新 -->
          <OrderTabContent
            v-if="activeTab === index"
            :ref="el => tabContentRefs[index] = el"
            :key="`${tab.key}-${tabChangeKey}`"
            :tab-type="tab.key"
            :scroll-position="scrollPositions[index]"
            @update-scroll="updateScrollPosition(index, $event)" />
        </van-pull-refresh>
      </van-tab>
    </van-tabs>
  </div>
</template>

<script setup>
import { ref, onActivated, onDeactivated, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'

import OrderTabContent from './components/OrderTabContent.vue'
import SearchHeader from "@components/Common/SearchHeader.vue"

import { getBizCode } from '@utils/curEnv.js'

// ==================== 核心依赖实例 ====================
// 路由导航实例
const router = useRouter()
// 当前路由信息实例
const route = useRoute()

// ==================== 搜索功能状态管理 ====================
// 搜索关键词
const searchKeyword = ref('')

// ==================== 下拉刷新状态管理 ====================
// 下拉刷新状态
const refreshing = ref(false)

// ==================== Tab组件引用管理 ====================
// Tab内容组件引用数组
const tabContentRefs = ref([])

// ==================== 业务配置管理 ====================
// 缓存业务代码，避免重复计算
const currentBizCode = getBizCode()

// 根据业务代码获取订单标签配置
const getOrderTabs = () => {
  if (currentBizCode === 'zq') {
    // 政企业务订单状态标签
    return [
      { name: '全部', key: '' },
      { name: '待发货', key: '3' },
      { name: '待收货', key: '5' },
      { name: '已取消', key: '2' },
      { name: '已签收', key: '9' }
    ]
  } else {
    // 普通商城订单状态标签
    return [
      { name: '全部', key: '' },
      { name: '待付款', key: '0' },
      { name: '待发货', key: '3' },
      { name: '待收货', key: '5' },
      { name: '已完成', key: '9' }
    ]
  }
}

// 订单状态标签配置
const orderTabs = getOrderTabs()

// ==================== Tab切换状态管理 ====================
// 当前激活的Tab索引
const activeTab = ref(0)
// Tab切换计数器，用于强制重新渲染组件
const tabChangeKey = ref(0)

// ==================== 滚动位置管理 ====================
// 初始化各Tab的滚动位置缓存
const scrollPositions = ref(
  orderTabs.reduce((acc, tab, index) => {
    acc[index] = 0
    return acc
  }, {})
)

// 更新指定Tab的滚动位置
const updateScrollPosition = (tabIndex, position) => {
  scrollPositions.value[tabIndex] = position
}

// ==================== Tab初始化功能 ====================
// 根据路由参数设置默认激活的Tab
const setActiveTabByType = () => {
  const type = route.params.type || route.query.type
  if (type) {
    const tabIndex = orderTabs.findIndex(tab => tab.key === type)
    if (tabIndex !== -1) {
      activeTab.value = tabIndex
    }
  }
}

// ==================== Tab切换功能 ====================
// 处理Tab切换事件
const handleTabChange = (index) => {
  const currentTab = orderTabs[index]

  // 增加切换计数器，强制重新渲染组件
  tabChangeKey.value++

  // 构建新的查询参数
  const newQuery = {
    ...route.query,
    type: currentTab.key || undefined
  }

  // 如果是全部标签，移除type参数
  if (!currentTab.key) {
    delete newQuery.type
  }

  // 更新路由查询参数，但不触发页面重新加载
  router.replace({
    path: route.path,
    query: newQuery
  }, () => { }, { shallow: true })
}

// ==================== 用户交互功能 ====================
// 搜索处理函数
const handleSearch = () => {
  // 搜索功能由SearchHeader组件处理跳转
  // 这里可以添加搜索前的数据处理逻辑
}

// 前往回收站功能
const goToRecycle = () => {
  router.push('/user/order/recycle')
}

// ==================== 下拉刷新功能 ====================
// 处理下拉刷新事件
const onRefresh = async () => {
  try {
    // 获取当前激活Tab的内容组件
    const currentTabContent = tabContentRefs.value[activeTab.value]

    // 调用组件的刷新方法
    if (currentTabContent && typeof currentTabContent.refreshData === 'function') {
      await currentTabContent.refreshData()
    }
  } catch (error) {
    // 刷新失败时的错误处理
  } finally {
    // 无论成功失败都要关闭刷新状态
    refreshing.value = false
  }
}

// ==================== 生命周期管理 ====================
// 组件挂载时初始化Tab状态
onMounted(() => {
  setActiveTabByType()
})

// 页面激活时的处理
onActivated(() => {
  // 页面重新显示时，可以在这里恢复滚动位置或刷新数据
})

// 页面失活时的处理
onDeactivated(() => {
  // 页面失活时，可以在这里保存状态或清理资源
})
</script>

<style scoped lang="less">
.order-list {
  background-color: #f5f5f5;
  height: 100vh;
  overflow: hidden;
}

:deep(.van-pull-refresh) {
  height: calc(100vh - 44px); // 减去搜索头部高度
  overflow: auto;
}

.recycle-icon {
  margin-left: 12px;
  padding: 4px;

  img {
    width: 20px;
    height: 20px;
  }
}

:deep(.van-tab) {
  height: 42px;
}

:deep(.van-tabs__line) {
  background-color: var(--wo-biz-theme-color);
}

:deep(.van-tab--active) {
  color: var(--wo-biz-theme-color);
  font-weight: bold;
}

:deep(.van-tabs__nav) {
  background-color: #fff;
}
</style>
