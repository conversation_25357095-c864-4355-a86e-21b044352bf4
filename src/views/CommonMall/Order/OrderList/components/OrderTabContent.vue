<!--
/**
 * 订单Tab内容组件
 *
 * 主要功能：
 * 1. 展示特定状态下的订单列表内容，支持分页加载
 * 2. 集成订单操作功能，包括查看详情、复制订单号等
 * 3. 提供订单按钮管理，根据订单状态显示不同的操作按钮
 * 4. 支持订单倒计时功能，实时显示支付剩余时间
 * 5. 集成证书弹窗功能，支持动态加载证书组件
 * 6. 实现滚动位置记忆，提升用户体验
 * 7. 提供空状态和加载状态的友好界面
 *
 * 技术特点：
 * - 使用组合式API实现功能模块化
 * - 集成van-list实现无限滚动加载
 * - 采用shallowRef优化大数组性能
 * - 支持动态组件加载，按需引入证书组件
 * - 使用markRaw优化组件引用性能
 *
 * 使用场景：
 * - 订单列表页面的Tab内容展示
 * - 不同订单状态的列表管理
 * - 订单操作和状态跟踪
 */
-->

<template>
  <!-- 订单Tab内容主容器 -->
  <section class="order-tab-content" ref="contentRef">
    <!-- 订单列表加载骨架屏 -->
    <OrderSkeleton v-if="showSkeleton" />

    <!-- 订单列表空状态 -->
    <!-- 当没有数据且加载完成时显示 -->
    <OrderEmptyState v-if="!showSkeleton && !loading && orderList.length === 0 && finished" />

    <!-- 订单列表内容 -->
    <!-- 使用van-list实现无限滚动加载 -->
    <van-list
      v-else
      v-model:loading="loading"
      :finished="finished"
      loading-text="加载中..."
      finished-text="没有更多了"
      @load="loadOrderList"
      :immediate-check="false"
    >
      <!-- 遍历显示订单项 -->
      <CommonOrderItem
        v-for="order in orderList"
        :key="order.id"
        :order="order"
        :visible-buttons="getVisibleButtonsForTemplate(order)"
        :more-actions="getMoreActionsForTemplate(order)"
        @detail-click="goToOrderDetail"
        @copy-order="copyOrderNumber"
      />
    </van-list>

    <!-- 动态证书弹窗组件 -->
    <!-- 仅在有证书组件且需要显示时渲染 -->
    <component
      v-if="CertificateComponent && certificate.show"
      :is="CertificateComponent"
      :show="certificate.show"
      :title="certificate.title"
      :date="certificate.date"
      :amt="certificate.amt"
      @close="onCertificateClose"
    />
  </section>
</template>

<script setup>
import { ref, shallowRef, watch, nextTick, toRefs, onMounted, onUnmounted, markRaw } from 'vue'
import OrderSkeleton from './OrderSkeleton.vue'
import OrderEmptyState from './OrderEmptyState.vue'
import CommonOrderItem from '../../components/CommonOrderItem.vue'
import { useOrderList } from '../../composables/useOrderList.js'
import { useOrderActions } from '../../composables/useOrderActions.js'
import { useOrderButtons } from '../../composables/useOrderButtons.js'
import { useOrderCountdown } from '../../composables/useOrderCountdown.js'

// ==================== 组件属性定义 ====================
// 定义组件接收的props参数
const props = defineProps({
  // Tab类型，用于区分不同的订单状态
  tabType: {
    type: String,
    required: true
  },
  // 滚动位置，用于恢复滚动状态
  scrollPosition: {
    type: Number,
    default: 0
  }
})

// 使用toRefs解构props，保持响应性
const { tabType, scrollPosition } = toRefs(props)

// 定义组件向父组件发射的事件
const emit = defineEmits(['update-scroll'])

// ==================== 证书弹窗状态管理 ====================
// 证书弹窗显示状态和数据
const certificate = ref({
  show: false,        // 弹窗显示状态
  title: '',          // 证书标题
  date: new Date(),   // 证书日期
  amt: ''             // 证书金额
})

// 动态加载的证书组件，使用shallowRef优化性能
const CertificateComponent = shallowRef(null)

// 使用通用的订单列表逻辑
const {
  loading,
  finished,
  orderList,
  showSkeleton,
  contentRef,
  loadOrderList,
  resetData,
  refreshData,
  handleScroll,
  removeOrderItem
} = useOrderList({
  tabType
})

// 使用通用的订单操作逻辑
const orderActions = useOrderActions()

// 使用通用的订单按钮逻辑
const { getVisibleButtons, getMoreActions } = useOrderButtons()

// 使用通用的倒计时逻辑
const {
  startCountdown,
  clearAllCountdowns,
  initOrderCountdowns,
  handleVisibilityChange
} = useOrderCountdown()

// 创建操作配置，包含成功回调和证书设置
const createActionConfig = () => ({
  ...orderActions,
  onSuccess: () => {
    resetData()
    loadOrderList()
  },
  setCertificate: async (config) => {
    // 懒加载证书组件
    await loadCertificateComponent()
    certificate.value = { ...certificate.value, ...config }
  }
})

const actionConfig = createActionConfig()

// 获取按钮配置
const getVisibleButtonsWithActions = (order) => {
  return getVisibleButtons(order, actionConfig)
}

const getMoreActionsWithActions = (order) => {
  return getMoreActions(order, actionConfig)
}

// 重写模板中使用的方法名
const { copyOrderNumber, goToOrderDetail } = orderActions

// 重新定义按钮获取方法以使用正确的名称
const getVisibleButtonsForTemplate = (order) => getVisibleButtonsWithActions(order)
const getMoreActionsForTemplate = (order) => getMoreActionsWithActions(order)

// 处理滚动事件，发送滚动位置给父组件
const handleScrollWithEmit = () => {
  handleScroll()
  if (contentRef.value) {
    const scrollTop = contentRef.value.scrollTop || document.documentElement.scrollTop || document.body.scrollTop
    emit('update-scroll', scrollTop)
  }
}

// 监听标签页切换
watch(() => tabType.value, () => {
  clearAllCountdowns()
  resetData()
  nextTick(() => {
    loadOrderList().then(orders => {
      if (orders) {
        initOrderCountdowns(orderList)
      }
    })
  })
})

// 懒加载证书组件
const loadCertificateComponent = async () => {
  if (!CertificateComponent.value) {
    try {
      const { default: Certificate } = await import('../../components/Certificate/Certificate.vue')
      // 使用 markRaw 避免组件被Vue的响应式系统包装，提升性能
      CertificateComponent.value = markRaw(Certificate)
    } catch (error) {
      console.error('Failed to load Certificate component:', error)
    }
  }
}

// 证书弹窗关闭
const onCertificateClose = () => {
  certificate.value.show = false
}

// 组件挂载时的初始化
const initComponent = () => {
  if (contentRef.value) {
    contentRef.value.addEventListener('scroll', handleScrollWithEmit)
  } else {
    window.addEventListener('scroll', handleScrollWithEmit)
  }

  document.addEventListener('visibilitychange', () => handleVisibilityChange(orderList))

  loadOrderList().then(orders => {
    if (orders) {
      initOrderCountdowns(orderList)
    }
  })
}

// 组件卸载时的清理
const cleanupComponent = () => {
  if (contentRef.value) {
    contentRef.value.removeEventListener('scroll', handleScrollWithEmit)
  } else {
    window.removeEventListener('scroll', handleScrollWithEmit)
  }

  document.removeEventListener('visibilitychange', () => handleVisibilityChange(orderList))
  clearAllCountdowns()
}

onMounted(() => {
  initComponent()
})

onUnmounted(() => {
  cleanupComponent()
})

// 暴露刷新方法给父组件
defineExpose({
  refreshData
})

</script>

<style scoped lang="less">
.order-tab-content {
  min-height: calc(100vh - 134px);
  padding: 10px;
}

</style>
