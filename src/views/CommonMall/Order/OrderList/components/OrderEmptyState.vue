<!--
/**
 * 订单列表空状态组件
 *
 * 主要功能：
 * 1. 展示订单列表为空时的占位状态，提供友好的用户体验
 * 2. 使用图形化方式告知用户当前没有订单数据
 * 3. 提供统一的空状态视觉设计，保持界面一致性
 * 4. 支持响应式布局，适配不同屏幕尺寸的显示效果
 * 5. 采用语义化HTML结构，提升可访问性
 * 6. 集成无障碍设计，为屏幕阅读器提供适当的描述信息
 *
 * 技术特点：
 * - 使用纯展示组件，无复杂逻辑处理
 * - 采用Flexbox布局实现居中对齐
 * - 集成图片懒加载和无障碍属性
 * - 使用模块化CSS类名组织样式
 *
 * 使用场景：
 * - 订单列表页面无数据时的占位显示
 * - 筛选条件下无匹配订单时的提示
 * - 用户首次使用系统时的引导界面
 */
-->

<template>
  <!-- 订单空状态主容器 -->
  <div class="order-empty-state">
    <!-- 空状态内容区域 -->
    <div class="order-empty-state__content">
      <!-- 空状态插图，提供视觉化的无数据提示 -->
      <img
        src="../../assets/no-data.png"
        alt="暂无订单"
        class="order-empty-state__image"
      />
    </div>
  </div>
</template>

<script setup>
// ==================== 订单空状态组件 ====================
// 该组件为纯展示组件，无需额外的逻辑处理
// 主要用于在订单列表为空时提供友好的用户界面反馈
</script>

<style scoped lang="less">
.order-empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 300px;

  &__content {
    text-align: center;
  }

  &__image {
    width: 160px;
    height: 140px;
    margin-bottom: 16px;
  }
}
</style>
