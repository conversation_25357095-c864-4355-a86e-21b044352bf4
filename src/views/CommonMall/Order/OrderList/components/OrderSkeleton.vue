<!--
/**
 * 订单列表骨架屏组件
 *
 * 主要功能：
 * 1. 在订单列表数据加载时显示骨架屏效果，提升用户体验
 * 2. 模拟订单卡片的完整布局结构，包括订单号、状态、商品信息、操作按钮等
 * 3. 提供流畅的加载动画效果，减少用户等待时的焦虑感
 * 4. 与真实订单卡片保持一致的尺寸和布局，确保无缝切换
 * 5. 采用语义化HTML结构，使用header、section、footer标签组织内容
 *
 * 技术特点：
 * - 使用CSS动画实现流畅的骨架屏效果
 * - 采用渐变背景模拟内容加载状态
 * - 响应式设计，适配不同屏幕尺寸
 * - 无需JavaScript逻辑，纯CSS实现
 * - 模块化设计，便于维护和扩展
 *
 * 使用场景：
 * - 订单列表页面数据加载时的占位显示
 * - 任何需要订单卡片加载状态的页面
 */
-->

<template>
  <!-- 订单列表骨架屏主容器 -->
  <div class="order-skeleton">
    <!-- 循环生成3个订单卡片骨架 -->
    <div v-for="i in 3" :key="`skeleton-${i}`" class="order-skeleton__item">
      <!-- 订单卡片容器 -->
      <WoCard>
        <!-- 订单内容骨架 -->
        <div class="order-skeleton__content">
          <!-- 订单头部信息骨架 -->
          <header class="order-skeleton__header">
            <!-- 订单号骨架线 -->
            <div class="order-skeleton__order-number"></div>
            <!-- 订单状态骨架线 -->
            <div class="order-skeleton__status"></div>
          </header>

          <!-- 商品信息区域骨架 -->
          <section class="order-skeleton__goods">
            <!-- 商品图片骨架 -->
            <div class="order-skeleton__image"></div>
            <!-- 商品详情骨架 -->
            <div class="order-skeleton__details">
              <!-- 商品标题骨架线 -->
              <div class="order-skeleton__title"></div>
              <!-- 商品副标题骨架线 -->
              <div class="order-skeleton__subtitle"></div>
              <!-- 商品价格骨架线 -->
              <div class="order-skeleton__price"></div>
            </div>
          </section>

          <!-- 操作按钮区域骨架 -->
          <footer class="order-skeleton__actions">
            <!-- 操作按钮骨架 -->
            <div class="order-skeleton__button"></div>
            <div class="order-skeleton__button"></div>
          </footer>
        </div>
      </WoCard>
    </div>
  </div>
</template>

<script setup>
import WoCard from '@components/WoElementCom/WoCard.vue'

// ==================== 订单列表骨架屏组件 ====================
// 纯展示组件，无需任何逻辑处理
</script>

<style scoped lang="less">
.order-skeleton {
  &__item {
    margin-bottom: 10px;
  }

  &__content {
    padding: 0;
  }

  &__header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 11px;
  }

  &__order-number {
    width: 120px;
    height: 12px;
    background: #f0f0f0;
    border-radius: 6px;
    animation: skeleton-loading 1.5s ease-in-out infinite;
  }

  &__status {
    width: 60px;
    height: 16px;
    background: #f0f0f0;
    border-radius: 8px;
    animation: skeleton-loading 1.5s ease-in-out infinite;
  }

  &__goods {
    display: flex;
    align-items: flex-start;
    margin-bottom: 15px;
  }

  &__image {
    width: 75px;
    height: 75px;
    background: #f0f0f0;
    border-radius: 8px;
    margin-right: 12px;
    flex-shrink: 0;
    animation: skeleton-loading 1.5s ease-in-out infinite;
  }

  &__details {
    flex: 1;
    min-height: 75px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
  }

  &__title {
    width: 80%;
    height: 16px;
    background: #f0f0f0;
    border-radius: 8px;
    margin-bottom: 8px;
    animation: skeleton-loading 1.5s ease-in-out infinite;
  }

  &__subtitle {
    width: 60%;
    height: 12px;
    background: #f0f0f0;
    border-radius: 6px;
    margin-bottom: 8px;
    animation: skeleton-loading 1.5s ease-in-out infinite;
  }

  &__price {
    width: 40%;
    height: 14px;
    background: #f0f0f0;
    border-radius: 6px;
    animation: skeleton-loading 1.5s ease-in-out infinite;
  }

  &__actions {
    display: flex;
    justify-content: flex-end;
    gap: 8px;
  }

  &__button {
    width: 60px;
    height: 28px;
    background: #f0f0f0;
    border-radius: 4px;
    animation: skeleton-loading 1.5s ease-in-out infinite;
  }
}

@keyframes skeleton-loading {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.6;
  }
  100% {
    opacity: 1;
  }
}
</style>
