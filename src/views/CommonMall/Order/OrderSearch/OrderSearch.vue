<!--
/**
 * 订单搜索结果页面组件
 *
 * 主要功能：
 * 1. 展示订单搜索结果列表，支持按关键词搜索订单
 * 2. 集成无限滚动加载，分页展示搜索结果
 * 3. 提供完整的订单操作功能，包括查看详情、复制订单号等
 * 4. 支持订单状态倒计时显示，实时更新待付款订单状态
 * 5. 集成订单操作按钮，支持取消、支付、申请售后等操作
 * 6. 实现滚动位置记忆，页面切换时保持浏览位置
 *
 * 搜索功能：
 * - 支持商品名称、订单号等关键词搜索
 * - 实时更新搜索结果
 * - 支持搜索历史记录
 *
 * 技术特点：
 * - 使用组合式API实现功能模块化
 * - 集成多个自定义composables提升代码复用性
 * - 支持骨架屏和空状态提升用户体验
 * - 采用响应式设计，适配不同设备
 *
 * 使用场景：
 * - 用户搜索特定订单时的结果展示
 * - 从搜索入口页面跳转而来的搜索结果
 */
-->

<template>
  <!-- 订单搜索结果页面主容器 -->
  <div class="order-search">
    <!-- 搜索头部组件 -->
    <!-- 提供搜索输入框和搜索功能 -->
    <SearchHeader
      v-model="searchKeyword"
      placeholder="搜索我的订单"
      @search="handleSearch"
      class="order-search__header"
    />

    <!-- 搜索结果内容区域 -->
    <div class="order-search__content" ref="contentRef">
      <!-- 搜索结果加载骨架屏 -->
      <OrderSearchSkeleton v-if="showSkeleton" />

      <!-- 搜索结果空状态 -->
      <OrderSearchEmpty v-if="!showSkeleton && !loading && orderList.length === 0 && finished" />

      <!-- 搜索结果列表，支持无限滚动加载 -->
      <van-list
        v-if="!showSkeleton"
        v-model:loading="loading"
        :finished="finished"
        :finished-text="orderList.length > 0 ? '没有更多了' : ''"
        @load="loadOrderList"
        :immediate-check="false"
      >
        <!-- 遍历显示每个搜索到的订单 -->
        <CommonOrderItem
          v-for="order in orderList"
          :key="order.id"
          :order="order"
          :visible-buttons="getVisibleButtonsForTemplate(order)"
          :more-actions="getMoreActionsForTemplate(order)"
          @detail-click="goToOrderDetail"
          @copy-order="copyOrderNumber"
        />
      </van-list>
    </div>
  </div>
</template>
<script setup>
import { ref, onMounted, onUnmounted, nextTick, onActivated, onDeactivated } from 'vue'
import { useRoute, useRouter } from 'vue-router'

import SearchHeader from "@components/Common/SearchHeader.vue"
import CommonOrderItem from '../components/CommonOrderItem.vue'
import OrderSearchSkeleton from './components/OrderSearchSkeleton.vue'
import OrderSearchEmpty from './components/OrderSearchEmpty.vue'

import { useOrderList } from '../composables/useOrderList.js'
import { useOrderActions } from '../composables/useOrderActions.js'
import { useOrderButtons } from '../composables/useOrderButtons.js'
import { useOrderCountdown } from '../composables/useOrderCountdown.js'

// ==================== 核心依赖实例 ====================
// 当前路由信息实例
const route = useRoute()
// 路由导航实例
const router = useRouter()

// ==================== 搜索状态管理 ====================
// 搜索关键词
const searchKeyword = ref('')

// ==================== 订单列表功能集成 ====================
// 使用订单列表组合式函数，配置为搜索模式
const {
  loading,           // 加载状态
  finished,          // 是否加载完成
  orderList,         // 订单列表数据
  showSkeleton,      // 显示骨架屏状态
  contentRef,        // 内容区域DOM引用
  scrollPositions,   // 滚动位置缓存
  loadOrderList,     // 加载订单列表函数
  debouncedSearch,   // 防抖搜索函数
  handleScroll,      // 滚动事件处理函数
  removeOrderItem    // 移除订单项函数
} = useOrderList({
  isSearch: true,    // 启用搜索模式
  searchKeyword      // 传入搜索关键词
})

// ==================== 订单操作功能集成 ====================
// 使用通用的订单操作逻辑
const orderActions = useOrderActions()

// 使用通用的订单按钮逻辑
const { getVisibleButtons, getMoreActions } = useOrderButtons()

// 使用通用的倒计时逻辑
const { startCountdown, clearAllCountdowns } = useOrderCountdown()

// ==================== 订单操作配置 ====================
// 创建订单操作配置，集成成功回调
const createActionConfig = () => ({
  ...orderActions,
  onSuccess: () => {
    removeOrderItem  // 操作成功后移除订单项
  }
})

// 订单操作配置实例
const actionConfig = createActionConfig()

// ==================== 按钮配置功能 ====================
// 获取带操作配置的可见按钮
const getVisibleButtonsWithActions = (order) => {
  return getVisibleButtons(order, actionConfig)
}

// 获取带操作配置的更多操作
const getMoreActionsWithActions = (order) => {
  return getMoreActions(order, actionConfig)
}

// ==================== 搜索功能 ====================
// 执行搜索操作
const performSearch = () => {
  if (!searchKeyword.value) {
    return
  }

  // 更新路由查询参数
  const newQuery = {
    ...route.query,
    keyword: searchKeyword.value
  }

  router.replace({
    path: route.path,
    query: newQuery
  })

  // 清除所有倒计时并执行搜索
  clearAllCountdowns()
  debouncedSearch(searchKeyword.value)
}

// 搜索事件处理函数
const handleSearch = performSearch

// ==================== 倒计时功能 ====================
// 初始化订单倒计时
const initCountdowns = (orders) => {
  orders.forEach(order => {
    // 仅为待付款订单启动倒计时
    if (order.orderState === '0' && order.createTime) {
      startCountdown(order)
    }
  })
}

// ==================== 模板使用的功能函数 ====================
// 从订单操作中解构常用函数
const { copyOrderNumber, goToOrderDetail } = orderActions

// 模板中使用的按钮配置函数
const getVisibleButtonsForTemplate = (order) => getVisibleButtonsWithActions(order)
const getMoreActionsForTemplate = (order) => getMoreActionsWithActions(order)


// ==================== 生命周期管理 ====================
// 组件挂载时初始化搜索和滚动监听
onMounted(() => {
  // 从路由查询参数获取搜索关键词
  const keyword = route.query.keyword
  if (keyword) {
    searchKeyword.value = keyword
  }

  // 添加滚动事件监听器
  if (contentRef.value) {
    contentRef.value.addEventListener('scroll', handleScroll)
  } else {
    window.addEventListener('scroll', handleScroll)
  }

  // 如果有搜索关键词，立即加载搜索结果
  if (keyword) {
    loadOrderList().then(orders => {
      if (orders) {
        // 为搜索结果中的待付款订单初始化倒计时
        initCountdowns(orders)
      }
    })
  }
})

// 组件卸载时清理滚动监听器和倒计时
onUnmounted(() => {
  if (contentRef.value) {
    contentRef.value.removeEventListener('scroll', handleScroll)
  } else {
    window.removeEventListener('scroll', handleScroll)
  }
  // 清除所有倒计时定时器
  clearAllCountdowns()
})

// 页面激活时恢复滚动位置
onActivated(() => {
  nextTick(() => {
    if (contentRef.value && scrollPositions.value.all > 0) {
      contentRef.value.scrollTop = scrollPositions.value.all
    }
  })
})

// 页面失活时保存滚动位置
onDeactivated(() => {
  if (contentRef.value) {
    scrollPositions.value.all = contentRef.value.scrollTop
  }
})
</script>

<style scoped lang="less">
.order-search {
  height: 100vh;
  display: flex;
  flex-direction: column;

  &__header {
    flex-shrink: 0;
  }

  &__content {
    flex: 1;
    overflow: auto;
    background-color: #F8F9FA;
    padding: 10px;
    -webkit-overflow-scrolling: touch; scrollbar-width: none; -ms-overflow-style: none; &::-webkit-scrollbar { display: none; };
  }
}
</style>
