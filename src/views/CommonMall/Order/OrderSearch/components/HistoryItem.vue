<!--
/**
 * 搜索历史项组件
 *
 * 主要功能：
 * 1. 展示单个搜索历史关键词，提供快速重新搜索的功能
 * 2. 支持点击事件处理，触发历史关键词的重新搜索
 * 3. 提供友好的交互反馈，包括悬停和点击效果
 * 4. 采用按钮语义化设计，提升可访问性和键盘导航支持
 * 5. 集成文本溢出处理，确保长关键词的正确显示
 * 6. 使用响应式设计，适配不同屏幕尺寸的显示效果
 *
 * 技术特点：
 * - 使用button元素提供原生键盘支持
 * - 采用CSS过渡效果增强用户体验
 * - 集成文本截断处理长内容
 * - 使用toRefs保持props响应性
 *
 * 使用场景：
 * - 搜索页面的历史记录展示
 * - 快速搜索功能的实现
 * - 用户搜索行为的便捷重复操作
 */
-->

<template>
  <!-- 搜索历史项按钮 -->
  <!-- 使用button元素提供原生的键盘导航和无障碍支持 -->
  <button
    type="button"
    class="history-item"
    @click="handleClick"
  >
    <!-- 历史关键词文本显示 -->
    <!-- 支持文本溢出截断，确保长关键词的正确显示 -->
    <span class="history-item__text">{{ keyword }}</span>
  </button>
</template>

<script setup>
import { toRefs } from 'vue'

// ==================== 组件属性定义 ====================
// 定义组件接收的props参数
const props = defineProps({
  // 搜索关键词，必填参数，用于显示和重新搜索
  keyword: {
    type: String,
    required: true
  }
})

// 使用toRefs解构props，保持响应性
const { keyword } = toRefs(props)

// 定义组件向父组件发射的事件
const emit = defineEmits(['click'])

// ==================== 事件处理函数 ====================
// 历史项点击处理函数
// 当用户点击历史关键词时触发，向父组件发射click事件并传递关键词
const handleClick = () => {
  emit('click', keyword.value)
}
</script>

<style scoped lang="less">
.history-item {
  display: inline-flex;
  align-items: center;
  padding: 6px 12px;
  background-color: #F8F9FA;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s ease;
  max-width: 200px;

  &:hover {
    opacity: 0.7;
  }

  &:active {
    transform: scale(0.98);
  }

  &__text {
    font-size: 13px;
    color: #4A5568;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}
</style>
