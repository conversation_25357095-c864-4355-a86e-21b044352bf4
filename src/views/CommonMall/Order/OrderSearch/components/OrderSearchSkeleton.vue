<!--
/**
 * 订单搜索骨架屏组件
 *
 * 主要功能：
 * 1. 在搜索结果数据加载时显示骨架屏效果，提升用户体验
 * 2. 模拟搜索结果订单卡片的完整布局结构，包括订单号、状态、商品信息、操作按钮等
 * 3. 提供流畅的加载动画效果，减少用户等待时的焦虑感
 * 4. 与真实搜索结果卡片保持一致的尺寸和布局，确保无缝切换
 * 5. 采用语义化HTML结构，使用article、header、section、footer标签组织内容
 *
 * 技术特点：
 * - 使用CSS动画实现流畅的骨架屏效果
 * - 采用渐变背景模拟内容加载状态
 * - 响应式设计，适配不同屏幕尺寸
 * - 无需JavaScript逻辑，纯CSS实现
 * - 模块化设计，便于维护和扩展
 *
 * 使用场景：
 * - 订单搜索页面数据加载时的占位显示
 * - 任何需要搜索结果加载状态的页面
 */
-->

<template>
  <!-- 订单搜索骨架屏主容器 -->
  <div class="order-search-skeleton">
    <!-- 循环生成3个搜索结果骨架项 -->
    <article
      v-for="i in 3"
      :key="`skeleton-${i}`"
      class="order-search-skeleton__item"
    >
      <!-- 订单卡片容器 -->
      <WoCard>
        <!-- 订单内容骨架 -->
        <div class="order-search-skeleton__content">
          <!-- 订单头部信息骨架 -->
          <header class="order-search-skeleton__header">
            <!-- 订单号骨架线 -->
            <div class="order-search-skeleton__number"></div>
            <!-- 订单状态骨架线 -->
            <div class="order-search-skeleton__status"></div>
          </header>

          <!-- 商品信息区域骨架 -->
          <section class="order-search-skeleton__goods">
            <!-- 商品图片骨架 -->
            <div class="order-search-skeleton__image"></div>
            <!-- 商品信息骨架 -->
            <div class="order-search-skeleton__info">
              <!-- 商品标题骨架线 -->
              <div class="order-search-skeleton__title"></div>
              <!-- 商品副标题骨架线 -->
              <div class="order-search-skeleton__subtitle"></div>
              <!-- 商品价格骨架线 -->
              <div class="order-search-skeleton__price"></div>
            </div>
          </section>

          <!-- 操作按钮区域骨架 -->
          <footer class="order-search-skeleton__actions">
            <!-- 操作按钮骨架 -->
            <div class="order-search-skeleton__button"></div>
            <div class="order-search-skeleton__button"></div>
          </footer>
        </div>
      </WoCard>
    </article>
  </div>
</template>

<script setup>
import WoCard from '@components/WoElementCom/WoCard.vue'

// ==================== 订单搜索骨架屏组件 ====================
// 纯展示组件，无需任何逻辑处理
</script>

<style scoped lang="less">
.order-search-skeleton {
  &__item {
    margin-bottom: 10px;
  }

  &__content {
    padding: 0;
  }

  &__header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 11px;
  }

  &__number {
    width: 120px;
    height: 12px;
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: skeleton-loading 1.5s infinite;
    border-radius: 4px;
  }

  &__status {
    width: 60px;
    height: 16px;
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: skeleton-loading 1.5s infinite;
    border-radius: 4px;
  }

  &__goods {
    display: flex;
    margin-bottom: 15px;
  }

  &__image {
    width: 75px;
    height: 75px;
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: skeleton-loading 1.5s infinite;
    border-radius: 8px;
    margin-right: 12px;
    flex-shrink: 0;
  }

  &__info {
    flex: 1;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
  }

  &__title {
    width: 80%;
    height: 16px;
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: skeleton-loading 1.5s infinite;
    border-radius: 4px4;
    margin-bottom: 8px;
  }

  &__subtitle {
    width: 60%;
    height: 14px;
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: skeleton-loading 1.5s infinite;
    border-radius: 4px;
    margin-bottom: 8px;
  }

  &__price {
    width: 40%;
    height: 16px;
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: skeleton-loading 1.5s infinite;
    border-radius: 4px;
  }

  &__actions {
    display: flex;
    justify-content: flex-end;
    gap: 8px;
  }

  &__button {
    width: 60px;
    height: 28px;
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: skeleton-loading 1.5s infinite;
    border-radius: 4px;
  }
}

@keyframes skeleton-loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}
</style>
