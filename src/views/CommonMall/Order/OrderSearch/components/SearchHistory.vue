<!--
/**
 * 搜索历史组件
 *
 * 主要功能：
 * 1. 展示用户的搜索历史记录列表，提供快速重复搜索功能
 * 2. 支持搜索历史的使用，点击历史项可快速填入搜索关键词
 * 3. 提供清空所有历史记录的功能，支持一键清理
 * 4. 采用响应式布局，自动换行显示历史关键词
 * 5. 集成交互反馈，提供悬停和点击效果
 * 6. 使用语义化HTML结构，提升可访问性
 *
 * 技术特点：
 * - 使用section和header标签提供语义化结构
 * - 采用Flexbox布局实现响应式关键词排列
 * - 集成图片懒加载优化性能
 * - 使用toRefs保持props响应性
 * - 支持键盘导航和无障碍访问
 *
 * 使用场景：
 * - 搜索页面的历史记录展示
 * - 提升用户搜索体验的便捷功能
 * - 搜索行为的历史追踪和重用
 */
-->

<template>
  <!-- 搜索历史主容器 -->
  <section class="search-history">
    <!-- 搜索历史头部区域 -->
    <header class="search-history__header">
      <!-- 搜索历史标题 -->
      <h2 class="search-history__title">搜索历史</h2>
      <!-- 清空历史记录按钮 -->
      <button
        type="button"
        class="search-history__clear-btn"
        @click="handleClearAll"
        aria-label="清空搜索历史"
      >
        <!-- 清空图标，使用懒加载优化性能 -->
        <img
          src="../../../../../static/images/delete.png"
          alt="清空"
          class="search-history__clear-icon"
          loading="lazy"
        />
      </button>
    </header>
    <!-- 搜索历史列表区域 -->
    <div class="search-history__list">
      <!-- 遍历显示每个历史记录项 -->
      <HistoryItem
        v-for="(record, index) in records"
        :key="`history-${index}`"
        :keyword="record"
        @click="handleUseKeyword"
      />
    </div>
  </section>
</template>

<script setup>
import HistoryItem from './HistoryItem.vue'
import { toRefs } from 'vue'

// ==================== 组件属性定义 ====================
// 定义组件接收的props参数
const props = defineProps({
  // 搜索历史记录数组，包含用户的历史搜索关键词
  records: {
    type: Array,
    default: () => []
  }
})

// 使用toRefs解构props，保持响应性
const { records } = toRefs(props)

// 定义组件向父组件发射的事件
const emit = defineEmits(['use-keyword', 'clear-all'])

// ==================== 事件处理函数 ====================
// 使用历史关键词处理函数
// 当用户点击历史项时触发，向父组件发射use-keyword事件
const handleUseKeyword = (keyword) => {
  emit('use-keyword', keyword)
}

// 清空所有历史记录处理函数
// 当用户点击清空按钮时触发，向父组件发射clear-all事件
const handleClearAll = () => {
  emit('clear-all')
}
</script>

<style scoped lang="less">
.search-history {
  padding: 10px;

  &__header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
  }

  &__title {
    font-size: 15px;
    font-weight: 500;
    color: #171E24;
    margin: 0;
  }

  &__clear-btn {
    display: flex;
    align-items: center;
    background: none;
    border: none;
    cursor: pointer;
    padding: 4px;
    border-radius: 2px;
    transition: opacity 0.2s ease;

    &:hover {
      opacity: 0.7;
    }

    &:active {
      transform: scale(0.95);
    }
  }

  &__clear-icon {
    width: 16px;
    height: 16px;
  }

  &__list {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
  }
}
</style>
