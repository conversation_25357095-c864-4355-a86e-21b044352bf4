<!--
/**
 * 订单搜索空状态组件
 *
 * 主要功能：
 * 1. 在搜索结果为空时显示友好的空状态提示
 * 2. 提供视觉化的无数据反馈，避免空白页面
 * 3. 使用图标和布局设计提升用户体验
 * 4. 支持响应式设计，适配不同屏幕尺寸
 * 5. 采用居中布局，确保内容在页面中的合理位置
 *
 * 技术特点：
 * - 使用纯展示组件，无复杂逻辑处理
 * - 采用Flexbox布局实现居中对齐
 * - 集成图片懒加载和无障碍属性
 * - 使用模块化CSS类名组织样式
 *
 * 使用场景：
 * - 订单搜索页面无匹配结果时的占位显示
 * - 搜索关键词无对应订单时的友好提示
 * - 提升搜索功能的用户体验
 */
-->

<template>
  <!-- 订单搜索空状态主容器 -->
  <div class="order-search-empty">
    <!-- 空状态内容区域 -->
    <div class="order-search-empty__content">
      <!-- 空状态插图，提供视觉化的无数据提示 -->
      <img
        src="../../assets/no-data.png"
        alt="暂无订单"
        class="order-search-empty__image"
      />
    </div>
  </div>
</template>

<script setup>
// ==================== 订单搜索空状态组件 ====================
// 纯展示组件，无需任何逻辑处理
</script>

<style scoped lang="less">
.order-search-empty {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 300px;
  padding: 40px 20px;

  &__content {
    text-align: center;
  }

  &__image {
    width: 120px;
    height: 120px;
    margin-bottom: 16px;
  }
}
</style>
