<!--
==================== 用户帮助中心组件 ====================
主要功能：
1. 展示常见问题列表，采用折叠面板形式
2. 支持问题内容的HTML渲染，包含富文本格式
3. 提供用户友好的问答交互体验
4. 响应式设计适配不同屏幕尺寸
5. 优化的折叠动画和视觉效果

技术特性：
- 使用 shallowRef 优化大数据性能
- van-collapse 组件实现折叠交互
- 支持HTML内容安全渲染
- 结构化数据管理
- CSS深度选择器样式定制

使用场景：
- 用户查看常见问题解答
- 帮助用户快速找到解决方案
- 减少客服咨询压力
-->
<template>
  <div class="help-page">
    <!-- 帮助页面标题 -->
    <h2 class="help-title">猜你想问</h2>
    <!-- 折叠面板容器 - 展示问题列表 -->
    <van-collapse v-model="activeNames">
      <!-- 单个问题项 - 支持展开收起交互 -->
      <van-collapse-item v-for="item in helpList" :key="item.id" :name="item.id" :title="item.title">
        <!-- 问题答案内容 - 支持HTML富文本渲染 -->
        <div class="help-content" v-html="item.content"></div>
      </van-collapse-item>
    </van-collapse>
  </div>
</template>

<script setup>
// ==================== 依赖导入区域 ====================
import { ref, shallowRef } from 'vue'

// ==================== 帮助问题数据配置 ====================
// 帮助问题列表 - 使用shallowRef优化大数组性能，数据结构化并添加唯一ID
const helpList = shallowRef([
  {
    id: 'help-1',
    title: '1、什么是沃钱包-帮扶商城？',
    content: '答：为推进助农工作，扩大联通集团所扶持贫困县的农产品销售，沃钱包商城利用现有商城系统及运营体系搭建了帮扶商城平台。沃钱包-帮扶商城由沃钱包提供<strong>平台建设及运营、支付结算和售后服务</strong>，助农供应商提供商品供应和物流配送服务。'
  },
  {
    id: 'help-2',
    title: '2、帮扶商城的商品售价如何确定？',
    content: '答：沃钱包-帮扶商城的商品售价，为各助农供应商提供给本渠道的统一售价，<strong>零加价直供</strong>让利给消费者和农户。'
  },
  {
    id: 'help-3',
    title: '3、帮扶商城的货源是哪里？',
    content: '答：沃钱包-帮扶商城的所有商品，均由助农供应商提供，确保<strong>农田牧场直供</strong>。目前已接入的有沽源县伯启通电子商务有限公司、康保县草原牧场肉业特产经销有限公司、康保县农产品电子商务有限公司等供应商。'
  },
  {
    id: 'help-4',
    title: '4、帮扶商城的运费如何收取？',
    content: '答：帮扶商城的所有订单<strong>一律包邮</strong>，不收取发货运费，不限订单金额，不限所有支持的送货区域。'
  },
  {
    id: 'help-5',
    title: '5、能使用电子券购买帮扶商城商品吗？',
    content: '答：帮扶商城支持使用<strong>电子券支付、快捷/余额支付及电子券组合支付</strong>。您在帮扶商城提交订单后，在付款页面可选择电子券进行支付抵扣，即可使用电子券购买帮扶商城商品。'
  },
  {
    id: 'help-6',
    title: '6、没有沃钱包电子券或者电子券不足，还能下单吗？',
    content: '答：可以。沃钱包-帮扶商城支持<strong>电子券支付、快捷/余额支付及电子券组合支付</strong>等多种方式完成支付。如果没有沃钱包电子券，您可使用沃账户余额或银行卡快捷支付。'
  },
  {
    id: 'help-7',
    title: '7、如何查找我的沃钱包-帮扶商城订单？',
    content: '答：您在沃钱包的<strong>帮扶商城-我的-我的订单</strong>，可查看全部订单状态和订单明细信息（含物流信息）。'
  },
  {
    id: 'help-8',
    title: '8、支付成功后，如何查询订单物流信息？',
    content: '答：在沃钱包-帮扶商城的<strong>"我的-我的订单"</strong>，通过点击待收货或已完成的订单详情，查询订单的物流信息。如果您对配送有疑问，请联系沃钱包在线客服。'
  },
  {
    id: 'help-9',
    title: '9、支付成功后，订单多长时间会发货？',
    content: '答：<br>1）帮扶商城会在订单支付成功后的<strong>48小时内</strong>（以工作日计算）发货，如遇特殊天气、节假日、备货不足等因素可能延长发货时间。<br>2）包裹发出后，您可通过"帮扶商城-我的"订单详情页，查询物流信息。送达时间视快递配送时间而定，如遇不可抗力因素，送达时间可能略有延迟。'
  },
  {
    id: 'help-10',
    title: '10、在帮扶商城购买的商品是否支持退换货？如何申请退换货？',
    content: '答：通过在<strong>帮扶商城-我的订单</strong>中订单详情页里，选择需要退款的商品点击申请退款，我们会有专人审核。'
  },
  {
    id: 'help-11',
    title: '11、发生退款时，退款何时、以何种方式退回？',
    content: '答：当收到退回商品，确认满足退款条件，将为您发起退款。退款金额中的原电子券支付部分将相应恢复电子券额度，支持在有效期内继续使用，电子券退还时间为发起退款后的<strong>24小时</strong>。沃账户余额支付部分将在发起退款后的<strong>24小时内</strong>退回到您的付款沃账户。银行卡快捷支付部分将在<strong>3-7个工作日</strong>退回到您的银行卡，具体时间以银行为准。您可登录沃钱包-我的-账单查看退款情况。如您对退款有疑问，请联系沃钱包在线客服。'
  },
  {
    id: 'help-12',
    title: '12、各供应商支持的送货区域及退换货建议的快递公司有哪些？',
    content: '答：<br><strong>1）支持的送货区域：</strong>不支持香港、澳门、台湾、新疆、西藏、海南、青海地区。除上述区域外，支持全国配送并包邮。<br><strong>2）退换货建议的快递公司：</strong>生鲜类商品如需退换货，建议通过<strong>顺丰快递</strong>，通过其他快递公司退回的商品将不予受理；其他商品如需退换货，康保供应商提供的商品建议通过圆通、韵达，偏远地区通过平邮；沽源供应商提供的商品建议通过圆通、百世汇通。'
  },
  {
    id: 'help-13',
    title: '13、付款时提示交易受限或交易风险，该怎么办？',
    content: '答：沃钱包会根据每个用户的风险等级和实名情况提供不同的支付金额上限，并会根据预置的风控规则，拦截可疑交易，并提示交易风险。遇到上述问题，请联系<strong>沃钱包在线客服</strong>处理。'
  },
  {
    id: 'help-14',
    title: '14、支付成功后，订单依然处于"待付款"状态怎么办？',
    content: '答：这类情况一般是由系统未及时同步订单状态所致，请耐心等待，订单状态将延迟更新，请勿重复支付或自行取消订单。如果超过<strong>2小时</strong>依然处于"待付款"状态，请咨询沃钱包在线客服。'
  },
  {
    id: 'help-15',
    title: '15、支付成功的订单能否取消？',
    content: '答：如订单未付款，点击<strong>【帮扶商城】-【我的】-【待付款】</strong>自助取消订单。如订单已付款，选择需要退款的商品点击申请退款，我们会有专人审核。'
  }
])

// ==================== 折叠面板状态管理 ====================
// 折叠面板展开状态控制 - 用于管理哪些问题项处于展开状态
const activeNames = ref([])
</script>


<style lang="less" scoped>
.help-page {
  padding-bottom: 20px;

  .help-title {
    font-size: 16px;
    font-weight: 700;
    color: #171E24;
    padding: 16px 17px 12px;
    margin: 0;
    background: #fff;
    position: sticky;
    top: 0;
    z-index: 10;
    border-bottom: 1px solid #f5f5f5;
  }

  .help-content {
    line-height: 1.6;
    font-size: 13px;
    color: #718096;
    padding: 16px;

    // 优化 HTML 内容样式
    :deep(strong) {
      color: #171E24;
      font-weight: 600;
    }

    :deep(br) {
      line-height: 2;
    }
  }
}

// 优化折叠面板样式
:deep(.van-collapse) {
  background: #fff;

  .van-collapse-item {
    &:not(:last-child) {
      border-bottom: 1px solid #f5f5f5;
    }

    &__title {
      font-size: 14px;
      color: #171E24;
      line-height: 1.5;
      padding: 16px 17px;
      font-weight: 500;

      // 添加展开/收起动画
      transition: background-color 0.2s ease;

      &:active {
        background-color: #f8f8f8;
      }
    }

    &__content {
      padding: 0;
      background: #fafafa;
    }

    // 展开状态下的标题样式
    &--expanded .van-collapse-item__title {
      background-color: #f8f8f8;
    }
  }
}

// 响应式优化
@media (max-width: 375px) {
  .help-page {
    .help-title {
      padding: 12px 15px 10px;
      font-size: 15px;
    }

    .help-content {
      padding: 14px;
      font-size: 12px;
    }
  }

  :deep(.van-collapse-item__title) {
    padding: 14px 15px;
    font-size: 13px;
  }
}
</style>
