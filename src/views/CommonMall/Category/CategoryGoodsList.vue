<!--
/**
 * 分类商品列表页面组件
 *
 * 主要功能：
 * - 展示指定分类下的商品列表，支持分页加载
 * - 提供商品搜索、排序、筛选功能
 * - 支持瀑布流和列表两种布局模式切换
 * - 集成购物车功能，支持商品加购和跳转
 * - 支持地址切换和省分筛选（政企版本）
 * - 响应式设计，适配不同屏幕尺寸
 *
 * 核心特性：
 * - 动态头部高度计算，确保内容区域正确显示
 * - 政企版本和普通版本的差异化处理
 * - 商品库存过滤和数据处理
 * - 无限滚动加载更多商品
 * - 筛选条件持久化和重置功能
 *
 * 技术特点：
 * - 使用 Vue 3 Composition API
 * - 集成 useGoodsList 组合函数复用商品列表逻辑
 * - 异步组件加载优化性能
 * - 响应式布局配置
 * - 状态管理集成（用户信息、省分服务）
 *
 * 使用场景：
 * - 商城分类页面商品展示
 * - 政企采购平台商品浏览
 * - 移动端商品列表页面
 */
-->
<template>
  <div class="goods-list-page">
    <!-- 固定头部区域，包含筛选、搜索、排序等功能 -->
    <header ref="pageHeaderRef" class="page-header">
      <!-- 省分筛选组件，仅在政企版本特定角色下显示 -->
      <ProvinceFilter @confirm="handleProvinceFilterConfirm" />

      <!-- 搜索头部组件，包含搜索框和布局切换按钮 -->
      <SearchHeader v-model="searchKeyword" placeholder="搜索商品" :redirectToSearch="bizCode !== 'zq'"
        :redirectUrl="'/search'" @search="handleSearch" @clickable="handleSearchClick">
        <template #right-action>
          <!-- 布局切换按钮，仅在非政企版本显示 -->
          <button v-if="bizCode !== 'zq'" class="layout-toggle" @click="toggleLayout" type="button">
            <img :src="isWaterfallLayout ? switchLayout2Img : switchLayoutImg" alt="切换布局" width="20" height="20" />
          </button>
        </template>
      </SearchHeader>

      <!-- 排序和筛选工具栏，仅在非政企版本显示 -->
      <SortFilterBar v-if="bizCode !== 'zq'" :sort-type="sortType" :sort-order="sortOrder"
        :has-filter-conditions="hasFilterConditions" @sort-change="handleSortChange" @filter-toggle="toggleFilter" />
    </header>

    <!-- 主要内容区域，展示商品列表 -->
    <main class="goods-content">
      <!-- 商品列表布局组件，支持瀑布流和列表模式 -->
      <GoodsListLayout :goods-list="goodsList" :is-loading="isLoading" :loading="loading" :finished="finished"
        :is-waterfall="isWaterfallLayout" :breakpoints="breakpoints" empty-description="本地区无货" @load-more="onLoad"
        @item-click="goToDetail" @add-cart="addOneCart" @update:loading="(val) => loading = val" />
    </main>

    <!-- 浮动购物车气泡，根据业务代码和角色类型控制显示 -->
    <FloatingBubble :offset="floatingBubbleOffset" @go-to-cart="goToCart"
      :is-show-cart="bizCode !== 'zq' || (bizCode === 'zq' && roleType !== '2')" />

    <!-- 筛选弹窗，仅在非政企版本显示 -->
    <FilterPopup v-if="bizCode !== 'zq'" v-model:show="isPopupShow" v-model="filterCriteria"
      :location-text="locationText" :category-id="categoryId" @switch-address="setSwitchAddressPopupShow"
      @confirm="handleFilterConfirm" @reset="handleFilterReset" />

    <!-- 地址切换弹窗，仅在非政企版本显示 -->
    <AddressSwitchPopup v-if="bizCode !== 'zq'" v-model:show="isSwitchAddressPopupShow"
      @address-changed="handleAddressChanged" />
  </div>
</template>

<script setup>
import { ref, onMounted, computed, nextTick, watch, defineAsyncComponent } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { filter, get } from 'lodash-es'
import { closeToast, showLoadingToast, showToast } from 'vant'
import SearchHeader from '@components/Common/SearchHeader.vue'
import AddressSwitchPopup from '@components/Common/FilterTools/AddressSwitchPopup.vue'
import FilterPopup from '@components/Common/FilterTools/FilterPopup.vue'
import FloatingBubble from '@components/Common/FloatingBubble.vue'
import SortFilterBar from '@components/Common/FilterTools/SortFilterBar.vue'
import GoodsListLayout from '@components/GoodsListCommon/GoodsListLayout.vue'
const ProvinceFilter = defineAsyncComponent(() => import('@components/ZQCommon/ProvinceFilter.vue'))
import { useGoodsList } from '@/composables/useGoodsList.js'
import { getBizCode } from '@utils/curEnv.js'
import { getCustomerManagerInfo, getEnterpriseManagerInfo, queryZqInfo } from '@utils/zqInfo.js'
import { getDefaultBreakpoints } from '@/config/responsive.js'
import { skuPageList, zqQuerySimplified } from '@api/index.js'
import { useUserStore } from '@store/modules/user.js'
import { useProvinceServiceStore } from '@store/modules/provinceService.js'
import switchLayoutImg from '@/static/images/switch-layout.png'
import switchLayout2Img from '@/static/images/switch-layout2.png'

// ===================== 路由和状态管理 =======================
const route = useRoute() // 当前路由信息，用于获取分类ID和查询参数
const router = useRouter() // 路由导航，用于页面跳转
const userStore = useUserStore() // 用户状态管理，处理用户信息和默认地址
const provinceServiceStore = useProvinceServiceStore() // 省分服务状态管理，处理政企版本的省分选择

// ===================== 业务配置和用户角色管理 =======================
const bizCode = getBizCode() // 获取当前业务代码，区分政企版本和普通版本
// 计算当前用户角色类型，用于控制不同角色的功能权限
const roleType = computed(() => {
  const { roleType = '' } = getEnterpriseManagerInfo() || getCustomerManagerInfo() || {}
  return roleType
})

// ===================== 商品列表核心逻辑复用 =======================
// 使用商品列表组合函数，复用商品列表的通用逻辑
const {
  goodsList, // 商品列表数据
  loading, // 加载更多状态
  finished, // 是否已加载完所有数据
  isLoading, // 初始加载状态
  pageNo, // 当前页码
  pageSize, // 每页数据量
  filterCriteria, // 筛选条件对象
  hasFilterConditions, // 是否有筛选条件
  locationText, // 当前位置文本
  addressInfo, // 地址信息
  resetList, // 重置列表方法
  processGoodsData, // 商品数据处理方法
  applyStockFilter, // 库存过滤方法
  goToCart, // 跳转购物车方法
  addOneCart, // 添加商品到购物车方法
  handleFilterReset, // 重置筛选条件方法
  handleAddressChanged // 地址变更处理方法
} = useGoodsList()

// ===================== 页面状态管理 =======================
const categoryId = ref('') // 当前分类ID，从路由参数获取
const searchKeyword = ref('') // 搜索关键词
const sortType = ref('sort') // 排序类型：sort(综合排序)、price(价格)、sale(销量)
const sortOrder = ref('desc') // 排序方向：asc(升序)、desc(降序)
const isWaterfallLayout = ref(false) // 是否使用瀑布流布局
const isPopupShow = ref(false) // 筛选弹窗显示状态
const isSwitchAddressPopupShow = ref(false) // 地址切换弹窗显示状态

// ===================== UI 布局和样式管理 =======================
const floatingBubbleOffset = ref({ bottom: 150 }) // 浮动购物车气泡位置偏移
const headerHeight = ref(0) // 头部实际高度，用于动态计算内容区域padding
const pageHeaderRef = ref(null) // 头部DOM元素引用
const breakpoints = ref(getDefaultBreakpoints()) // 瀑布流响应式断点配置

// 动态计算内容区域顶部padding，确保不被固定头部遮挡
const dynamicPaddingTop = computed(() => {
  return headerHeight.value >= 0 ? `${headerHeight.value}px` : '85px'
})

// ===================== 用户交互处理 =======================
// 切换商品列表布局模式（瀑布流/列表）
const toggleLayout = () => {
  isWaterfallLayout.value = !isWaterfallLayout.value
}

// 跳转到商品详情页面
const goToDetail = (item) => {
  const firstSku = get(item, 'skuList[0]', {}) // 获取商品的第一个SKU
  const { goodsId, skuId } = firstSku

  if (goodsId && skuId) {
    router.push(`/goodsdetail/${goodsId}/${skuId}`) // 跳转到商品详情页
  }
}

// 切换筛选弹窗显示状态
const toggleFilter = () => {
  isPopupShow.value = !isPopupShow.value
}

// 显示地址切换弹窗
const setSwitchAddressPopupShow = () => {
  isSwitchAddressPopupShow.value = true
}

// ===================== 搜索功能处理 =======================
// 处理搜索操作，跳转到搜索结果页面
const handleSearch = (keyWord) => {
  if (!keyWord) {
    showToast('请输入搜索内容')
    return
  }
  const timestamp = Date.parse(new Date()) // 时间戳，用于缓存控制
  const testDMX = get(route.query, 'testDMX', false) // 测试参数

  // 根据用户角色类型决定供应商和省分参数来源
  const zqInfo = queryZqInfo()
  let supplierCode = ''
  let proStr = ''

  if (roleType.value === '4') {
    // 角色类型为4时，从省分服务store获取数据
    supplierCode = provinceServiceStore.selectedIsvId || ''
    proStr = provinceServiceStore.selectedAreaId || ''
  } else {
    // 其他角色从政企信息获取数据
    supplierCode = zqInfo.isvList[0]?.isvId || ''
    proStr = zqInfo.provinceCode.join(',')
  }

  // 跳转到搜索结果页面，携带搜索参数
  router.push('/search/list?timestamp=' + timestamp + '&keyword=' + keyWord + '&testDMX=' + testDMX + '&supplierCode=' + supplierCode + '&proStr=' + proStr)
  searchKeyword.value = '' // 清空搜索框
}

// 处理搜索框点击事件
const handleSearchClick = () => {
  console.log('搜索框被点击，即将跳转到搜索页面')
}

// ===================== 排序和筛选处理 =======================
// 处理排序方式变更
const handleSortChange = ({ type, currentSortType, currentSortOrder }) => {
  if (currentSortType === type) {
    // 如果点击的是当前排序类型，切换排序方向
    if (type === 'price' || type === 'sale') {
      sortOrder.value = currentSortOrder === 'asc' ? 'desc' : 'asc'
    }
  } else {
    // 切换到新的排序类型
    sortType.value = type
    sortOrder.value = ''
  }

  reloadGoodsList() // 重新加载商品列表
}

// 处理筛选条件确认
const handleFilterConfirm = () => {
  reloadGoodsList() // 应用筛选条件并重新加载列表
}

// 处理省分筛选确认
const handleProvinceFilterConfirm = (selection) => {
  console.log('省分筛选选择:', selection)
  reloadGoodsList() // 重置列表并重新加载数据
}

// ===================== 数据加载和刷新管理 =======================
// 重新加载商品列表的通用方法
const reloadGoodsList = () => {
  resetList() // 重置列表状态
  fetchGoodsList() // 重新获取商品数据
}

// ===================== 商品数据获取和处理 =======================
// 获取商品列表数据的核心方法
const fetchGoodsList = async () => {
  if (pageNo.value === 1) {
    isLoading.value = true // 首页加载时显示骨架屏
  }

  // 提取已选择的品牌列表
  const brandList = filter(filterCriteria.value.brandsList, 'isSelected').map(item => item.value)

  const zqInfo = queryZqInfo()

  // 根据业务代码和用户角色决定API参数
  let supplierCode = ''
  let proStr = ''

  if(bizCode === 'zq') {
    if (roleType.value === '4') {
      // 角色类型为4时，从省分服务store获取数据
      supplierCode = provinceServiceStore.selectedIsvId || ''
      proStr = provinceServiceStore.selectedAreaId || ''
    } else {
      // 其他角色从政企信息获取数据
      supplierCode = zqInfo.isvList[0]?.isvId || ''
      proStr = zqInfo.provinceCode.join(',')
    }
  }

  showLoadingToast() // 显示加载提示

  let apiResult
  if (bizCode === 'zq') {
    // 政企版本使用简化查询接口
    const [zqErr, zqJson] = await zqQuerySimplified({
      bizCode: getBizCode('GOODS'),
      type: 1, // 查询类型：分类商品
      id: categoryId.value,
      pageNo: pageNo.value,
      pageSize: pageSize.value,
      roleType: roleType.value,
      supplierCode: supplierCode,
      proStr: proStr,
    })
    apiResult = { err: zqErr, json: zqJson }
  } else {
    // 普通版本使用SKU分页查询接口
    const [skuErr, skuJson] = await skuPageList({
      bizCode: getBizCode('GOODS'),
      categoryId: categoryId.value,
      pageNo: pageNo.value,
      pageSize: pageSize.value,
      type: sortType.value, // 排序类型
      sort: sortOrder.value, // 排序方向
      brandList: JSON.stringify(brandList), // 品牌筛选
      priceFrom: filterCriteria.value.minPrice !== '' ? Number(filterCriteria.value.minPrice * 100) : '', // 最低价格（分）
      priceTo: filterCriteria.value.maxPrice !== '' ? Number(filterCriteria.value.maxPrice * 100) : '', // 最高价格（分）
      addressInfo: addressInfo.value // 地址信息
    })
    apiResult = { err: skuErr, json: skuJson }
  }

  const { err, json } = apiResult

  closeToast() // 关闭加载提示

  if (pageNo.value === 1) {
    goodsList.value = [] // 首页加载时清空列表
  }

  loading.value = false
  isLoading.value = false

  if (!err) {
    // 根据业务代码选择数据字段：政企用户使用goodsList，普通用户使用skuList
    const dataList = bizCode === 'zq' ? json?.goodsList : json?.skuList

    if (json && dataList && dataList.length > 0) {
      const processedList = processGoodsData(dataList) // 处理商品数据格式
      const filteredList = applyStockFilter(processedList) // 应用库存过滤

      // 如果过滤后无数据且还有更多页，继续加载下一页
      if (filteredList.length <= 0 && json.cacheType === '1') {
        pageNo.value++
        fetchGoodsList()
        return
      }

      goodsList.value = goodsList.value.concat(filteredList) // 追加到商品列表

      if (json.cacheType === '1') {
        pageNo.value++ // 还有更多数据，页码递增
      } else {
        finished.value = true // 数据加载完毕
      }
    } else {
      // 无数据时的处理逻辑
      if (!json || (json && json.cacheType === '0')) {
        finished.value = true
        return
      }
      pageNo.value++
      fetchGoodsList() // 继续尝试加载下一页
    }
  } else {
    console.error('获取商品列表失败:', err.msg)
  }
}

// 无限滚动加载更多数据的回调
const onLoad = () => {
  fetchGoodsList()
}

// ===================== 头部高度动态计算 =======================
// 计算头部实际高度，确保内容区域不被遮挡
const calculateHeaderHeight = () => {
  nextTick(() => {
    if (pageHeaderRef.value) {
      // 优先使用DOM元素的实际高度
      headerHeight.value = pageHeaderRef.value.offsetHeight
    } else {
      // 备用计算方式：根据组件显示情况估算高度
      let totalHeight = 0

      // 省分筛选组件高度（仅在特定角色下显示）
      if (roleType.value === '4') {
        totalHeight += 44
      }

      // 搜索头部组件高度
      totalHeight += 44

      // 排序筛选工具栏高度（仅在非政企版本显示）
      if (bizCode !== 'zq') {
        totalHeight += 44
      }

      headerHeight.value = totalHeight
    }
  })
}

// ===================== 响应式监听和生命周期管理 =======================
// 监听业务代码和角色类型变化，重新计算头部高度
watch(() => [bizCode, roleType.value], () => {
  calculateHeaderHeight()
}, { flush: 'post' })

// 组件挂载时的初始化逻辑
onMounted(async () => {
  categoryId.value = route.params.id // 从路由参数获取分类ID

  if (route.query.keyword) {
    searchKeyword.value = route.query.keyword // 从查询参数获取搜索关键词
  }

  await userStore.queryDefaultAddr({ force: true }) // 强制刷新用户默认地址

  calculateHeaderHeight() // 计算头部高度

  fetchGoodsList() // 加载商品列表数据
})
</script>

<style scoped lang="less">
.goods-list-page {
  padding-top: v-bind(dynamicPaddingTop);

  .page-header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 999;

    .layout-toggle {
      margin-left: 12px;
      padding: 4px;
      background: none;
      border: none;
      cursor: pointer;

      img {
        width: 20px;
        height: 20px;
      }
    }
  }

  .goods-content {
    padding: 0 10px;
  }
}
</style>
